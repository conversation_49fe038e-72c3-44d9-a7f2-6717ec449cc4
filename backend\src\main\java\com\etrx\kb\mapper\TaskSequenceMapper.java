package com.etrx.kb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.etrx.kb.domain.TaskSequence;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 任务序列Mapper接口
 */
@Mapper
public interface TaskSequenceMapper extends BaseMapper<TaskSequence> {
    
    /**
     * 获取当前序列号
     * @param sequenceName 序列名称
     * @return 当前序列号
     */
    Long getNextSequence(@Param("sequenceName") String sequenceName);

    /**
     * 更新序列号
     * @param sequenceName 序列名称
     * @return 更新的行数
     */
    int updateSequence(@Param("sequenceName") String sequenceName,@Param("currentSequence") Long currentSequence);

}