package com.etrx.kb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.etrx.kb.common.Constants;
import com.etrx.kb.common.PageResult;
import com.etrx.kb.domain.ApiKey;
import com.etrx.kb.domain.KnowledgeBase;
import com.etrx.kb.dto.ApiKeyDTO;
import com.etrx.kb.exception.ApiException;
import com.etrx.kb.mapper.ApiKeyMapper;
import com.etrx.kb.mapper.KnowledgeBaseMapper;
import com.etrx.kb.service.ApiKeyService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * API密钥服务实现类
 */
@Service
@RequiredArgsConstructor
public class ApiKeyServiceImpl implements ApiKeyService {

    private final ApiKeyMapper apiKeyMapper;
    private final KnowledgeBaseMapper knowledgeBaseMapper;

    /**
     * 检查API密钥是否被知识库使用
     *
     * @param apiKey API密钥
     * @return 如果被使用返回true，否则返回false
     */
    private boolean isApiKeyUsedByKnowledgeBase(String apiKey) {
        LambdaQueryWrapper<KnowledgeBase> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(KnowledgeBase::getAppName, apiKey)
               .eq(KnowledgeBase::getDeleted, 0);
        return knowledgeBaseMapper.exists(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiKeyDTO createApiKey(String appName, Long creatorId) {
        // 检查应用名称是否已存在
        LambdaQueryWrapper<ApiKey> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ApiKey::getAppName, appName)
                .eq(ApiKey::getCreatorId, creatorId);
        if (apiKeyMapper.selectCount(queryWrapper) > 0) {
            throw new ApiException("该应用名称已存在");
        }

        // 生成API密钥
        String apiKey = UUID.randomUUID().toString().replace("-", "");

        // 创建API密钥记录
        ApiKey apiKeyEntity = new ApiKey();
        apiKeyEntity.setAppName(appName);
        apiKeyEntity.setApiKey(apiKey);
        apiKeyEntity.setStatus(Constants.ApiKeyStatus.ENABLED);
        apiKeyEntity.setCreatorId(creatorId);
        apiKeyEntity.setCreateTime(LocalDateTime.now());
        apiKeyEntity.setUpdateTime(LocalDateTime.now());

        apiKeyMapper.insert(apiKeyEntity);

        // 返回API密钥信息
        ApiKeyDTO dto = new ApiKeyDTO();
        BeanUtils.copyProperties(apiKeyEntity, dto);
        return dto;
    }

    @Override
    public ApiKeyDTO getApiKey(Long id) {
        ApiKey apiKey = apiKeyMapper.selectById(id);
        if (apiKey == null) {
            throw new ApiException("API密钥不存在");
        }

        ApiKeyDTO dto = new ApiKeyDTO();
        BeanUtils.copyProperties(apiKey, dto);
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteApiKey(Long id, Long operatorId) {
        // 获取API密钥信息
        ApiKey apiKey = apiKeyMapper.selectById(id);
        if (apiKey == null) {
            throw new ApiException("API密钥不存在");
        }

        // 检查是否有权限删除
        if (!apiKey.getCreatorId().equals(operatorId)) {
            throw new ApiException("没有权限删除该API密钥");
        }

        // 检查API密钥是否被知识库使用
        if (isApiKeyUsedByKnowledgeBase(apiKey.getAppName())) {
            throw new ApiException("该API密钥已被知识库使用，无法删除");
        }

        // 删除API密钥
        apiKeyMapper.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(Long id, Integer status, Long operatorId) {
        // 获取API密钥信息
        ApiKey apiKey = apiKeyMapper.selectById(id);
        if (apiKey == null) {
            throw new ApiException("API密钥不存在");
        }

        // 检查是否有权限更新
        if (!apiKey.getCreatorId().equals(operatorId)) {
            throw new ApiException("没有权限更新该API密钥");
        }

        // 如果要禁用API密钥，检查是否被知识库使用
        if (status == Constants.ApiKeyStatus.DISABLED && isApiKeyUsedByKnowledgeBase(apiKey.getAppName())) {
            throw new ApiException("该API密钥已被知识库使用，无法禁用");
        }

        // 更新状态
        apiKey.setStatus(status);
        apiKey.setUpdateTime(LocalDateTime.now());
        apiKeyMapper.updateById(apiKey);
    }

    @Override
    public PageResult<ApiKeyDTO> listApiKeys(Integer pageNum, Integer pageSize, String keyword, Long userId) {
        // 构建查询条件
        LambdaQueryWrapper<ApiKey> queryWrapper = new LambdaQueryWrapper<>();

        if (keyword != null && !keyword.isEmpty()) {
            queryWrapper.like(ApiKey::getAppName, keyword);
        }

        // 分页查询
        IPage<ApiKey> page = apiKeyMapper.selectPage(new Page<>(pageNum, pageSize), queryWrapper);

        // 转换为DTO
        List<ApiKeyDTO> records = page.getRecords().stream()
                .map(entity -> {
                    ApiKeyDTO dto = new ApiKeyDTO();
                    BeanUtils.copyProperties(entity, dto);
                    // 出于安全考虑，不返回实际的API密钥值
                    dto.setApiKey(null);
                    return dto;
                })
                .collect(Collectors.toList());

        // 构建分页结果
        PageResult<ApiKeyDTO> result = new PageResult<>();
        result.setPageNum(page.getCurrent());
        result.setPageSize(page.getSize());
        result.setTotal(page.getTotal());
        result.setTotalPage(page.getPages());
        result.setList(records);
        return result;
    }

    @Override
    public boolean validateApiKey(String apiKey) {
        ApiKey key = apiKeyMapper.findByApiKey(apiKey);
        return key != null && key.getStatus() == Constants.ApiKeyStatus.ENABLED;
    }

    @Override
    public List<ApiKeyDTO> getUserApiKeys(Long userId) {
        return apiKeyMapper.selectList(new LambdaQueryWrapper<ApiKey>()
                        .eq(ApiKey::getCreatorId, userId))
                .stream()
                .map(apiKey -> {
                    ApiKeyDTO dto = new ApiKeyDTO();
                    dto.setId(apiKey.getId());
                    dto.setAppName(apiKey.getAppName());
                    dto.setStatus(apiKey.getStatus());
                    return dto;
                })
                .collect(Collectors.toList());
    }

    @Override
    public String getAppNameByApiKey(String apiKey) {
        ApiKey key = apiKeyMapper.findByApiKey(apiKey);
        return key != null ? key.getAppName() : "Unknown";
    }

    @Override
    public Long getAppIdByApiKey(String apiKey) {
        ApiKey key = apiKeyMapper.findByApiKey(apiKey);
        return key != null ? key.getId() : null;
    }

    @Override
    public ApiKeyDTO getAppInfoByApiKey(String apiKey) {
        ApiKey key = apiKeyMapper.findByApiKey(apiKey);
        if (key == null) {
            return null;
        }
        
        ApiKeyDTO dto = new ApiKeyDTO();
        dto.setId(key.getId());
        dto.setAppName(key.getAppName());
        dto.setStatus(key.getStatus());
        dto.setCreateTime(key.getCreateTime());
        return dto;
    }
}