package com.etrx.kb.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 任务执行记录实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("tb_task_execution_record")
public class TaskExecutionRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务队列ID（来自定时任务扫描）
     */
    private Long taskQueueId;

    /**
     * 任务定义ID
     */
    private Long taskDefinitionId;

    /**
     * 业务数据（JSON格式）
     */
    private String businessData;

    /**
     * 执行来源(1:定时任务扫描,2:业务直接触发)
     */
    private Integer executionSource;

    /**
     * 执行状态(0:待执行,1:执行中,2:执行成功,3:执行失败,4:已取消)
     */
    private Integer executionStatus;

    /**
     * 开始执行时间
     */
    private LocalDateTime startTime;

    /**
     * 结束执行时间
     */
    private LocalDateTime endTime;

    /**
     * 执行时长（毫秒）
     */
    private Long executionDuration;

    /**
     * 执行结果数据
     */
    private String resultData;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 执行线程名称
     */
    private String threadName;

    /**
     * 执行主机名
     */
    private String hostName;

    /**
     * 执行主机IP
     */
    private String hostIp;

    /**
     * 创建者ID
     */
    private Long createdBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 任务定义对象（不映射到数据库）
     */
    @TableField(exist = false)
    private TaskDefinition taskDefinition;

    /**
     * 执行来源枚举
     */
    public static class ExecutionSource {
        public static final int SCHEDULED_SCAN = 1; // 定时任务扫描
        public static final int BUSINESS_TRIGGER = 2; // 业务直接触发
    }

    /**
     * 执行状态枚举
     */
    public static class ExecutionStatus {
        public static final int PENDING = 0; // 待执行
        public static final int RUNNING = 1; // 执行中
        public static final int SUCCESS = 2; // 执行成功
        public static final int FAILED = 3; // 执行失败
        public static final int CANCELLED = 4; // 已取消
    }
} 