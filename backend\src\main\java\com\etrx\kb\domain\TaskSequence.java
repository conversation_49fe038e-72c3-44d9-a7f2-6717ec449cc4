package com.etrx.kb.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 任务序列实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("tb_task_sequence")
public class TaskSequence implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 序列名称
     */
    private String sequenceName;

    /**
     * 当前值
     */
    private Long currentValue;

    /**
     * 步长
     */
    private Integer step;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
} 