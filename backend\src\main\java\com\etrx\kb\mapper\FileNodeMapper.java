package com.etrx.kb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.etrx.kb.domain.FileNode;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Many;

import java.util.List;

@Mapper
public interface FileNodeMapper extends BaseMapper<FileNode> {
    
    @Select("""
            <script>
            <choose>
                <!-- 知识库根目录：合并知识库和非知识库查询结果 -->
                <when test='isKbRoot == true and kbId != null'>
                    <!-- 知识库内文件查询 -->
                    SELECT * FROM (
                        SELECT DISTINCT fn.id, fn.name, fn.full_path, fn.parent_id, fn.node_type,
                               fn.file_type, fn.file_size, fn.storage_path, fn.status,
                               fn.creator_id, fn.create_time, fn.update_time, fn.deleted, 
                               u.nickname as creator_name, #{kbId} as kbId
                        FROM tb_file_node fn
                        LEFT JOIN tb_user u ON fn.creator_id = u.id
                        LEFT JOIN tb_kb_file_rel rel ON fn.id = rel.file_node_id AND rel.deleted = 0
                        WHERE fn.parent_id = #{parentId}
                          AND fn.deleted = 0
                          AND (
                              (fn.node_type = 1) OR 
                              (fn.node_type = 2 AND rel.kb_id = #{kbId})
                          )
                          <if test='keyword != null and keyword != ""'>
                          AND fn.name LIKE CONCAT('%', #{keyword}, '%')
                          </if>
                        
                        UNION ALL
                        
                        <!-- 非知识库文件查询 -->
                        SELECT DISTINCT fh.id, fh.name, fh.full_path, fh.parent_id, fh.node_type,
                               fh.file_type, fh.file_size, fh.storage_path, fh.status,
                               fh.creator_id, fh.create_time, fh.update_time, fh.deleted, 
                               u.nickname as creator_name, #{kbId} as kbId
                        FROM (
                            WITH RECURSIVE file_hierarchy AS (
                                SELECT tn.id, tn.name, tn.full_path, tn.parent_id, tn.node_type, 
                                       tn.file_type, tn.file_size, tn.storage_path, tn.status,
                                       tn.creator_id, tn.create_time, tn.update_time, tn.deleted
                                FROM tb_file_node tn
                                JOIN tb_kb_file_rel rel ON tn.id = rel.file_node_id
                                WHERE rel.kb_id = #{kbId}
                                  AND rel.deleted = 0
                                  AND tn.deleted = 0
                                  AND tn.full_path NOT LIKE '/.knowledgebase%'
                                UNION ALL
                                SELECT p.id, p.name, p.full_path, p.parent_id, p.node_type,
                                       p.file_type, p.file_size, p.storage_path, p.status,
                                       p.creator_id, p.create_time, p.update_time, p.deleted
                                FROM tb_file_node p
                                JOIN file_hierarchy fh ON p.id = fh.parent_id
                                WHERE p.deleted = 0
                            )
                            SELECT * FROM file_hierarchy
                            WHERE 
                            parent_id is null
                              <if test='keyword != null and keyword != ""'>
                              AND name LIKE CONCAT('%', #{keyword}, '%')
                              </if>
                        ) fh
                        LEFT JOIN tb_user u ON fh.creator_id = u.id
                    ) combined_result
                    ORDER BY node_type ASC, create_time DESC
                    LIMIT #{offset}, #{pageSize}
                </when>
                
                <!-- 非知识库下层目录：只查询非知识库文件 -->
                <when test='isNonKbDirectory == true and kbId != null'>
                    WITH RECURSIVE file_hierarchy AS (
                        SELECT tn.id, tn.name, tn.full_path, tn.parent_id, tn.node_type,
                               tn.file_type, tn.file_size, tn.storage_path, tn.status,
                               tn.creator_id, tn.create_time, tn.update_time, tn.deleted
                        FROM tb_file_node tn
                        JOIN tb_kb_file_rel rel ON tn.id = rel.file_node_id
                        WHERE rel.kb_id = #{kbId}
                          AND rel.deleted = 0
                          AND tn.deleted = 0
                          AND tn.full_path NOT LIKE '/.knowledgebase%'
                        UNION ALL
                        SELECT p.id, p.name, p.full_path, p.parent_id, p.node_type,
                               p.file_type, p.file_size, p.storage_path, p.status,
                               p.creator_id, p.create_time, p.update_time, p.deleted
                        FROM tb_file_node p
                        JOIN file_hierarchy fh ON p.id = fh.parent_id
                        WHERE p.deleted = 0
                    )
                    SELECT DISTINCT fh.id, fh.name, fh.full_path, fh.parent_id, fh.node_type,
                           fh.file_type, fh.file_size, fh.storage_path, fh.status,
                           fh.creator_id, fh.create_time, fh.update_time, fh.deleted, 
                           u.nickname as creator_name, #{kbId} as kbId
                    FROM file_hierarchy fh
                    LEFT JOIN tb_user u ON fh.creator_id = u.id
                    WHERE fh.parent_id = #{parentId}
                      <if test='keyword != null and keyword != ""'>
                      AND fh.name LIKE CONCAT('%', #{keyword}, '%')
                      </if>
                    ORDER BY fh.node_type ASC, fh.create_time DESC
                    LIMIT #{offset}, #{pageSize}
                </when>
                
                <!-- 默认查询逻辑：知识库下层目录或无知识库信息 -->
                <otherwise>
                    SELECT DISTINCT fn.id, fn.name, fn.full_path, fn.parent_id, fn.node_type,
                           fn.file_type, fn.file_size, fn.storage_path, fn.status,
                           fn.creator_id, fn.create_time, fn.update_time, fn.deleted, 
                           u.nickname as creator_name, #{kbId} as kbId
                    FROM tb_file_node fn
                    LEFT JOIN tb_user u ON fn.creator_id = u.id
                    LEFT JOIN tb_kb_file_rel rel ON fn.id = rel.file_node_id AND rel.deleted = 0
                    WHERE 
                    <choose>
                        <when test='parentId == -1'>
                            fn.parent_id IS NULL
                        </when>
                        <otherwise>
                            fn.parent_id = #{parentId}
                        </otherwise>
                    </choose>
                    AND fn.deleted = 0
                    <if test='kbId != null'>
                    AND (
                        (fn.node_type = 1) OR 
                        (fn.node_type = 2 AND rel.kb_id = #{kbId})
                    )
                    </if>
                    <if test='keyword != null and keyword != ""'>
                    AND fn.name LIKE CONCAT('%', #{keyword}, '%')
                    </if>
                    ORDER BY fn.node_type ASC, fn.create_time DESC
                    LIMIT #{offset}, #{pageSize}
                </otherwise>
            </choose>
            </script>
            """)
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "knowledgeBases", column = "{fileNodeId=id,kbId=kbId}", javaType = List.class,
            many = @Many(select = "selectKnowledgeBases"))
    })
    List<FileNode> selectFilesWithPaging(
            @Param("parentId") Long parentId,
            @Param("kbId") Long kbId,
            @Param("offset") Integer offset,
            @Param("pageSize") Integer pageSize,
            @Param("keyword") String keyword,
            @Param("isKbRoot") Boolean isKbRoot,
            @Param("isNonKbDirectory") Boolean isNonKbDirectory);
            
    /**
     * 查询文件关联的知识库信息
     * @param fileNodeId 文件节点ID
     * @param kbId 知识库ID，如果为null则查询所有关联知识库，如果不为null则只查询指定知识库
     * @return 知识库关联信息列表
     */
    @Select("""
            <script>
            SELECT 
                kb.id as id,
                kb.name as name,
                rel.rel_type as rel_type,
                rel.document_id as document_id,
                rel.document_status as document_status,
                rel.document_info as document_info
            FROM tb_kb_file_rel rel
            JOIN tb_knowledge_base kb ON rel.kb_id = kb.id
            WHERE rel.file_node_id = #{fileNodeId}
            <if test='kbId != null'>
            AND rel.kb_id = #{kbId}
            </if>
            AND rel.deleted = 0
            AND kb.deleted = 0
            </script>
            """)
    List<FileNode.KnowledgeBaseInfo> selectKnowledgeBases(@Param("fileNodeId") Long fileNodeId, @Param("kbId") Long kbId);
    
    @Select("""
            <script>
            <choose>
                <!-- 知识库根目录：合并知识库和非知识库文件统计 -->
                <when test='isKbRoot == true and kbId != null'>
                    SELECT COUNT(*) FROM (
                        <!-- 知识库内文件统计 -->
                        SELECT DISTINCT fn.id
                        FROM tb_file_node fn
                        LEFT JOIN tb_kb_file_rel rel ON fn.id = rel.file_node_id AND rel.deleted = 0
                        WHERE fn.parent_id = #{parentId}
                          AND fn.deleted = 0
                          AND (
                              (fn.node_type = 1) OR 
                              (fn.node_type = 2 AND rel.kb_id = #{kbId})
                          )
                          <if test='keyword != null and keyword != ""'>
                          AND fn.name LIKE CONCAT('%', #{keyword}, '%')
                          </if>
                        
                        UNION ALL
                        
                        <!-- 非知识库文件统计 -->
                        SELECT DISTINCT fh.id
                        FROM (
                            WITH RECURSIVE file_hierarchy AS (
                                SELECT tn.id, tn.name, tn.full_path, tn.parent_id
                                FROM tb_file_node tn
                                JOIN tb_kb_file_rel rel ON tn.id = rel.file_node_id
                                WHERE rel.kb_id = #{kbId}
                                  AND rel.deleted = 0
                                  AND tn.deleted = 0
                                  AND tn.full_path NOT LIKE '/.knowledgebase%'
                                UNION ALL
                                SELECT p.id, p.name, p.full_path, p.parent_id
                                FROM tb_file_node p
                                JOIN file_hierarchy fh ON p.id = fh.parent_id
                                WHERE p.deleted = 0
                            )
                            SELECT * FROM file_hierarchy
                            WHERE parent_id = #{parentId}
                              <if test='keyword != null and keyword != ""'>
                              AND name LIKE CONCAT('%', #{keyword}, '%')
                              </if>
                        ) fh
                    ) combined_count
                </when>
                
                <!-- 非知识库下层目录：只统计非知识库文件 -->
                <when test='isNonKbDirectory == true and kbId != null'>
                    WITH RECURSIVE file_hierarchy AS (
                        SELECT tn.id, tn.name, tn.full_path, tn.parent_id
                        FROM tb_file_node tn
                        JOIN tb_kb_file_rel rel ON tn.id = rel.file_node_id
                        WHERE rel.kb_id = #{kbId}
                          AND rel.deleted = 0
                          AND tn.deleted = 0
                          AND tn.full_path NOT LIKE '/.knowledgebase%'
                        UNION ALL
                        SELECT p.id, p.name, p.full_path, p.parent_id
                        FROM tb_file_node p
                        JOIN file_hierarchy fh ON p.id = fh.parent_id
                        WHERE p.deleted = 0
                    )
                    SELECT COUNT(DISTINCT fh.id)
                    FROM file_hierarchy fh
                    WHERE fh.parent_id = #{parentId}
                      <if test='keyword != null and keyword != ""'>
                      AND fh.name LIKE CONCAT('%', #{keyword}, '%')
                      </if>
                </when>
                
                <!-- 默认统计逻辑：知识库下层目录或无知识库信息 -->
                <otherwise>
                    SELECT COUNT(DISTINCT fn.id)
                    FROM tb_file_node fn
                    LEFT JOIN tb_kb_file_rel rel ON fn.id = rel.file_node_id AND rel.deleted = 0
                    WHERE 
                    <choose>
                        <when test='parentId == -1'>
                            fn.parent_id IS NULL
                        </when>
                        <otherwise>
                            fn.parent_id = #{parentId}
                        </otherwise>
                    </choose>
                    AND fn.deleted = 0
                    <if test='kbId != null'>
                    AND (
                        (fn.node_type = 1) OR 
                        (fn.node_type = 2 AND rel.kb_id = #{kbId})
                    )
                    </if>
                    <if test='keyword != null and keyword != ""'>
                    AND fn.name LIKE CONCAT('%', #{keyword}, '%')
                    </if>
                </otherwise>
            </choose>
            </script>
            """)
    Long countFiles(
            @Param("parentId") Long parentId,
            @Param("kbId") Long kbId,
            @Param("keyword") String keyword,
            @Param("isKbRoot") Boolean isKbRoot,
            @Param("isNonKbDirectory") Boolean isNonKbDirectory);

    /**
     * 查询知识库关联的所有文档信息
     * @param kbId 知识库ID
     * @return 文档信息列表，包含documentId、文件名、文件路径
     */
    @Select("""
            SELECT rel.document_id, fn.name, fn.full_path
            FROM tb_file_node fn
            INNER JOIN tb_kb_file_rel rel ON fn.id = rel.file_node_id
            WHERE rel.kb_id = #{kbId}
              AND rel.deleted = 0
              AND fn.deleted = 0
              AND fn.node_type = 2
              AND rel.document_id IS NOT NULL
            """)
    List<java.util.Map<String, Object>> selectDocumentsByKbId(@Param("kbId") Long kbId);
} 