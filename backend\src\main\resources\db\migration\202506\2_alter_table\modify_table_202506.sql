-- 为用户表添加工号和uuid字段，支持第三方系统集成
ALTER TABLE `tb_user` ADD COLUMN `employee_no` varchar(50) DEFAULT NULL COMMENT '用户工号(第三方系统使用)';
ALTER TABLE `tb_user` ADD COLUMN `user_uuid` varchar(100) DEFAULT NULL COMMENT '用户UUID(第三方系统使用)';
-- 添加用户注册来源字段
ALTER TABLE `tb_user` ADD COLUMN `register_source` varchar(100) DEFAULT 'SYSTEM' COMMENT '用户注册来源：SYSTEM(系统注册)、THIRD_PARTY_appName(第三方接入)、DATA_INTEGRATION(数据集成)';

-- 添加工号的唯一索引
ALTER TABLE `tb_user` ADD UNIQUE KEY `idx_employee_no` (`employee_no`);
-- 添加uuid的唯一索引
ALTER TABLE `tb_user` ADD UNIQUE KEY `idx_user_uuid` (`user_uuid`);
-- 添加注册来源的索引
ALTER TABLE `tb_user` ADD INDEX `idx_register_source` (`register_source`);

CREATE INDEX tb_knowledge_base_type_IDX USING BTREE ON etrx_ai_kb.tb_knowledge_base (`type`);
CREATE INDEX tb_knowledge_base_name_IDX USING BTREE ON etrx_ai_kb.tb_knowledge_base (name);
CREATE INDEX tb_knowledge_base_dataset_id_IDX USING BTREE ON etrx_ai_kb.tb_knowledge_base (dataset_id);
