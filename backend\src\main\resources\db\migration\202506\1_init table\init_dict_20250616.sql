-- 创建数据字典表
CREATE TABLE IF NOT EXISTS `tb_dict` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `dict_type` varchar(50) NOT NULL COMMENT '字典类型',
  `dict_key` varchar(200) NOT NULL COMMENT '字典键(支持路径格式)',
  `dict_value` varchar(500) NOT NULL COMMENT '字典值(类路径+方法名)',
  `dict_label` varchar(200) DEFAULT NULL COMMENT '字典标签',
  `parent_id` bigint DEFAULT NULL COMMENT '父节点ID',
  `node_type` tinyint NOT NULL DEFAULT '1' COMMENT '节点类型(1:目录,2:配置项)',
  `path_level` int NOT NULL DEFAULT '1' COMMENT '层级深度',
  `match_type` tinyint NOT NULL DEFAULT '1' COMMENT '匹配类型(1:精确匹配,2:类通配符,3:包通配符)',
  `http_method` varchar(20) DEFAULT NULL COMMENT 'HTTP方法(GET,POST,PUT,DELETE等,可选)',
  `url_pattern` varchar(500) DEFAULT NULL COMMENT 'URL模式(可选,用于双重验证)',
  `description` varchar(500) DEFAULT NULL COMMENT '描述',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(0:禁用,1:启用)',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除(0:未删除,1:已删除)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_type_key` (`dict_type`, `dict_key`),
  KEY `idx_dict_type` (`dict_type`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_node_type` (`node_type`),
  KEY `idx_match_type` (`match_type`),
  KEY `idx_http_method` (`http_method`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='数据字典表';

-- 插入第三方API白名单配置（方法级别控制）
-- 根目录
INSERT INTO `tb_dict` (`dict_type`, `dict_key`, `dict_value`, `dict_label`, `parent_id`, `node_type`, `path_level`, `match_type`, `description`, `sort_order`, `status`) VALUES
('third_party_whitelist', 'third_party_method_api', '', '第三方API方法白名单', NULL, 1, 1, 1, '基于类路径+方法名的精确控制', 1, 1);

SET @root_id = LAST_INSERT_ID();

-- 认证控制器目录
INSERT INTO `tb_dict` (`dict_type`, `dict_key`, `dict_value`, `dict_label`, `parent_id`, `node_type`, `path_level`, `match_type`, `description`, `sort_order`, `status`) VALUES
('third_party_whitelist', 'third_party_method_api.auth_controller', '', '认证控制器', @root_id, 1, 2, 1, '认证相关的Controller方法', 0, 1);

SET @auth_controller_id = LAST_INSERT_ID();

-- 认证控制器方法配置（仅开放第三方登录接口）
INSERT INTO `tb_dict` (`dict_type`, `dict_key`, `dict_value`, `dict_label`, `parent_id`, `node_type`, `path_level`, `match_type`, `http_method`, `url_pattern`, `description`, `sort_order`, `status`) VALUES
-- 第三方登录接口
('third_party_whitelist', 'third_party_method_api.auth_controller.third_party_login', 'com.etrx.kb.controller.AuthController.thirdPartyLogin', '第三方登录', @auth_controller_id, 2, 3, 1, 'POST', '/api/auth/app-login', '精确匹配：开放第三方系统登录接口', 1, 1);

-- 知识库控制器目录
INSERT INTO `tb_dict` (`dict_type`, `dict_key`, `dict_value`, `dict_label`, `parent_id`, `node_type`, `path_level`, `match_type`, `description`, `sort_order`, `status`) VALUES
('third_party_whitelist', 'third_party_method_api.kb_controller', '', '知识库控制器', @root_id, 1, 2, 1, '知识库相关的Controller方法', 1, 1);

SET @kb_controller_id = LAST_INSERT_ID();

-- 知识库控制器方法配置（开放所有方法）
INSERT INTO `tb_dict` (`dict_type`, `dict_key`, `dict_value`, `dict_label`, `parent_id`, `node_type`, `path_level`, `match_type`, `http_method`, `url_pattern`, `description`, `sort_order`, `status`) VALUES
-- 开放知识库控制器的所有方法
('third_party_whitelist', 'third_party_method_api.kb_controller.all_methods', 'com.etrx.kb.controller.KnowledgeBaseController.*', '知识库所有方法', @kb_controller_id, 2, 3, 3, NULL, '/api/knowledge-bases/**', '包通配符匹配：开放KnowledgeBaseController所有方法', 1, 1);

-- 聊天控制器目录
INSERT INTO `tb_dict` (`dict_type`, `dict_key`, `dict_value`, `dict_label`, `parent_id`, `node_type`, `path_level`, `match_type`, `description`, `sort_order`, `status`) VALUES
('third_party_whitelist', 'third_party_method_api.chat_controller', '', '聊天控制器', @root_id, 1, 2, 1, '聊天相关的Controller方法', 2, 1);

SET @chat_controller_id = LAST_INSERT_ID();

-- 聊天控制器方法配置（开放所有方法）
INSERT INTO `tb_dict` (`dict_type`, `dict_key`, `dict_value`, `dict_label`, `parent_id`, `node_type`, `path_level`, `match_type`, `http_method`, `url_pattern`, `description`, `sort_order`, `status`) VALUES
-- 开放聊天控制器的所有方法
('third_party_whitelist', 'third_party_method_api.chat_controller.all_methods', 'com.etrx.kb.controller.ChatController.*', '聊天所有方法', @chat_controller_id, 2, 3, 3, NULL, '/api/chat/**', '包通配符匹配：开放ChatController所有方法', 1, 1);

-- 文件控制器目录
INSERT INTO `tb_dict` (`dict_type`, `dict_key`, `dict_value`, `dict_label`, `parent_id`, `node_type`, `path_level`, `match_type`, `description`, `sort_order`, `status`) VALUES
('third_party_whitelist', 'third_party_method_api.file_controller', '', '文件控制器', @root_id, 1, 2, 1, '文件管理相关的Controller方法', 3, 1);

SET @file_controller_id = LAST_INSERT_ID();

-- 文件控制器方法配置（开放所有方法）
INSERT INTO `tb_dict` (`dict_type`, `dict_key`, `dict_value`, `dict_label`, `parent_id`, `node_type`, `path_level`, `match_type`, `http_method`, `url_pattern`, `description`, `sort_order`, `status`) VALUES
-- 开放文件控制器的所有方法
('third_party_whitelist', 'third_party_method_api.file_controller.all_methods', 'com.etrx.kb.controller.FileController.*', '文件所有方法', @file_controller_id, 2, 3, 3, NULL, '/api/files/**', '包通配符匹配：开放FileController所有方法', 1, 1);

-- 模型控制器目录
INSERT INTO `tb_dict` (`dict_type`, `dict_key`, `dict_value`, `dict_label`, `parent_id`, `node_type`, `path_level`, `match_type`, `description`, `sort_order`, `status`) VALUES
('third_party_whitelist', 'third_party_method_api.model_controller', '', '模型控制器', @root_id, 1, 2, 1, '模型管理相关的Controller方法', 4, 1);

SET @model_controller_id = LAST_INSERT_ID();

-- 模型控制器方法配置（开放所有方法）
INSERT INTO `tb_dict` (`dict_type`, `dict_key`, `dict_value`, `dict_label`, `parent_id`, `node_type`, `path_level`, `match_type`, `http_method`, `url_pattern`, `description`, `sort_order`, `status`) VALUES
-- 开放模型控制器的所有方法
('third_party_whitelist', 'third_party_method_api.model_controller.all_methods', 'com.etrx.kb.controller.ModelController.*', '模型所有方法', @model_controller_id, 2, 3, 3, NULL, '/aikb/models/**', '包通配符匹配：开放ModelController所有方法', 1, 1);

-- 插入AI嵌入模型配置
-- 单一key存储多个模型值，使用JSON数组格式便于解析
INSERT INTO `tb_dict` (`dict_type`, `dict_key`, `dict_value`, `dict_label`, `parent_id`, `node_type`, `path_level`, `match_type`, `description`, `sort_order`, `status`) VALUES
('AI_EMBEDDING_MODEL', 'AI_EMBEDDING_MODEL', '["BAAI/bge-large-zh-v1.5@BAAI","nomic-embed-text:latest@Ollama"]', 'AI嵌入模型列表', NULL, 2, 1, 1, 'AI文本嵌入模型配置列表，包含BAAI中文大模型v1.5和Nomic文本嵌入模型', 1, 1);
