package com.etrx.kb.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 文档DTO
 */
@Data
public class DocumentDTO {

    /**
     * 文档ID
     */
    private Long id;

    /**
     * 知识库ID
     */
    private Long kbId;

    /**
     * 文档名称
     */
    private String name;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * 文件大小(字节)
     */
    private Long fileSize;

    /**
     * 文件夹ID
     */
    private Long folderId;

    /**
     * 状态(0:处理中,1:已完成,2:失败)
     */
    private Integer status;

    /**
     * 嵌入状态(0:未嵌入,1:嵌入中,2:已嵌入,3:失败)
     */
    private Integer embedStatus;

    /**
     * 上传者ID
     */
    private Long uploaderId;

    /**
     * 逻辑删除(0:未删除,1:已删除)
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}