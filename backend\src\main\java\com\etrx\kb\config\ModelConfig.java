package com.etrx.kb.config;

import com.etrx.kb.dto.ModelDTO;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;

/**
 * 模型配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "models")
public class ModelConfig {

    /**
     * 默认模型提供商
     */
    private String defaultProvider;

    /**
     * 默认模型名称
     */
    private String defaultModel;

    /**
     * 按提供商分组的模型配置
     * 键为提供商名称（如 "Ollama", "OpenAI"）
     * 值为该提供商的配置信息
     */
    private Map<String, ProviderConfig> providers;

    /**
     * 提供商配置
     */
    @Data
    public static class ProviderConfig {
        /**
         * 是否启用
         */
        private Boolean enabled = true;

        /**
         * 基础URL
         */
        private String baseUrl;

        /**
         * API密钥
         */
        private String apiKey;

        /**
         * 端点（用于Azure OpenAI）
         */
        private String endpoint;

        /**
         * 默认温度参数
         */
        private Double temperature = 0.7;

        /**
         * 默认模型
         */
        private String defaultModel;

        /**
         * 模型列表
         */
        private List<ModelConfigItem> models;
    }

    /**
     * 模型配置项
     */
    @Data
    public static class ModelConfigItem {
        /**
         * 是否可用
         */
        private Boolean available = true;

        /**
         * 模型名称
         */
        private String llmName;

        /**
         * 模型类型：chat, embedding, tts, speech2text
         */
        private String modelType;

        /**
         * 最大令牌数
         */
        private Integer maxTokens;

        /**
         * 是否支持工具
         */
        private Boolean isTools = false;

        /**
         * 状态
         */
        private String status = "1";

        /**
         * 标签
         */
        private String tags;

        /**
         * 转换为ModelDTO
         */
        public ModelDTO toModelDTO(String fid) {
            if (maxTokens != null) {
                return new ModelDTO(available, fid, llmName, modelType, 
                                  maxTokens, isTools, status, tags);
            } else {
                return new ModelDTO(available, fid, llmName, modelType);
            }
        }
    }
} 