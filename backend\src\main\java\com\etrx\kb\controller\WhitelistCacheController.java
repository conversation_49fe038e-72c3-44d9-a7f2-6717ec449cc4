package com.etrx.kb.controller;

import com.etrx.kb.common.Result;
import com.etrx.kb.service.ThirdPartyWhitelistService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 白名单缓存管理控制器
 * 
 * <AUTHOR>
 * @date 2024-01-10
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/admin/whitelist-cache")
@RequiredArgsConstructor
@Tag(name = "白名单缓存管理", description = "第三方API白名单缓存管理接口")
public class WhitelistCacheController {
    
    private final ThirdPartyWhitelistService whitelistService;
    
    /**
     * 手动刷新缓存
     */
    @PostMapping("/refresh")
    @Operation(summary = "手动刷新缓存", description = "立即刷新第三方API白名单缓存")
    public Result<String> refreshCache() {
        log.info("手动刷新白名单缓存");
        
        try {
            long startTime = System.currentTimeMillis();
            whitelistService.refreshCache();
            long costTime = System.currentTimeMillis() - startTime;
            
            String message = String.format("缓存刷新成功，耗时: %dms", costTime);
            log.info(message);
            return Result.success(message);
            
        } catch (Exception e) {
            log.error("手动刷新缓存失败", e);
            return Result.failed("缓存刷新失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取缓存状态信息
     */
    @GetMapping("/status")
    @Operation(summary = "获取缓存状态", description = "查看第三方API白名单缓存的状态信息")
    public Result<Map<String, Object>> getCacheStatus() {
        log.info("查看白名单缓存状态");
        
        Map<String, Object> status = new HashMap<>();
        status.put("cacheInfo", whitelistService.getCacheInfo());
        status.put("methodCount", whitelistService.getWhitelistMethods().size());
        status.put("timestamp", System.currentTimeMillis());
        
        return Result.success(status);
    }
    
    /**
     * 获取所有白名单方法
     */
    @GetMapping("/methods")
    @Operation(summary = "获取所有白名单方法", description = "获取当前缓存中的所有白名单方法列表")
    public Result<List<String>> getAllWhitelistMethods() {
        log.info("获取所有白名单方法");
        
        List<String> methods = whitelistService.getWhitelistMethods();
        return Result.success(methods);
    }
} 