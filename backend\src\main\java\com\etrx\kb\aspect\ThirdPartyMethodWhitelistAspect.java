package com.etrx.kb.aspect;

import com.etrx.kb.exception.ThirdPartyAccessDeniedException;
import com.etrx.kb.service.ThirdPartyWhitelistService;
import com.etrx.kb.util.JwtTokenUtil;
import com.etrx.kb.util.SecurityUtils;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * 第三方API方法级别白名单拦截器
 */
@Slf4j
@Aspect
@Component
@Order(1) // 设置较高优先级，确保在其他切面之前执行
@RequiredArgsConstructor
public class ThirdPartyMethodWhitelistAspect {

    private final ThirdPartyWhitelistService whitelistService;
    private final JwtTokenUtil jwtTokenUtil;

    /**
     * 拦截所有带有Spring MVC注解的Controller方法
     * 包括：@RestController类中的方法
     * 以及所有带有HTTP映射注解的方法
     */
    @Around("@within(org.springframework.web.bind.annotation.RestController) || " +
            "@annotation(org.springframework.web.bind.annotation.RequestMapping) || " +
            "@annotation(org.springframework.web.bind.annotation.GetMapping) || " +
            "@annotation(org.springframework.web.bind.annotation.PostMapping) || " +
            "@annotation(org.springframework.web.bind.annotation.PutMapping) || " +
            "@annotation(org.springframework.web.bind.annotation.DeleteMapping) || " +
            "@annotation(org.springframework.web.bind.annotation.PatchMapping)")
    public Object checkMethodWhitelist(ProceedingJoinPoint joinPoint) throws Throwable {
        
        // 获取当前请求
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return joinPoint.proceed();
        }
        
        HttpServletRequest request = attributes.getRequest();
        
        // 获取当前执行的方法信息
        String className = joinPoint.getTarget().getClass().getName();
        String methodName = joinPoint.getSignature().getName();
        String fullMethodName = className + "." + methodName;
        
        // 特殊处理：第三方登录接口不需要token验证，直接放行
        if (isThirdPartyLoginMethod(fullMethodName)) {
            log.debug("第三方登录接口，直接放行: {}", fullMethodName);
            return joinPoint.proceed();
        }
        
        // 检查是否为第三方请求
        if (!isThirdPartyRequest(request)) {
            // 非第三方请求，直接放行
            return joinPoint.proceed();
        }
        
        // 获取HTTP方法
        String httpMethod = request.getMethod();
        
        log.debug("第三方请求方法拦截 - 类名: {}, 方法名: {}, HTTP方法: {}", className, methodName, httpMethod);
        
        // 检查方法是否在白名单中
        if (whitelistService.isMethodInWhitelist(fullMethodName, httpMethod)) {
            log.debug("第三方方法调用通过白名单验证: {}", fullMethodName);
            return joinPoint.proceed();
        } else {
            log.warn("第三方方法调用被白名单拒绝: {} [{}]", fullMethodName, httpMethod);
            throw new ThirdPartyAccessDeniedException(fullMethodName, httpMethod);
        }
    }
    
    /**
     * 判断是否为第三方登录方法
     */
    private boolean isThirdPartyLoginMethod(String fullMethodName) {
        return "com.etrx.kb.controller.AuthController.thirdPartyLogin".equals(fullMethodName);
    }
    
    /**
     * 判断是否为第三方请求
     */
    private boolean isThirdPartyRequest(HttpServletRequest request) {
        try {
            // 1. 检查是否为API密钥认证
            Boolean isApiKeyAuth = (Boolean) request.getAttribute("API_KEY_AUTH");
            if (isApiKeyAuth != null && isApiKeyAuth) {
                log.debug("检测到API密钥认证的第三方请求");
                return true;
            }
            
            // 2. 检查是否为JWT Token认证
            String authHeader = request.getHeader("Authorization");
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                return false;
            }
            
            String token = authHeader.substring(7);
            boolean isThirdPartyToken = jwtTokenUtil.isThirdPartyToken(token);
            if (isThirdPartyToken) {
                log.debug("检测到JWT Token认证的第三方请求");
            }
            return isThirdPartyToken;
            
        } catch (Exception e) {
            log.debug("判断第三方请求失败", e);
            return false;
        }
    }
} 