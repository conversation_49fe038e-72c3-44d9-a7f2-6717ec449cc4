package com.etrx.kb.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 聊天会话DTO
 */
@Data
public class ChatSessionDTO {

    /**
     * ID
     */
    private Long id;

    /**
     * 平台用户ID
     */
    private Long userId;

    /**
     * RAGflow会话ID
     */
    private String sessionId;

    /**
     * RAGflow聊天助手ID
     */
    private String chatAssistantId;

    /**
     * 会话名称
     */
    private String sessionName;

    /**
     * 状态: 0-禁用, 1-启用
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 