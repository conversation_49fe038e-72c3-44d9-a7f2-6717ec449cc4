server:
  port: ${SERVER_PORT:8080}
  # servlet:
  #   context-path: ${SERVER_CONTEXT_PATH:/aikb}

spring:
  application:
    name: etrx-ai-kb
  
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:3306}/${DB_NAME:etrx_ai_kb}?useUnicode=true&characterEncoding=utf8&serverTimezone=Asia/Shanghai&useSSL=false
    username: ${DB_USER:etrx}
    password: ${DB_PASSWORD:etrx123}
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall
  
  # Redis配置
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:etrx123}
      database: ${REDIS_DATABASE:0}
      timeout: 10s
      lettuce:
        pool:
          min-idle: 0
          max-idle: 8
          max-active: 8
          max-wait: -1ms
  
  # Flyway配置
  flyway:
    enabled: ${FLYWAY_ENABLED:true}
    locations: classpath:db/migration
    baseline-on-migrate: ${FLYWAY_BASELINE_ON_MIGRATE:true}
    baseline-version: ${FLYWAY_BASELINE_VERSION:1}
    mixed: ${FLYWAY_MIXED:true}
    clean-disabled: ${FLYWAY_CLEAN_DISABLED:true}
    validate-on-migrate: ${FLYWAY_VALIDATE_ON_MIGRATE:true}
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: ${SPRING_SERVLET_MULTIPART_MAX_FILE_SIZE:100MB}
      max-request-size: ${SPRING_SERVLET_MULTIPART_MAX_REQUEST_SIZE:100MB}
  ai:
    dashscope:
      api-key: sk-2a704484e328408a9afe85ef2c0afeb2
      chat:
        options:
          model: qwen3:8b
    # Ollama配置
    ollama:
      base-url: http://**************:11434
      chat:
        enabled: true
        options:
          model: qwen3:8b
          temperature: 0.7

    # OpenAI配置
    openai:
      api-key: 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
      base-url: ${models.providers.OpenAI.baseUrl:https://openrouter.ai/api/v1}
      chat:
        enabled: ${models.providers.OpenAI.enabled:false}
        options:
          model: DeepSeek:R1
          temperature: ${models.providers.OpenAI.temperature:0.7}

    # Azure OpenAI配置 - 只有在启用时才配置
    azure:
      openai:
        api-key: ${models.providers.AzureOpenAI.apiKey:dummy-azure-key-for-development-only}
        endpoint: ${models.providers.AzureOpenAI.endpoint:https://dummy.openai.azure.com}
        chat:
          enabled: ${models.providers.AzureOpenAI.enabled:false}
          options:
            deployment-name: ${models.providers.AzureOpenAI.defaultModel:gpt-35-turbo}
            temperature: ${models.providers.AzureOpenAI.temperature:0.7}
    # MCP配置 - 禁用自动配置的服务器传输
    mcp:
      server:
        stdio:
          enabled: false
        webmvc:
          enabled: false
        webflux:
          enabled: false
# RDMS API配置 (用于MCP服务)
rdms:
  api:
    url: ${RDMS_API_URL:http://rc.szcp.ddns.e-lead.cn}
    token: ${RDMS_API_TOKEN:default-token}

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath:mapper/**/*.xml
  type-aliases-package: com.etrx.kb.domain
  configuration:
    default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: ${MYBATIS_LOG_IMPL:org.apache.ibatis.logging.stdout.StdOutImpl}
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# MinIO配置
minio:
  endpoint: ${MINIO_ENDPOINT:http://localhost:9000}
  accessKey: ${MINIO_ACCESS_KEY:etrx}
  secretKey: ${MINIO_SECRET_KEY:etrx123}
  bucketName: ${MINIO_BUCKET_NAME:etrx-kb-docs}

# JWT配置
jwt:
  secret: ${JWT_SECRET:etrx-ai-kb-secret-key-for-jwt-token-generation-and-validation}
  expiration: ${JWT_EXPIRATION:86400000}  # 24小时
  header: Authorization
  token-prefix: Bearer 

# 日志配置
logging:
  level:
    root: info
    org.springframework: INFO
    com.etrx: DEBUG

# Knife4j配置
knife4j:
  enable: ${KNIFE4J_ENABLE:true}
  setting:
    language: zh-CN
    swagger-model-name: 实体类列表
    enable-swagger-models: true
    enable-document-manage: true
    enable-home-custom: false
    enable-footer-custom: false
    enable-footer: false

# RAGflow配置
ragflow:
  base-url: ${RAGFLOW_BASE_URL:http://localhost:9380}
  static-url: http://**************/v1
  api-key: ${RAGFLOW_API_KEY:}
  user-id: ${RAGFLOW_USER_ID:}
  timeout: ${RAGFLOW_TIMEOUT:30000}
  retry-count: ${RAGFLOW_RETRY_COUNT:3}
  # 默认LLM配置
  default-llm:
    model-name: ${RAGFLOW_DEFAULT_MODEL:qwen-plus@Tongyi-Qianwen}
    temperature: ${RAGFLOW_DEFAULT_TEMPERATURE:0.1}
    top-p: ${RAGFLOW_DEFAULT_TOP_P:0.3}
    max-tokens: ${RAGFLOW_DEFAULT_MAX_TOKENS:512}
  # 默认提示配置
  default-prompt:
    similarity-threshold: ${RAGFLOW_DEFAULT_SIMILARITY_THRESHOLD:0.2}
    keywords-similarity-weight: ${RAGFLOW_DEFAULT_KEYWORDS_WEIGHT:0.7}
    top-n: ${RAGFLOW_DEFAULT_TOP_N:8}

# 模型配置
models:
  # 默认模型提供商
  default-provider: ${DEFAULT_MODEL_PROVIDER:Ollama}
  # 默认模型名称
  default-model: ${DEFAULT_MODEL_NAME:qwen3:8b}
  providers:
    Ollama:
      enabled: ${OLLAMA_ENABLED:true}
      baseUrl: ${OLLAMA_BASE_URL:http://**************:11434}
      temperature: ${OLLAMA_TEMPERATURE:0.7}
      defaultModel: ${OLLAMA_DEFAULT_MODEL:qwen3:8b}
      models:
        - llmName: "qwen3:8b"
          modelType: "chat"
          available: true
          maxTokens: 8192
          isTools: false
          status: "1"
          tags: "LLM,CHAT,8K"

# 临时禁用 context propagation 解决 Spring AI MessageAggregator 兼容性问题
management:
  observations:
    enabled: false
  tracing:
    enabled: false

# 任务调度配置
task:
  scheduler:
    enabled: true
    host-count: 5
    batch-size: 100
    fixed-delay: 30000