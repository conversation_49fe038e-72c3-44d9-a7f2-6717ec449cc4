package com.etrx.kb.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.Getter;

@Getter
public enum TaskExecutionStatus implements IEnum<Integer> {
    
    PENDING(0, "待执行"),
    RUNNING(1, "执行中"),
    COMPLETED(2, "执行成功"),
    FAILED(3, "执行失败"),
    CANCELLED(4, "已取消");

    @EnumValue
    private final int value;
    private final String description;

    TaskExecutionStatus(int value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }

    /**
     * 根据状态值获取枚举
     */
    public static TaskExecutionStatus fromValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (TaskExecutionStatus status : TaskExecutionStatus.values()) {
            if (status.value == value) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid TaskExecutionStatus value: " + value);
    }
}
