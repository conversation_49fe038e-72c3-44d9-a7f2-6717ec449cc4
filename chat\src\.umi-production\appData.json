{"cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "pkg": {"name": "etrx-ai-assistant", "version": "1.0.0", "description": "AI知识库聊天系统前端", "private": false, "author": "wanggangqi <<EMAIL>>", "scripts": {"prepublishOnly": "npm run build && npm run build:component", "build": "umi build", "build:component": "cross-env NODE_ENV=production vite build", "dev": "cross-env TAILWIND_MODE=watch UMI_DEV_SERVER_COMPRESS=none umi dev", "postinstall": "umi setup", "lint": "umi lint --eslint-only", "prepare": "cd .. && husky web/.husky", "setup": "umi setup", "start": "npm run dev", "test": "jest --no-cache --coverage"}, "lint-staged": {"*.{js,jsx,ts,tsx,css,less,json}": ["prettier --write --ignore-unknown"]}, "dependencies": {"@ant-design/icons": "^5.2.6", "@ant-design/pro-components": "^2.6.46", "@ant-design/pro-layout": "^7.17.16", "@antv/g2": "^5.2.10", "@antv/g6": "^5.0.10", "@hookform/resolvers": "^3.9.1", "@js-preview/excel": "^1.7.8", "@lexical/react": "^0.23.1", "@monaco-editor/react": "^4.6.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "^1.1.11", "@radix-ui/react-icons": "^1.3.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.4", "@tailwindcss/line-clamp": "^0.4.4", "@tanstack/react-query": "^5.40.0", "@tanstack/react-query-devtools": "^5.51.5", "@tanstack/react-table": "^8.20.5", "@uiw/react-markdown-preview": "^5.1.3", "@xyflow/react": "^12.3.6", "ahooks": "^3.7.10", "antd": "^5.12.7", "axios": "^1.6.3", "class-variance-authority": "^0.7.0", "classnames": "^2.5.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "dayjs": "^1.11.10", "dompurify": "^3.1.6", "eventsource-parser": "^1.1.2", "human-id": "^4.1.1", "i18next": "^23.7.16", "i18next-browser-languagedetector": "^8.0.0", "immer": "^10.1.1", "input-otp": "^1.4.1", "js-base64": "^3.7.5", "jsencrypt": "^3.3.2", "lexical": "^0.23.1", "lodash": "^4.17.21", "lucide-react": "^0.508.0", "mammoth": "^1.7.2", "monaco-editor": "^0.52.2", "next-themes": "^0.4.6", "node-stdlib-browser": "^1.3.1", "openai-speech-stream-player": "^1.0.8", "rc-tween-one": "^3.0.6", "react-copy-to-clipboard": "^5.1.0", "react-dropzone": "^14.3.5", "react-error-boundary": "^4.0.13", "react-hook-form": "^7.56.4", "react-i18next": "^14.0.0", "react-infinite-scroll-component": "^6.1.0", "react-markdown": "^9.0.1", "react-pdf-highlighter": "^6.1.0", "react-string-replace": "^1.1.1", "react-syntax-highlighter": "^15.5.0", "react18-json-view": "^0.2.8", "recharts": "^2.12.4", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "sonner": "^1.7.4", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "umi": "^4.0.90", "umi-request": "^1.4.0", "unist-util-visit-parents": "^6.0.1", "uuid": "^9.0.1", "zod": "^3.23.8", "zustand": "^4.5.2"}, "devDependencies": {"@react-dev-inspector/umi4-plugin": "^2.0.1", "@redux-devtools/extension": "^3.3.0", "@testing-library/jest-dom": "^6.4.5", "@testing-library/react": "^15.0.7", "@types/dompurify": "^3.0.5", "@types/jest": "^29.5.12", "@types/lodash": "^4.14.202", "@types/react": "^18.0.33", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "^18.0.11", "@types/react-syntax-highlighter": "^15.5.11", "@types/testing-library__jest-dom": "^6.0.0", "@types/uuid": "^9.0.8", "@types/webpack-env": "^1.18.4", "@umijs/lint": "^4.1.1", "@umijs/plugins": "^4.1.0", "@vitejs/plugin-react": "4.2.1", "cross-env": "^7.0.3", "html-loader": "^5.1.0", "husky": "^9.0.11", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.2.7", "prettier": "^3.2.4", "prettier-plugin-organize-imports": "^3.2.4", "prettier-plugin-packagejson": "^2.4.9", "react-dev-inspector": "^2.0.1", "remark-loader": "^6.0.0", "rollup-plugin-node-polyfills": "^0.2.1", "rollup-plugin-polyfill-node": "^0.13.0", "tailwindcss": "^3", "terser-webpack-plugin": "^5.3.11", "ts-node": "^10.9.2", "typescript": "^5.0.3", "umi-plugin-icons": "^0.1.1", "vite": "5.1.4", "vite-plugin-node-stdlib-browser": "^0.2.1", "vite-plugin-svgr": "^4.3.0"}, "engines": {"node": ">=18.20.4"}, "overrides": {"@radix-ui/react-dismissable-layer": "1.1.4"}}, "pkgPath": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat\\package.json", "plugins": {"./node_modules/@umijs/core/dist/service/servicePlugin": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "preset", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/core/dist/service/servicePlugin.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/core/dist/service/servicePlugin", "key": "servicePlugin"}, "@umijs/preset-umi": {"config": {}, "time": {"hooks": {}, "register": 34}, "enableBy": "register", "type": "preset", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/index.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "@umijs/preset-umi", "key": "umi"}, "./node_modules/@umijs/preset-umi/dist/registerMethods": {"config": {}, "time": {"hooks": {"onStart": [0]}, "register": 6}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/registerMethods.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/registerMethods", "key": "registerMethods"}, "@umijs/did-you-know": {"config": {}, "time": {"hooks": {"onStart": [1]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/did-you-know/dist/plugin.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "@umijs/did-you-know", "key": "umijsDidYouKnow"}, "./node_modules/@umijs/preset-umi/dist/features/404/404": {"config": {}, "time": {"hooks": {"modifyRoutes": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/404/404.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/404/404", "key": "404"}, "./node_modules/@umijs/preset-umi/dist/features/appData/appData": {"config": {}, "time": {"hooks": {"modifyAppData": [222]}, "register": 31}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/appData/appData.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/appData/appData", "key": "appData"}, "./node_modules/@umijs/preset-umi/dist/features/appData/umiInfo": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/appData/umiInfo.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/appData/umiInfo", "key": "umiInfo"}, "./node_modules/@umijs/preset-umi/dist/features/check/check": {"config": {}, "time": {"hooks": {"onCheckConfig": [0], "onCheck": [0]}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/check/check.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/check/check", "key": "check"}, "./node_modules/@umijs/preset-umi/dist/features/check/babel722": {"config": {}, "time": {"hooks": {"onCheck": [1]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/check/babel722.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/check/babel722", "key": "babel722"}, "./node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting", "key": "codeSplitting"}, "./node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins": {"config": {}, "time": {"hooks": {"modifyConfig": [0]}, "register": 17}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins", "key": "configPlugins"}, "virtual: config-title": {"id": "virtual: config-title", "key": "title", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-styles": {"id": "virtual: config-styles", "key": "styles", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-scripts": {"id": "virtual: config-scripts", "key": "scripts", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-routes": {"id": "virtual: config-routes", "key": "routes", "config": {"onChange": "regenerateTmpFiles"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-routeLoader": {"id": "virtual: config-routeLoader", "key": "routeLoader", "config": {"default": {"moduleType": "esm"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-reactRouter5Compat": {"id": "virtual: config-reactRouter5Compat", "key": "reactRouter5Compat", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-presets": {"id": "virtual: config-presets", "key": "presets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-plugins": {"id": "virtual: config-plugins", "key": "plugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-npmClient": {"id": "virtual: config-npmClient", "key": "npmClient", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mountElementId": {"id": "virtual: config-mountElementId", "key": "mountElementId", "config": {"default": "root"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-metas": {"id": "virtual: config-metas", "key": "metas", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-links": {"id": "virtual: config-links", "key": "links", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-historyWithQuery": {"id": "virtual: config-historyWithQuery", "key": "historyWithQuery", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-history": {"id": "virtual: config-history", "key": "history", "config": {"default": {"type": "browser"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-headScripts": {"id": "virtual: config-headScripts", "key": "headScripts", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-esbuildMinifyIIFE": {"id": "virtual: config-esbuildMinifyIIFE", "key": "esbuildMinifyIIFE", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-conventionRoutes": {"id": "virtual: config-conventionRoutes", "key": "conventionRoutes", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-conventionLayout": {"id": "virtual: config-conventionLayout", "key": "conventionLayout", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-base": {"id": "virtual: config-base", "key": "base", "config": {"default": "/"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-analyze": {"id": "virtual: config-analyze", "key": "analyze", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-writeToDisk": {"id": "virtual: config-writeToDisk", "key": "writeToDisk", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-transformRuntime": {"id": "virtual: config-transformRuntime", "key": "transformRuntime", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-theme": {"id": "virtual: config-theme", "key": "theme", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-targets": {"id": "virtual: config-targets", "key": "targets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-svgr": {"id": "virtual: config-svgr", "key": "svgr", "config": {"default": {}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-svgo": {"id": "virtual: config-svgo", "key": "svgo", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-stylusLoader": {"id": "virtual: config-stylusLoader", "key": "stylus<PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-styleLoader": {"id": "virtual: config-style<PERSON>oader", "key": "<PERSON><PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-srcTranspilerOptions": {"id": "virtual: config-srcTranspilerOptions", "key": "srcTranspilerOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-srcTranspiler": {"id": "virtual: config-srcTranspiler", "key": "srcTranspiler", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-sassLoader": {"id": "virtual: config-sassLoader", "key": "sass<PERSON><PERSON>der", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-runtimePublicPath": {"id": "virtual: config-runtimePublicPath", "key": "runtimePublicPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-purgeCSS": {"id": "virtual: config-purgeCSS", "key": "purgeCSS", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-publicPath": {"id": "virtual: config-publicPath", "key": "publicPath", "config": {"default": "/"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-proxy": {"id": "virtual: config-proxy", "key": "proxy", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-postcssLoader": {"id": "virtual: config-postcssLoader", "key": "postcss<PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-outputPath": {"id": "virtual: config-outputPath", "key": "outputPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-normalCSSLoaderModules": {"id": "virtual: config-normalCSSLoaderModules", "key": "normalCSSLoaderModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mfsu": {"id": "virtual: config-mfsu", "key": "mfsu", "config": {"default": {"strategy": "eager"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mdx": {"id": "virtual: config-mdx", "key": "mdx", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-manifest": {"id": "virtual: config-manifest", "key": "manifest", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-lessLoader": {"id": "virtual: config-less<PERSON><PERSON>der", "key": "<PERSON><PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-jsMinifierOptions": {"id": "virtual: config-jsMinifierOptions", "key": "jsMinifierOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-jsMinifier": {"id": "virtual: config-jsMinifier", "key": "jsMinifier", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-inlineLimit": {"id": "virtual: config-inlineLimit", "key": "inlineLimit", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-ignoreMomentLocale": {"id": "virtual: config-ignoreMomentLocale", "key": "ignoreMomentLocale", "config": {"default": true}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-https": {"id": "virtual: config-https", "key": "https", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-hash": {"id": "virtual: config-hash", "key": "hash", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-forkTSChecker": {"id": "virtual: config-fork<PERSON><PERSON><PERSON><PERSON>", "key": "forkTSChecker", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-fastRefresh": {"id": "virtual: config-fastRefresh", "key": "fastRefresh", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraPostCSSPlugins": {"id": "virtual: config-extraPostCSSPlugins", "key": "extraPostCSSPlugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelPresets": {"id": "virtual: config-extraBabelPresets", "key": "extraBabelPresets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelPlugins": {"id": "virtual: config-extraBabelPlugins", "key": "extraBabelPlugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelIncludes": {"id": "virtual: config-extraBabelIncludes", "key": "extraBabelIncludes", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-externals": {"id": "virtual: config-externals", "key": "externals", "config": {"default": {}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-esm": {"id": "virtual: config-esm", "key": "esm", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-devtool": {"id": "virtual: config-devtool", "key": "devtool", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-depTranspiler": {"id": "virtual: config-depTranspiler", "key": "depTranspiler", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-define": {"id": "virtual: config-define", "key": "define", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-deadCode": {"id": "virtual: config-deadCode", "key": "deadCode", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssPublicPath": {"id": "virtual: config-cssPublicPath", "key": "cssPublicPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssMinifierOptions": {"id": "virtual: config-cssMinifierOptions", "key": "cssMinifierOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssMinifier": {"id": "virtual: config-cssMinifier", "key": "cssMinifier", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssLoaderModules": {"id": "virtual: config-cssLoaderModules", "key": "cssLoaderModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssLoader": {"id": "virtual: config-cssLoader", "key": "cssL<PERSON>der", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-copy": {"id": "virtual: config-copy", "key": "copy", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-checkDepCssModules": {"id": "virtual: config-checkDepCssModules", "key": "checkDepCssModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-chainWebpack": {"id": "virtual: config-chainWebpack", "key": "chainWebpack", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cacheDirectoryPath": {"id": "virtual: config-cacheDirectoryPath", "key": "cacheDirectoryPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-babelLoaderCustomize": {"id": "virtual: config-babelLoaderCustomize", "key": "babelLoaderCustomize", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-autoprefixer": {"id": "virtual: config-autoprefixer", "key": "autoprefixer", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-autoCSSModules": {"id": "virtual: config-autoCSSModules", "key": "autoCSSModules", "config": {"default": true}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-alias": {"id": "virtual: config-alias", "key": "alias", "config": {"default": {"umi": "@@/exports", "react": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat\\node_modules\\react", "react-dom": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat\\node_modules\\react-dom", "react-router": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat\\node_modules\\react-router", "react-router-dom": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat\\node_modules\\react-router-dom"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "./node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin", "key": "crossorigin"}, "./node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand": {"config": {}, "time": {"hooks": {"onStart": [1]}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand", "key": "deps<PERSON>n<PERSON><PERSON><PERSON>"}, "./node_modules/@umijs/preset-umi/dist/features/devTool/devTool": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/devTool/devTool.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/devTool/devTool", "key": "devTool"}, "./node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker": {"config": {}, "time": {"hooks": {}, "register": 80}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker", "key": "esbuildHelperChecker"}, "./node_modules/@umijs/preset-umi/dist/features/esmi/esmi": {"config": {}, "time": {"hooks": {}, "register": 226}, "enableBy": "config", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/esmi/esmi.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/esmi/esmi", "key": "esmi"}, "./node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic": {"config": {}, "time": {"hooks": {}, "register": 29}, "enableBy": "config", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic", "key": "exportStatic"}, "./node_modules/@umijs/preset-umi/dist/features/favicons/favicons": {"config": {}, "time": {"hooks": {"modifyAppData": [0]}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/favicons/favicons.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/favicons/favicons", "key": "favicons"}, "./node_modules/@umijs/preset-umi/dist/features/helmet/helmet": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/helmet/helmet.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/helmet/helmet", "key": "helmet"}, "./node_modules/@umijs/preset-umi/dist/features/icons/icons": {"config": {}, "time": {"hooks": {"onCheckConfig": [0]}, "register": 5}, "enableBy": "config", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/icons/icons.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/icons/icons", "key": "icons"}, "./node_modules/@umijs/preset-umi/dist/features/mock/mock": {"config": {}, "time": {"hooks": {}, "register": 39}, "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/mock/mock.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/mock/mock", "key": "mock"}, "./node_modules/@umijs/preset-umi/dist/features/mpa/mpa": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "config", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/mpa/mpa.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/mpa/mpa", "key": "mpa"}, "./node_modules/@umijs/preset-umi/dist/features/okam/okam": {"config": {}, "time": {"hooks": {}, "register": 2}, "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/okam/okam.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/okam/okam", "key": "okam"}, "./node_modules/@umijs/preset-umi/dist/features/overrides/overrides": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/overrides/overrides.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/overrides/overrides", "key": "overrides"}, "./node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency", "key": "phantomDependency"}, "./node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill": {"config": {}, "time": {"hooks": {"modifyConfig": [2]}, "register": 5}, "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill", "key": "polyfill"}, "./node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill", "key": "publicPathPolyfill"}, "./node_modules/@umijs/preset-umi/dist/features/prepare/prepare": {"config": {}, "time": {"hooks": {}, "register": 4}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/prepare/prepare.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/prepare/prepare", "key": "prepare"}, "./node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch", "key": "routePrefetch"}, "./node_modules/@umijs/preset-umi/dist/features/terminal/terminal": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/terminal/terminal.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/terminal/terminal", "key": "terminal"}, "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles": {"config": {}, "time": {"hooks": {}, "register": 12}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles", "key": "tmpFiles"}, "./node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader": {"config": {}, "time": {"hooks": {}, "register": 4}, "enableBy": "config", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader", "key": "clientLoader"}, "./node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps": {"config": {}, "time": {"hooks": {}, "register": 2}, "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps", "key": "routeProps"}, "./node_modules/@umijs/preset-umi/dist/features/ssr/ssr": {"config": {}, "time": {"hooks": {}, "register": 7}, "enableBy": "config", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/ssr/ssr.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/ssr/ssr", "key": "ssr"}, "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes": {"config": {}, "time": {"hooks": {}, "register": 9}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes", "key": "configTypes"}, "./node_modules/@umijs/preset-umi/dist/features/transform/transform": {"config": {}, "time": {"hooks": {}, "register": 7}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/transform/transform.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/transform/transform", "key": "transform"}, "./node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport": {"config": {}, "time": {"hooks": {}, "register": 5}, "enableBy": "config", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport", "key": "lowImport"}, "./node_modules/@umijs/preset-umi/dist/features/vite/vite": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/vite/vite.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/vite/vite", "key": "vite"}, "./node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute": {"config": {}, "time": {"hooks": {}, "register": 15}, "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute", "key": "apiRoute"}, "./node_modules/@umijs/preset-umi/dist/features/monorepo/redirect": {"config": {}, "time": {"hooks": {}, "register": 23}, "enableBy": "config", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/monorepo/redirect.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/monorepo/redirect", "key": "monorepoRedirect"}, "./node_modules/@umijs/preset-umi/dist/features/test/test": {"config": {}, "time": {"hooks": {}, "register": 3}, "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/test/test.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/test/test", "key": "test"}, "./node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent": {"config": {}, "time": {"hooks": {}, "register": 2}, "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent", "key": "clickToComponent"}, "./node_modules/@umijs/preset-umi/dist/features/legacy/legacy": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "config", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/legacy/legacy.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/legacy/legacy", "key": "legacy"}, "./node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose", "key": "classPropertiesLoose"}, "./node_modules/@umijs/preset-umi/dist/features/webpack/webpack": {"config": {}, "time": {"hooks": {}, "register": 3}, "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/webpack/webpack.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/webpack/webpack", "key": "preset-umi:webpack"}, "./node_modules/@umijs/preset-umi/dist/features/swc/swc": {"config": {}, "time": {"hooks": {"addOnDemandDeps": [0]}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/swc/swc.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/swc/swc", "key": "swc"}, "./node_modules/@umijs/preset-umi/dist/features/ui/ui": {"config": {}, "time": {"hooks": {}, "register": 6}, "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/ui/ui.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/ui/ui", "key": "ui"}, "./node_modules/@umijs/preset-umi/dist/features/mako/mako": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/mako/mako.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/mako/mako", "key": "mako"}, "./node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian": {"config": {}, "time": {"hooks": {}, "register": 5}, "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian", "key": "hm<PERSON><PERSON><PERSON><PERSON>"}, "./node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad": {"config": {}, "time": {"hooks": {}, "register": 4}, "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad", "key": "routePreloadOnLoad"}, "./node_modules/@umijs/preset-umi/dist/features/forget/forget": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/forget/forget.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/forget/forget", "key": "forget"}, "./node_modules/@umijs/preset-umi/dist/features/bundler/bundler": {"config": {}, "time": {"hooks": {}, "register": 4}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/features/bundler/bundler.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/features/bundler/bundler", "key": "preset-umi:bundler"}, "./node_modules/@umijs/preset-umi/dist/commands/build": {"config": {}, "time": {"hooks": {}, "register": 19}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/commands/build.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/commands/build", "key": "build"}, "./node_modules/@umijs/preset-umi/dist/commands/config/config": {"config": {}, "time": {"hooks": {}, "register": 50}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/commands/config/config.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/commands/config/config", "key": "config"}, "./node_modules/@umijs/preset-umi/dist/commands/dev/dev": {"config": {}, "time": {"hooks": {}, "register": 92}, "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/commands/dev/dev.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/commands/dev/dev", "key": "dev"}, "./node_modules/@umijs/preset-umi/dist/commands/help": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/commands/help.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/commands/help", "key": "help"}, "./node_modules/@umijs/preset-umi/dist/commands/lint": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/commands/lint.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/commands/lint", "key": "lint"}, "./node_modules/@umijs/preset-umi/dist/commands/setup": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/commands/setup.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/commands/setup", "key": "setup"}, "./node_modules/@umijs/preset-umi/dist/commands/deadcode": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/commands/deadcode.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/commands/deadcode", "key": "deadcode"}, "./node_modules/@umijs/preset-umi/dist/commands/version": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/commands/version.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/commands/version", "key": "version"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/page": {"config": {}, "time": {"hooks": {}, "register": 6}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/commands/generators/page.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/page", "key": "generator:page"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/prettier": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/commands/generators/prettier.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/prettier", "key": "generator:prettier"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig", "key": "generator:tsconfig"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/jest": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/commands/generators/jest.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/jest", "key": "generator:jest"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss", "key": "generator:tailwindcss"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/dva": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/commands/generators/dva.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/dva", "key": "generator:dva"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/component": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/commands/generators/component.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/component", "key": "generator:component"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/mock": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/commands/generators/mock.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/mock", "key": "generator:mock"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/cypress": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/commands/generators/cypress.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/cypress", "key": "generator:cypress"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/api": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/commands/generators/api.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/api", "key": "generator:api"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/precommit": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/commands/generators/precommit.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/precommit", "key": "generator:precommit"}, "./node_modules/@umijs/preset-umi/dist/commands/plugin": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/commands/plugin.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/commands/plugin", "key": "command:plugin"}, "./node_modules/@umijs/preset-umi/dist/commands/verify-commit": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/commands/verify-commit.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/commands/verify-commit", "key": "verifyCommit"}, "./node_modules/@umijs/preset-umi/dist/commands/preview": {"config": {}, "time": {"hooks": {}, "register": 28}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/commands/preview.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/commands/preview", "key": "preview"}, "./node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu", "key": "mfsu-cli"}, "@umijs/plugin-run": {"config": {}, "time": {"hooks": {}, "register": 7}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/plugin-run/dist/index.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "@umijs/plugin-run", "key": "run"}, "./node_modules/@umijs/core/dist/service/generatePlugin": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/core/dist/service/generatePlugin.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/core/dist/service/generatePlugin", "key": "generatePlugin"}, "./node_modules/@react-dev-inspector/umi4-plugin/lib/index": {"config": {}, "time": {"hooks": {}, "register": 18}, "enableBy": "register", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@react-dev-inspector/umi4-plugin/lib/index.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@react-dev-inspector/umi4-plugin/lib/index", "key": "inspectorConfig"}, "./node_modules/@umijs/plugins/dist/tailwindcss": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/node_modules/@umijs/plugins/dist/tailwindcss.js", "cwd": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat", "id": "./node_modules/@umijs/plugins/dist/tailwindcss", "key": "tailwindcss"}}, "presets": [], "name": "build", "args": {"_": []}, "userConfig": {"title": "易特克斯-聊天知识库", "outputPath": "chat", "alias": {"@parent": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb"}, "npmClient": "npm", "base": "/chat/", "routes": [{"path": "/", "component": "@/pages/chat", "layout": false}, {"path": "/test", "component": "@/components/chat-widget", "layout": false}, {"path": "/pie-chart-test", "component": "@/pages/pie-chart-test", "layout": false}], "publicPath": "/chat/", "esbuildMinifyIIFE": true, "icons": {}, "hash": true, "favicons": ["./logo.svg"], "clickToComponent": {}, "history": {"type": "browser"}, "plugins": ["@react-dev-inspector/umi4-plugin", "@umijs/plugins/dist/tailwindcss"], "jsMinifier": "none", "lessLoader": {"modifyVars": {"hack": "true; @import \"~@/less/index.less\";"}}, "devtool": false, "copy": [{"from": "src/conf.json", "to": "dist/conf.json"}], "proxy": [{"context": ["/aikb"], "target": "https://aikb.ddns.e-lead.cn", "changeOrigin": true, "ws": true, "logger": {}, "headers": {"X-API-Key": "b091d358d39747c394d150a712b1487d"}}], "tailwindcss": {}}, "mainConfigFile": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat\\.umirc.ts", "config": {"routeLoader": {"moduleType": "esm"}, "mountElementId": "root", "history": {"type": "browser"}, "base": "/chat/", "svgr": {}, "publicPath": "/chat/", "mfsu": {"strategy": "eager"}, "ignoreMomentLocale": true, "externals": {}, "autoCSSModules": true, "alias": {"umi": "@@/exports", "react": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat\\node_modules\\react", "react-dom": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat\\node_modules\\react-dom", "react-router": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat\\node_modules\\react-router", "react-router-dom": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat\\node_modules\\react-router-dom", "@parent": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb", "@": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/src", "@@": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/src/.umi-production", "regenerator-runtime": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat\\node_modules\\regenerator-runtime"}, "title": "易特克斯-聊天知识库", "outputPath": "chat", "npmClient": "npm", "routes": [{"path": "/", "component": "@/pages/chat", "layout": false}, {"path": "/test", "component": "@/components/chat-widget", "layout": false}, {"path": "/pie-chart-test", "component": "@/pages/pie-chart-test", "layout": false}], "esbuildMinifyIIFE": true, "icons": {}, "hash": true, "favicons": ["./logo.svg"], "clickToComponent": {}, "plugins": ["@react-dev-inspector/umi4-plugin", "@umijs/plugins/dist/tailwindcss"], "jsMinifier": "none", "lessLoader": {"modifyVars": {"hack": "true; @import \"~@/less/index.less\";"}}, "devtool": false, "copy": [{"from": "src/conf.json", "to": "dist/conf.json"}], "proxy": [{"context": ["/aikb"], "target": "https://aikb.ddns.e-lead.cn", "changeOrigin": true, "ws": true, "headers": {"X-API-Key": "b091d358d39747c394d150a712b1487d"}}], "tailwindcss": {}, "targets": {"chrome": 80}}, "routes": {"1": {"path": "/", "layout": false, "file": "@/pages/chat/index.tsx", "id": "1", "absPath": "/", "__content": "import RenameModal from '@/components/rename-modal';\r\nimport { DeleteOutlined, EditOutlined, AlertOutlined, ApiOutlined } from '@ant-design/icons';\r\nimport {\r\n  Avatar,\r\n  Button,\r\n  Card,\r\n  Divider,\r\n  Dropdown,\r\n  Flex,\r\n  MenuProps,\r\n  Space,\r\n  Spin,\r\n  Tag,\r\n  Typography,\r\n} from 'antd';\r\nimport { MenuItemProps } from 'antd/lib/menu/MenuItem';\r\nimport classNames from 'classnames';\r\nimport React, { useCallback, useEffect, useState, useRef } from 'react';\r\nimport ChatConfigurationModal from './chat-configuration-modal';\r\nimport ChatContainer from './chat-container';\r\nimport {\r\n  useDeleteConversation,\r\n  useDeleteDialog,\r\n  useEditDialog,\r\n  useHandleItemHover,\r\n  useRenameConversation,\r\n  useSelectDerivedConversationList,\r\n} from './hooks';\r\n\r\nimport EmbedModal from '@/components/api-service/embed-modal';\r\nimport { useShowEmbedModal } from '@/components/api-service/hooks';\r\nimport SvgIcon from '@/components/svg-icon';\r\nimport { useTheme } from '@/components/theme-provider';\r\nimport { SharedFrom } from '@/constants/chat';\r\nimport {\r\n  useClickConversationCard,\r\n  useClickDialogCard,\r\n  useFetchNextDialogList,\r\n  useGetChatSearchParams,\r\n} from '@/hooks/chat-hooks';\r\nimport { useTranslate } from '@/hooks/common-hooks';\r\nimport { useSetSelectedRecord } from '@/hooks/logic-hooks';\r\nimport { IConversation, IDialog } from '@/interfaces/database/chat';\r\nimport { MoreHorizontal, Plus } from 'lucide-react';\r\nimport styles from './index.less';\r\n\r\nconst { Text } = Typography;\r\n\r\nconst Chat = () => {\r\n  const { data: dialogList, loading: dialogLoading } = useFetchNextDialogList();\r\n  const { onRemoveDialog } = useDeleteDialog();\r\n  const { onRemoveConversation } = useDeleteConversation();\r\n  const { handleClickDialog } = useClickDialogCard();\r\n  const { handleClickConversation } = useClickConversationCard();\r\n  const { dialogId, conversationId } = useGetChatSearchParams();\r\n  const { theme } = useTheme();\r\n  const autoSelect = useRef(true);\r\n  const {\r\n    list: conversationList,\r\n    addTemporaryConversation,\r\n    loading: conversationLoading,\r\n  } = useSelectDerivedConversationList(autoSelect.current);\r\n  const { activated, handleItemEnter, handleItemLeave } = useHandleItemHover();\r\n  const {\r\n    activated: conversationActivated,\r\n    handleItemEnter: handleConversationItemEnter,\r\n    handleItemLeave: handleConversationItemLeave,\r\n  } = useHandleItemHover();\r\n  const {\r\n    conversationRenameLoading,\r\n    initialConversationName,\r\n    onConversationRenameOk,\r\n    conversationRenameVisible,\r\n    hideConversationRenameModal,\r\n    showConversationRenameModal,\r\n  } = useRenameConversation();\r\n  const {\r\n    dialogSettingLoading,\r\n    initialDialog,\r\n    onDialogEditOk,\r\n    dialogEditVisible,\r\n    clearDialog,\r\n    hideDialogEditModal,\r\n    showDialogEditModal,\r\n  } = useEditDialog();\r\n  const { t } = useTranslate('chat');\r\n  const { currentRecord, setRecord } = useSetSelectedRecord<IDialog>();\r\n  const [controller, setController] = useState(new AbortController());\r\n  const { showEmbedModal, hideEmbedModal, embedVisible, beta } =\r\n    useShowEmbedModal();\r\n  const handleDialogEdit = useCallback(async (dialog: IDialog) => {\r\n    autoSelect.current = false;\r\n    await onDialogEditOk(dialog);\r\n    autoSelect.current = true;\r\n  }, [onDialogEditOk])\r\n  const handleAppCardEnter = (id: string) => () => {\r\n    handleItemEnter(id);\r\n  };\r\n\r\n  const handleConversationCardEnter = (id: string) => () => {\r\n    handleConversationItemEnter(id);\r\n  };\r\n\r\n  const handleShowChatConfigurationModal =\r\n    (dialogId?: string): any =>\r\n    (info: any) => {\r\n      info?.domEvent?.preventDefault();\r\n      info?.domEvent?.stopPropagation();\r\n      showDialogEditModal(dialogId);\r\n    };\r\n\r\n  const handleRemoveDialog =\r\n    (dialogId: string): MenuItemProps['onClick'] =>\r\n    ({ domEvent }) => {\r\n      domEvent.preventDefault();\r\n      domEvent.stopPropagation();\r\n      onRemoveDialog(dialogId);\r\n    };\r\n\r\n  const handleShowOverviewModal =\r\n    (dialog: IDialog): any =>\r\n    (info: any) => {\r\n      info?.domEvent?.preventDefault();\r\n      info?.domEvent?.stopPropagation();\r\n      setRecord(dialog);\r\n      showEmbedModal();\r\n    };\r\n\r\n  const handleRemoveConversation =\r\n    (conversationId: string): MenuItemProps['onClick'] =>\r\n    ({ domEvent }) => {\r\n      domEvent.preventDefault();\r\n      domEvent.stopPropagation();\r\n      onRemoveConversation(conversationId);\r\n    };\r\n\r\n  const handleShowConversationRenameModal =\r\n    (conversationId: string, conversation: IConversation): MenuItemProps['onClick'] =>\r\n    ({ domEvent }) => {\r\n      domEvent.preventDefault();\r\n      domEvent.stopPropagation();\r\n      showConversationRenameModal(conversationId, conversation);\r\n    };\r\n\r\n  const handleDialogCardClick = useCallback(\r\n    (dialogId: string) => () => {\r\n      handleClickDialog(dialogId);\r\n    },\r\n    [handleClickDialog],\r\n  );\r\n\r\n  const handleConversationCardClick = useCallback(\r\n    (conversationId: string, isNew: boolean) => () => {\r\n      handleClickConversation(conversationId, isNew ? 'true' : '');\r\n      setController((pre) => {\r\n        pre.abort();\r\n        return new AbortController();\r\n      });\r\n    },\r\n    [handleClickConversation],\r\n  );\r\n\r\n  const handleCreateTemporaryConversation = useCallback(() => {\r\n    addTemporaryConversation();\r\n  }, [addTemporaryConversation]);\r\n\r\n  const buildAppItems = (dialog: IDialog) => {\r\n    const dialogId = dialog.id;\r\n\r\n    const appItems: MenuProps['items'] = [\r\n      {\r\n        key: '1',\r\n        onClick: handleShowChatConfigurationModal(dialogId),\r\n        label: (\r\n          <Space>\r\n            <EditOutlined />\r\n            {t('edit', { keyPrefix: 'common' })}\r\n          </Space>\r\n        ),\r\n      },\r\n      { type: 'divider' },\r\n      {\r\n        key: '2',\r\n        onClick: handleRemoveDialog(dialogId),\r\n        label: (\r\n          <Space>\r\n            <DeleteOutlined />\r\n            {t('delete', { keyPrefix: 'common' })}\r\n          </Space>\r\n        ),\r\n      },\r\n      // { type: 'divider' },\r\n      // {\r\n      //   key: '3',\r\n      //   onClick: handleShowOverviewModal(dialog),\r\n      //   label: (\r\n      //     <Space>\r\n      //       {/* <KeyOutlined /> */}\r\n      //       <PictureInPicture2 className=\"size-4\" />\r\n      //       {t('embedIntoSite', { keyPrefix: 'common' })}\r\n      //     </Space>\r\n      //   ),\r\n      // },\r\n    ];\r\n\r\n    return appItems;\r\n  };\r\n\r\n  const buildConversationItems = (conversationId: string, x: IConversation) => {\r\n    const appItems: MenuProps['items'] = [\r\n      {\r\n        key: '1',\r\n        onClick: handleShowConversationRenameModal(conversationId, x),\r\n        label: (\r\n          <Space>\r\n            <EditOutlined />\r\n            {t('rename', { keyPrefix: 'common' })}\r\n          </Space>\r\n        ),\r\n      },\r\n      { type: 'divider' },\r\n      {\r\n        key: '2',\r\n        onClick: handleRemoveConversation(conversationId),\r\n        label: (\r\n          <Space>\r\n            <DeleteOutlined />\r\n            {t('delete', { keyPrefix: 'common' })}\r\n          </Space>\r\n        ),\r\n      },\r\n    ];\r\n\r\n    return appItems;\r\n  };\r\n\r\n  useEffect(() => {\r\n    if((!dialogId && dialogList.length > 0) || (dialogId && dialogList.length > 0 && dialogList.every(x => x.id !== dialogId))) {\r\n      handleClickDialog(dialogList[0].id);\r\n    }\r\n  }, [dialogList, dialogId, handleClickDialog]);\r\n  // useEffect(() => {\r\n  //   if( !conversationId && conversationList.length > 0) {\r\n  //     handleConversationCardClick(conversationList[0].id, false);\r\n  //   }\r\n  // }, [conversationList]);\r\n  return (\r\n    <Flex className={styles.chatWrapper}>\r\n      <Flex className={styles.chatAppWrapper}>\r\n        <Flex flex={1} vertical>\r\n          <Button type=\"primary\" onClick={handleShowChatConfigurationModal()}>\r\n            {t('createAssistant')}\r\n          </Button>\r\n          <Divider></Divider>\r\n          <Flex className={styles.chatAppContent} vertical gap={10}>\r\n            <Spin spinning={dialogLoading} wrapperClassName={styles.chatSpin}>\r\n              {dialogList.map((x) => (\r\n                <Card\r\n                  key={x.id}\r\n                  hoverable\r\n                  className={classNames(styles.chatAppCard, {\r\n                    [theme === 'dark'\r\n                      ? styles.chatAppCardSelectedDark\r\n                      : styles.chatAppCardSelected]: dialogId === x.id,\r\n                  })}\r\n                  onMouseEnter={handleAppCardEnter(x.id)}\r\n                  onMouseLeave={handleItemLeave}\r\n                  onClick={handleDialogCardClick(x.id)}\r\n                >\r\n                  <Flex justify=\"space-between\" align=\"center\">\r\n                    <Space size={15}>\r\n                      {x.icon ? <Avatar src={x.icon} shape={'square'} /> :\r\n                        x.ragflowKnowledgeBaseIds && x.ragflowKnowledgeBaseIds.length > 0 ?\r\n                          <Avatar className={styles.ragflowIcon} icon={<AlertOutlined />} shape={'square'} /> :\r\n                          <Avatar icon={<ApiOutlined />} shape={'square'} /> }\r\n                      <section>\r\n                        <b>\r\n                          <Text\r\n                            ellipsis={{ tooltip: x.name }}\r\n                            style={{ width: 130 }}\r\n                          >\r\n                            {x.name}\r\n                          </Text>\r\n                        </b>\r\n                        <div>{x.description}</div>\r\n                      </section>\r\n                    </Space>\r\n                    {activated === x.id && (\r\n                      <section>\r\n                        <Dropdown menu={{ items: buildAppItems(x) }}>\r\n                          <MoreHorizontal className=\"tw-h-4 tw-w-4\" />\r\n                        </Dropdown>\r\n                      </section>\r\n                    )}\r\n                  </Flex>\r\n                </Card>\r\n              ))}\r\n            </Spin>\r\n          </Flex>\r\n        </Flex>\r\n      </Flex>\r\n      <Divider type={'vertical'} className={styles.divider}></Divider>\r\n      <Flex className={styles.chatTitleWrapper}>\r\n        <Flex flex={1} vertical>\r\n          <Flex\r\n            justify={'space-between'}\r\n            align=\"center\"\r\n            className={styles.chatTitle}\r\n          >\r\n            <Space>\r\n              <b>{t('chat')}</b>\r\n              <Tag>{conversationList.length}</Tag>\r\n            </Space>\r\n            <Button\r\n              type=\"text\"\r\n              className=\"tw-h-8 tw-w-8 tw-text-slate-600 hover:tw-text-slate-800\"\r\n              icon={<Plus className=\"tw-h-4 tw-w-4\"/>}\r\n              onClick={handleCreateTemporaryConversation}\r\n            />\r\n          </Flex>\r\n          <Divider></Divider>\r\n          <Flex vertical gap={10} className={styles.chatTitleContent}>\r\n            <Spin\r\n              spinning={conversationLoading}\r\n              wrapperClassName={styles.chatSpin}\r\n            >\r\n              {conversationList.map((x) => (\r\n                <Card\r\n                  key={x.id}\r\n                  hoverable\r\n                  onClick={handleConversationCardClick(x.id, x.is_new)}\r\n                  onMouseEnter={handleConversationCardEnter(x.id)}\r\n                  onMouseLeave={handleConversationItemLeave}\r\n                  className={classNames(styles.chatTitleCard, {\r\n                    [theme === 'dark'\r\n                      ? styles.chatTitleCardSelectedDark\r\n                      : styles.chatTitleCardSelected]: x.id === conversationId,\r\n                  })}\r\n                >\r\n                  <Flex justify=\"space-between\" align=\"center\">\r\n                    <div>\r\n                      <Text\r\n                        ellipsis={{ tooltip: x.name }}\r\n                        style={{ width: 150 }}\r\n                      >\r\n                        {x.name}\r\n                      </Text>\r\n                    </div>\r\n                    {conversationActivated === x.id &&\r\n                      x.id !== '' &&\r\n                      !x.is_new && (\r\n                        <section>\r\n                          <Dropdown\r\n                            menu={{ items: buildConversationItems(x.id, x) }}\r\n                          >\r\n                            <MoreHorizontal className=\"tw-h-4 tw-w-4\" />\r\n                          </Dropdown>\r\n                        </section>\r\n                      )}\r\n                  </Flex>\r\n                </Card>\r\n              ))}\r\n            </Spin>\r\n          </Flex>\r\n        </Flex>\r\n      </Flex>\r\n      <Divider type={'vertical'} className={styles.divider}></Divider>\r\n      <ChatContainer controller={controller} setController={setController}></ChatContainer>\r\n      {dialogEditVisible && (\r\n        <ChatConfigurationModal\r\n          visible={dialogEditVisible}\r\n          initialDialog={initialDialog}\r\n          showModal={showDialogEditModal}\r\n          hideModal={hideDialogEditModal}\r\n          loading={dialogSettingLoading}\r\n          onOk={handleDialogEdit}\r\n          clearDialog={clearDialog}\r\n        ></ChatConfigurationModal>\r\n      )}\r\n      <RenameModal\r\n        visible={conversationRenameVisible}\r\n        hideModal={hideConversationRenameModal}\r\n        onOk={onConversationRenameOk}\r\n        initialName={initialConversationName}\r\n        loading={conversationRenameLoading}\r\n      ></RenameModal>\r\n\r\n      {embedVisible && (\r\n        <EmbedModal\r\n          visible={embedVisible}\r\n          hideModal={hideEmbedModal}\r\n          token={currentRecord.id}\r\n          form={SharedFrom.Chat}\r\n          beta={beta}\r\n          isAgent={false}\r\n        ></EmbedModal>\r\n      )}\r\n    </Flex>\r\n  );\r\n};\r\n\r\nexport default Chat;\r\n", "__isJSFile": true, "__absFile": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/src/pages/chat/index.tsx"}, "2": {"path": "/test", "layout": false, "file": "@/components/chat-widget/index.tsx", "id": "2", "absPath": "/test", "__content": "import React, { useState, useCallback, useEffect, memo, useRef } from 'react';\r\nimport { Button, Drawer, Space, Typography, Popover,  List, Spin, Avatar } from 'antd';\r\nimport { X, Plus, History, Minimize2, Maximize2, Bot } from 'lucide-react';\r\nimport MessageInput from './message-input';\r\nimport MessageItem from './message-item';\r\nimport styles from './index.module.less';\r\nimport {\r\n  useFetchNextConversation,\r\n  useHandleDialog,\r\n  useHandleMessageInputChange,\r\n  useSendButtonDisabled,\r\n  useFindPrologueFromDialogList,\r\n  useSetConversation\r\n} from './hook';\r\nimport trim from 'lodash/trim';\r\nimport { v4 as uuid } from 'uuid';\r\nimport { MessageType } from '@/constants/chat';\r\nimport { useSelectDerivedMessages, useSendMessageWithSse } from './hook';\r\nimport {  Message } from '@/interfaces/database/chat';\r\nimport api from '@/utils/api';\r\nimport { useSelectDerivedConversationList } from './hook';\r\nimport { IMessage } from '@/pages/chat/interface';\r\nimport { useRemoveNextConversation } from './hook';\r\nimport { DeleteOutlined } from '@ant-design/icons';\r\nimport { useTranslate } from '@/hooks/common-hooks';\r\nimport { useRegenerateMessage } from './hook';\r\nimport { ReactComponent as AiChatIcon } from '@/assets/svg/ai-chat-icon.svg';\r\n\r\nconst { Title } = Typography;\r\n\r\nexport interface ChatWidgetProps {\r\n  fullscreenClick?: () => void;\r\n  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';\r\n  drawerWidth?: number;\r\n  iconPosition?: { left: number; top: number };\r\n}\r\n\r\n// interface Message {\r\n//   id: string;\r\n//   role: 'user' | 'assistant';\r\n//   content: string;\r\n//   doc_ids?: string[];\r\n// }\r\n\r\ninterface ContextTag {\r\n  id: string;\r\n  name: string;\r\n}\r\n\r\nconst ChatWidget: React.FC<ChatWidgetProps> = ({\r\n  fullscreenClick,\r\n  position = 'bottom-right',\r\n  drawerWidth = 600,\r\n  iconPosition\r\n}) => {\r\n  const [visible, setVisible] = useState(false);\r\n  const [isFullscreen, setIsFullscreen] = useState(false);\r\n  const { dialogId, setDialogId, data: dialogDetail} = useHandleDialog();\r\n  const [historyVisible, setHistoryVisible] = useState(false);\r\n  const [contextTags, setContextTags] = useState<ContextTag[]>([]);\r\n  const [controller, setController] = useState(new AbortController());\r\n  const { handleInputChange, value, setValue } = useHandleMessageInputChange();\r\n  const iconRef = useRef<HTMLDivElement>(null);\r\n\r\n  const {\r\n    conversationId,\r\n    setConversationId,\r\n    list: conversationList,\r\n    addConversation,\r\n    loading: conversationListLoading,\r\n  } = useSelectDerivedConversationList();\r\n  const handleAddNewConversation = useCallback(async () => {\r\n    let newConversation = await addConversation();\r\n    setConversationId(newConversation.data);\r\n  }, [addConversation]);\r\n\r\n\r\n  const { data: conversation, loading: conversationLoading } = useFetchNextConversation(conversationId);\r\n  const {setConversation} = useSetConversation()\r\n  const stopOutputMessage = useCallback(() => {\r\n    controller.abort();\r\n    setController(new AbortController());\r\n  }, [controller]);\r\n  const { send, answer, done } = useSendMessageWithSse(\r\n    api.completeConversation,\r\n  );\r\n\r\n\r\n\r\n  const {\r\n    ref,\r\n    setDerivedMessages,\r\n    derivedMessages,\r\n    addNewestAnswer,\r\n    addNewestQuestion,\r\n    removeLatestMessage,\r\n    removeMessagesAfterCurrentMessage\r\n  } = useSelectDerivedMessages(visible);\r\n\r\n  const sendMessage = useCallback(\r\n    async ({\r\n             message,\r\n             currentConversationId,\r\n             messages,\r\n           }: {\r\n      message: Message;\r\n      currentConversationId?: string;\r\n      messages?: Message[];\r\n    }) => {\r\n      const res = await send(\r\n        {\r\n          conversationId: currentConversationId,\r\n          messages: [...(messages ?? derivedMessages ?? []), message],\r\n          question: message.content,\r\n          sessionId: currentConversationId,\r\n        },\r\n        controller,\r\n      );\r\n\r\n      if (res && (res?.response.status !== 200 || res?.data?.code !== 0)) {\r\n        setValue(message.content);\r\n        console.info('removeLatestMessage111');\r\n        removeLatestMessage();\r\n      }\r\n    },\r\n    [\r\n      derivedMessages,\r\n      removeLatestMessage,\r\n      setValue,\r\n      send,\r\n      controller,\r\n    ],\r\n  );\r\n  const { regenerateMessage } = useRegenerateMessage({\r\n    removeMessagesAfterCurrentMessage,\r\n    sendMessage,\r\n    messages: derivedMessages,\r\n    conversationId\r\n  });\r\n\r\n  const { t } = useTranslate('chat');\r\n  const handlePressEnterForWidget = useCallback(\r\n    async (question?: string, documentIds?: string[]) => {\r\n      let innerValue = question ||value;\r\n      if (trim(innerValue) === '') return;\r\n      const id = uuid();\r\n      if (done) {\r\n        addNewestQuestion({\r\n          content: innerValue,\r\n          doc_ids: documentIds,\r\n          id,\r\n          role: MessageType.User,\r\n        });\r\n        setValue('');\r\n        if (conversation.name !== t('newConversation')) {\r\n          sendMessage({ message: {\r\n              id,\r\n              content: innerValue.trim(),\r\n              role: MessageType.User,\r\n              doc_ids: documentIds,\r\n            }, currentConversationId: conversationId });\r\n        } else {\r\n          const data = await setConversation(\r\n            innerValue.trim(),\r\n            false,\r\n            conversationId,\r\n            dialogId\r\n          );\r\n          if (data.code === 0 || data.code === 200) {\r\n            sendMessage({ message: {\r\n                id,\r\n                content: innerValue.trim(),\r\n                role: MessageType.User,\r\n                doc_ids: documentIds,\r\n              }, currentConversationId: conversationId });\r\n          }\r\n        }\r\n      }\r\n    },\r\n    [addNewestQuestion, sendMessage, done, setValue, value, conversationId, conversation],\r\n  );\r\n  const handleRemoveContextTag = (tagId: string) => {\r\n    setContextTags((prev) => prev.filter((tag) => tag.id !== tagId));\r\n  };\r\n  const addContextTag = (tag: ContextTag) => {\r\n    setContextTags((prev) => [\r\n      ...prev,\r\n      {\r\n        ...tag\r\n      }\r\n    ])\r\n  }\r\n\r\n  useEffect(() => {\r\n    if (answer.answer && conversationId) {\r\n      addNewestAnswer(answer);\r\n    }\r\n  }, [answer, addNewestAnswer, conversationId]);\r\n\r\n\r\n\r\n  // 监听dialogId变化\r\n  // useEffect(() => {\r\n  //   loadTopics();\r\n  // }, [dialogId, loadTopics]);\r\n  const sendDisabled = useSendButtonDisabled(value);\r\n\r\n  useEffect(() => {\r\n    (window as any).chatWidget = {\r\n      ask:(question: string) => {\r\n        handlePressEnterForWidget(question, []);\r\n        setVisible(true);\r\n      },\r\n      show:() => {\r\n        setVisible(true);\r\n      },\r\n      hide:() => {\r\n        setVisible(false);\r\n      }\r\n    }\r\n  });\r\n\r\n  const handleIconClick = useCallback(() => {\r\n    setVisible(true);\r\n  }, []);\r\n\r\n  const prologue = useFindPrologueFromDialogList();\r\n  const addPrologue = useCallback(() => {\r\n    if (dialogId !== '' && conversationId !== '') {\r\n      const nextMessage = {\r\n        role: MessageType.Assistant,\r\n        content: prologue,\r\n        id: uuid(),\r\n      } as IMessage;\r\n\r\n      setDerivedMessages([nextMessage]);\r\n    }\r\n  }, [dialogId, prologue, setDerivedMessages, conversationId]);\r\n  useEffect(() => {\r\n    if (conversationId) {\r\n      if (conversation.message?.length > 0) {\r\n        setDerivedMessages(conversation.message || []);\r\n      } else {\r\n        addPrologue();\r\n      }\r\n    }\r\n  }, [conversation.message, conversationId, setDerivedMessages]);\r\n\r\n  const { removeConversation } = useRemoveNextConversation();\r\n  // console.log(\"conversationList\", conversationList)\r\n  const historyContent = (\r\n    <div className={styles.historyContent}>\r\n      <Spin spinning={conversationListLoading}>\r\n        <List\r\n          dataSource={conversationList}\r\n          renderItem={(conversation) => (\r\n            <List.Item\r\n              key={conversation.id}\r\n              onClick={(e) => {\r\n                setConversationId(conversation.id);\r\n                setHistoryVisible(false);\r\n              }}\r\n              className={styles.topicItem}\r\n            >\r\n              <div style={{ flex: 1, margin:\"0px 8px 0px 0px\" }}>\r\n                <div className=\"tw-text-xs text-slate-800\">{conversation.name}</div>\r\n                <div className=\"tw-text-xs text-slate-500\">\r\n                  {conversation.createTime\r\n                    ? new Date(conversation.createTime).toLocaleString()\r\n                    : ''}\r\n                </div>\r\n              </div>\r\n              <DeleteOutlined className=\"tw-text-red-500\"  onClick={ (e) => {\r\n                e.preventDefault();\r\n                e.stopPropagation();\r\n                removeConversation(conversation.id);\r\n                setHistoryVisible(false);\r\n              }}/>\r\n            </List.Item>\r\n          )}\r\n        />\r\n      </Spin>\r\n    </div>\r\n  );\r\n\r\n  const handleToggleFullscreen = useCallback(() => {\r\n    if(fullscreenClick) {\r\n      fullscreenClick();\r\n    } else {\r\n      setIsFullscreen(prev => !prev);\r\n    }\r\n  }, []);\r\n  const handleOpenChange = (newOpen: boolean) => {\r\n    setHistoryVisible(newOpen);\r\n  };\r\n\r\n  const handleCloseDrawer = useCallback(() => {\r\n    setVisible(false);\r\n  }, []);\r\n\r\n  return (\r\n    <div className={styles.chatWidget} >\r\n      {!visible && (\r\n        <div\r\n          ref={iconRef}\r\n          data-position={position}\r\n          className={styles.customChatIcon}\r\n          style={iconPosition ? { \r\n            left: iconPosition.left, \r\n            top: iconPosition.top, \r\n            position: 'fixed', \r\n            zIndex: 1100 \r\n          } : {}}\r\n          title=\"点击打开聊天\"\r\n          onClick={handleIconClick}\r\n        >\r\n          <AiChatIcon style={{ width: '24px', height: '24px' }} />\r\n        </div>\r\n      )}\r\n      <Drawer\r\n        zIndex={10000}\r\n        title={\r\n          <div className=\"tw-flex tw-items-center tw-space-x-3\">\r\n            <div className=\"tw-relative\">\r\n              <Avatar className=\"tw-bg-gradient-to-br tw-from-blue-500 tw-to-purple-600 tw-text-white tw-h-10 tw-w-10 tw-ring-2 tw-ring-blue-100\" icon={<Bot className=\"tw-h-5 tw-w-5\" />}>\r\n              </Avatar>\r\n              <div className=\"tw-absolute tw--bottom-1 tw--right-1 tw-h-4 tw-w-4 tw-bg-green-500 tw-rounded-full tw-border-2 tw-border-white tw-shadow-sm\"></div>\r\n            </div>\r\n            <div>\r\n              <h4 className=\"tw-font-semibold tw-text-slate-800\">易特克思AI助手</h4>\r\n              <p className=\"tw-text-xs tw-text-slate-500\">在线 · 随时为您服务</p>\r\n            </div>\r\n          </div>\r\n        }\r\n        placement=\"right\"\r\n        onClose={handleCloseDrawer}\r\n        open={visible}\r\n        width={isFullscreen ? '100%' : drawerWidth}\r\n        closeIcon={false}\r\n        mask={false}\r\n        className={isFullscreen ? styles.fullscreenDrawer : ''}\r\n        extra={\r\n          <Space>\r\n            <Button\r\n              type=\"text\"\r\n              className=\"tw-h-8 tw-w-8 tw-text-slate-600 hover:tw-text-slate-800\"\r\n              icon={<Plus className=\"tw-h-4 tw-w-4\"/>}\r\n              onClick={handleAddNewConversation}\r\n            />\r\n            <Popover\r\n              zIndex={10000}\r\n              content={historyContent}\r\n              open={historyVisible}\r\n              onOpenChange={handleOpenChange}\r\n              trigger=\"click\"\r\n            >\r\n              <Button\r\n                type=\"text\"\r\n                className=\"tw-h-8 tw-w-8 tw-text-slate-600 hover:tw-text-slate-800\"\r\n                icon={<History className=\"tw-h-4 tw-w-4\"/>}\r\n              />\r\n            </Popover>\r\n            <Button\r\n              type=\"text\"\r\n              className=\"tw-h-8 tw-w-8 tw-text-slate-600 hover:tw-text-slate-800\"\r\n              icon={isFullscreen ? <Minimize2 className=\"tw-h-4 tw-w-4\"/> : <Maximize2 className=\"tw-h-4 tw-w-4\"/>}\r\n              onClick={handleToggleFullscreen}\r\n            />\r\n            <Button\r\n              type=\"text\"\r\n              className=\"tw-h-8 tw-w-8 tw-text-slate-600 hover:tw-text-slate-800\"\r\n              icon={<X className=\"tw-h-4 tw-w-4\"/>}\r\n              onClick={handleCloseDrawer}\r\n            />\r\n          </Space>\r\n        }\r\n      >\r\n        <Spin spinning={conversationLoading} wrapperClassName=\"wgq\" style={{height: '100%'}}>\r\n        <div className={styles.chatContainer}>\r\n          <div className={styles.messageList}>\r\n            {derivedMessages.map((message, index) => (\r\n              <MessageItem\r\n                key={message.id}\r\n                item={message}\r\n                loading={!done}\r\n                index={index}\r\n                reference={message.reference}\r\n                regenerateMessage={regenerateMessage}\r\n              />\r\n            ))}\r\n            <div ref={ref} />\r\n          </div>\r\n          <div className={styles.inputContainer}>\r\n            <MessageInput\r\n              sendLoading={!done}\r\n              conversationId={conversationId || 'default'}\r\n              disabled={!conversationId}\r\n              sendDisabled={sendDisabled}\r\n              value={value}\r\n              onInputChange={handleInputChange}\r\n              onPressEnter={handlePressEnterForWidget}\r\n              stopOutputMessage={stopOutputMessage}\r\n              contextTags={contextTags}\r\n              onRemoveContextTag={handleRemoveContextTag}\r\n            />\r\n          </div>\r\n        </div>\r\n        </Spin>\r\n      </Drawer>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default memo(ChatWidget);", "__isJSFile": true, "__absFile": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/src/components/chat-widget/index.tsx"}, "3": {"path": "/pie-chart-test", "layout": false, "file": "@/pages/pie-chart-test/index.tsx", "id": "3", "absPath": "/pie-chart-test", "__content": "import React from 'react';\r\nimport PieChartTest from '@/components/chat-widget/markdown-content/pie-chart-test';\r\n\r\nconst PieChartTestPage: React.FC = () => {\r\n  return (\r\n    <div style={{ \r\n      minHeight: '100vh', \r\n      backgroundColor: '#f5f5f5',\r\n      overflow: 'auto',\r\n      maxHeight: '100vh'\r\n    }}>\r\n      <PieChartTest />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PieChartTestPage; ", "__isJSFile": true, "__absFile": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/src/pages/pie-chart-test/index.tsx"}, "@@/global-layout": {"id": "@@/global-layout", "path": "/", "file": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/src/layouts/index.tsx", "absPath": "/", "isLayout": true, "__absFile": "D:/Work/AI Knowledge/code/etrx-ai-kb/chat/src/layouts/index.tsx"}}, "apiRoutes": {}, "hasSrcDir": true, "npmClient": "npm", "umi": {"version": "4.4.11", "name": "<PERSON><PERSON>", "importSource": "umi", "cliName": "umi"}, "bundleStatus": {"done": false}, "mfsuBundleStatus": {"done": false}, "react": {"version": "18.3.1", "path": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat\\node_modules\\react"}, "react-dom": {"version": "18.3.1", "path": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat\\node_modules\\react-dom"}, "appJS": {"path": "D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat\\src\\app.tsx", "exports": ["rootContainer"]}, "locale": "zh-CN", "globalCSS": ["D:\\Work\\AI Knowledge\\code\\etrx-ai-kb\\chat\\src\\global.less"], "globalJS": [], "overridesCSS": [], "bundler": "webpack", "git": {"originUrl": "http://gitlab.ddns.e-lead.cn/liutao/etrx-ai-kb"}, "framework": "react", "typescript": {"tsVersion": "5.8.3", "tslibVersion": "2.8.1"}}