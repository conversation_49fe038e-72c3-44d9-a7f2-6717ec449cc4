-- 测试迁移脚本
-- 添加测试表用于验证Flyway迁移
CREATE TABLE IF NOT EXISTS `tb_test_flyway` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '测试名称',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Flyway测试表';

-- 添加测试数据
INSERT INTO `tb_test_flyway` (`name`) VALUES ('Flyway迁移测试数据');