package com.etrx.kb.service;

import com.etrx.kb.common.PageResult;
import com.etrx.kb.dto.KnowledgeBaseDTO;
import com.etrx.kb.dto.KnowledgeBaseMemberDTO;
import com.etrx.kb.dto.DashboardStatsDTO;

import java.util.List;

/**
 * 知识库服务接口
 */
public interface KnowledgeBaseService {

    /**
     * 创建知识库
     *
     * @param userId 创建者ID
     * @param dto    知识库信息
     * @return 知识库信息
     */
    KnowledgeBaseDTO createKnowledgeBase(Long userId, KnowledgeBaseDTO dto);

    /**
     * 更新知识库
     *
     * @param id  知识库ID
     * @param dto 知识库信息
     * @return 知识库信息
     */
    KnowledgeBaseDTO updateKnowledgeBase(Long id, KnowledgeBaseDTO dto);

    /**
     * 获取知识库信息
     *
     * @param id 知识库ID
     * @return 知识库信息
     */
    KnowledgeBaseDTO getKnowledgeBase(Long id);

    /**
     * 删除知识库
     *
     * @param id     知识库ID
     * @param userId 操作用户ID
     */
    void deleteKnowledgeBase(Long id, Long userId);

    /**
     * 分页查询知识库列表
     *
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @param keyword  搜索关键字
     * @param type     查询类型：null-全部，my-我创建的，share-我参与的
     * @param userId   用户ID
     * @return 知识库分页列表
     */
    PageResult<KnowledgeBaseDTO> listKnowledgeBases(Integer pageNum, Integer pageSize, String keyword, String type, Long userId);

    /**
     * 获取用户有权限的知识库列表
     *
     * @param userId 用户ID
     * @return 知识库列表
     */
    List<KnowledgeBaseDTO> getUserKnowledgeBases(Long userId);

    /**
     * 添加知识库成员
     *
     * @param operatorId 操作者ID
     * @param dto        成员信息
     */
    void addMember(Long operatorId, KnowledgeBaseMemberDTO dto);

    /**
     * 更新知识库成员角色
     *
     * @param operatorId 操作者ID
     * @param dto        成员信息
     */
    void updateMemberRole(Long operatorId, KnowledgeBaseMemberDTO dto);

    /**
     * 移除知识库成员
     *
     * @param operatorId 操作者ID
     * @param kbId      知识库ID
     * @param userId    要移除的用户ID
     */
    void removeMember(Long operatorId, Long kbId, Long userId);

    /**
     * 获取知识库成员列表
     *
     * @param kbId 知识库ID
     * @return 成员列表
     */
    List<KnowledgeBaseMemberDTO> listMembers(Long kbId);

    /**
     * 检查用户是否有权限访问知识库
     *
     * @param kbId   知识库ID
     * @param userId 用户ID
     * @param role   最小需要的角色级别
     * @return 是否有权限
     */
    boolean checkPermission(Long kbId, Long userId, Integer role);

    /**
     * 转移知识库所有权
     *
     * @param kbId      知识库ID
     * @param operatorId 当前所有者ID
     * @param newOwnerId 新所有者ID
     */
    void transferOwnership(Long kbId, Long operatorId, Long newOwnerId);

    /**
     * 更新知识库状态
     *
     * @param id     知识库ID
     * @param status 状态(0:禁用,1:启用)
     */
    void updateStatus(Long id, Integer status);

    /**
     * 获取数据看板统计信息
     *
     * @param userId 用户ID
     * @return 数据看板统计信息
     */
    DashboardStatsDTO getDashboardStats(Long userId);

    /**
     * 获取知识库ID和名称列表
     *
     * @param keyword 搜索关键字
     * @param userId 用户ID
     * @return 知识库ID和名称列表
     */
    List<KnowledgeBaseDTO> listKnowledgeBaseNames(String keyword, Long userId);
}