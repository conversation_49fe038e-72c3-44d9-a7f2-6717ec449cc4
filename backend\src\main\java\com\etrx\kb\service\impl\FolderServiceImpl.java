package com.etrx.kb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.etrx.kb.common.Constants;
import com.etrx.kb.domain.Folder;
import com.etrx.kb.domain.FileNode;
import com.etrx.kb.domain.KbFileRel;
import com.etrx.kb.dto.FolderDTO;
import com.etrx.kb.dto.FolderTreeDTO;
import com.etrx.kb.exception.ApiException;
import com.etrx.kb.mapper.FolderMapper;
import com.etrx.kb.mapper.KbFileRelMapper;
import com.etrx.kb.service.FolderService;
import com.etrx.kb.service.KnowledgeBaseService;
import com.etrx.kb.service.FileService;
import lombok.RequiredArgsConstructor;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 文件夹服务实现类
 */
@Service
@RequiredArgsConstructor
public class FolderServiceImpl implements FolderService {

    private final FolderMapper folderMapper;
    private final KbFileRelMapper kbFileRelMapper;
    private final FileService fileService;
    private final KnowledgeBaseService knowledgeBaseService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FolderDTO createFolder(Long kbId, Long parentId, String name, Long creatorId) {
        // 检查用户是否有权限创建文件夹
        if (!knowledgeBaseService.checkPermission(kbId, creatorId, Constants.KnowledgeBaseMemberRole.MEMBER)) {
            throw new ApiException("没有权限在该知识库创建文件夹");
        }

        // 检查父文件夹是否存在
        if (parentId != null && folderMapper.selectById(parentId) == null) {
            throw new ApiException("父文件夹不存在");
        }

        // 检查文件夹名称是否已存在
        LambdaQueryWrapper<Folder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Folder::getKbId, kbId)
                .eq(Folder::getParentId, parentId)
                .eq(Folder::getName, name);
        if (folderMapper.selectCount(queryWrapper) > 0) {
            throw new ApiException("该名称的文件夹已存在");
        }

        // 创建文件夹
        Folder folder = new Folder();
        folder.setKbId(kbId);
        folder.setName(name);
        folder.setParentId(parentId);
        folder.setCreatorId(creatorId);
        folder.setCreateTime(LocalDateTime.now());
        folder.setUpdateTime(LocalDateTime.now());

        folderMapper.insert(folder);

        // 返回文件夹信息
        FolderDTO dto = new FolderDTO();
        BeanUtils.copyProperties(folder, dto);
        return dto;
    }

    @Override
    public FolderDTO getFolder(Long id) {
        Folder folder = folderMapper.selectById(id);
        if (folder == null) {
            throw new ApiException("文件夹不存在");
        }

        FolderDTO dto = new FolderDTO();
        BeanUtils.copyProperties(folder, dto);
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteFolder(Long id, Long operatorId) {
        Folder folder = folderMapper.selectById(id);
        if (folder == null) {
            throw new ApiException("文件夹不存在");
        }

        // 检查用户是否有权限删除文件夹
        if (!knowledgeBaseService.checkPermission(folder.getKbId(), operatorId, Constants.KnowledgeBaseMemberRole.ADMIN)) {
            throw new ApiException("没有权限删除该文件夹");
        }

        // 递归删除子文件夹和文档
        deleteFolderRecursive(id, folder.getKbId(), operatorId);
    }

    /**
     * 递归删除文件夹及其内容
     */
    private void deleteFolderRecursive(Long folderId, Long kbId, Long operatorId) {
        // 删除子文件夹
        List<Folder> subFolders = folderMapper.findByParentId(folderId);
        for (Folder subFolder : subFolders) {
            deleteFolderRecursive(subFolder.getId(), kbId, operatorId);
        }

        // 删除文件夹中的文件
        LambdaQueryWrapper<FileNode> fileWrapper = new LambdaQueryWrapper<>();
        fileWrapper.eq(FileNode::getParentId, folderId);
        List<FileNode> files = fileService.list(fileWrapper);
        
        for (FileNode file : files) {
            // 删除文件与知识库的关联关系
            LambdaQueryWrapper<KbFileRel> relWrapper = new LambdaQueryWrapper<>();
            relWrapper.eq(KbFileRel::getKbId, kbId)
                     .eq(KbFileRel::getFileNodeId, file.getId());
            kbFileRelMapper.delete(relWrapper);
            
            // 删除文件节点
            fileService.deleteFile(file.getId(), operatorId);
        }

        // 删除文件夹本身
        folderMapper.deleteById(folderId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void moveFolder(Long id, Long parentId, Long operatorId) {
        Folder folder = folderMapper.selectById(id);
        if (folder == null) {
            throw new ApiException("文件夹不存在");
        }

        // 检查用户是否有权限移动文件夹
        if (!knowledgeBaseService.checkPermission(folder.getKbId(), operatorId, Constants.KnowledgeBaseMemberRole.MEMBER)) {
            throw new ApiException("没有权限移动该文件夹");
        }

        // 检查目标文件夹是否存在
        if (parentId != null && folderMapper.selectById(parentId) == null) {
            throw new ApiException("目标文件夹不存在");
        }

        // 检查是否移动到自己的子文件夹
        if (parentId != null && isSubFolder(id, parentId)) {
            throw new ApiException("不能将文件夹移动到自己的子文件夹中");
        }

        // 更新文件夹的父文件夹ID
        folder.setParentId(parentId);
        folder.setUpdateTime(LocalDateTime.now());
        folderMapper.updateById(folder);
    }

    /**
     * 检查是否是子文件夹
     */
    private boolean isSubFolder(Long folderId, Long potentialParentId) {
        if (potentialParentId == null) {
            return false;
        }

        Folder current = folderMapper.selectById(potentialParentId);
        while (current != null && current.getParentId() != null) {
            if (current.getParentId().equals(folderId)) {
                return true;
            }
            current = folderMapper.selectById(current.getParentId());
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void renameFolder(Long id, String newName, Long operatorId) {
        Folder folder = folderMapper.selectById(id);
        if (folder == null) {
            throw new ApiException("文件夹不存在");
        }

        // 检查用户是否有权限重命名文件夹
        if (!knowledgeBaseService.checkPermission(folder.getKbId(), operatorId, Constants.KnowledgeBaseMemberRole.MEMBER)) {
            throw new ApiException("没有权限重命名该文件夹");
        }

        // 检查文件夹名称是否已存在
        LambdaQueryWrapper<Folder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Folder::getKbId, folder.getKbId())
                .eq(Folder::getParentId, folder.getParentId())
                .eq(Folder::getName, newName);
        if (folderMapper.selectCount(queryWrapper) > 0) {
            throw new ApiException("该名称的文件夹已存在");
        }

        // 更新文件夹名称
        folder.setName(newName);
        folder.setUpdateTime(LocalDateTime.now());
        folderMapper.updateById(folder);
    }

    @Override
    public List<FolderTreeDTO> getFolderTree(Long kbId, Long userId) {
        // 检查用户是否有权限查看文件夹
        if (!knowledgeBaseService.checkPermission(kbId, userId, Constants.KnowledgeBaseMemberRole.MEMBER)) {
            throw new ApiException("没有权限查看该知识库的文件夹");
        }

        // 获取知识库的所有文件夹
        List<Folder> allFolders = folderMapper.findByKbId(kbId);

        // 构建文件夹树
        return buildFolderTree(null, allFolders);
    }

    /**
     * 递归构建文件夹树
     */
    private List<FolderTreeDTO> buildFolderTree(Long parentId, List<Folder> allFolders) {
        List<FolderTreeDTO> tree = new ArrayList<>();

        // 获取当前层级的文件夹
        List<Folder> currentLevelFolders = allFolders.stream()
                .filter(folder -> 
                    (parentId == null && folder.getParentId() == null) ||
                    (parentId != null && parentId.equals(folder.getParentId()))
                )
                .collect(Collectors.toList());

        // 为每个文件夹创建节点
        for (Folder folder : currentLevelFolders) {
            FolderTreeDTO node = new FolderTreeDTO();
            node.setId(folder.getId());
            node.setName(folder.getName());
            node.setChildren(buildFolderTree(folder.getId(), allFolders));
            tree.add(node);
        }

        return tree;
    }

    @Override
    public List<FolderDTO> getSubFolders(Long parentId, Long userId) {
        Folder parent = folderMapper.selectById(parentId);
        if (parent == null) {
            throw new ApiException("父文件夹不存在");
        }

        // 检查用户是否有权限查看子文件夹
        if (!knowledgeBaseService.checkPermission(parent.getKbId(), userId, Constants.KnowledgeBaseMemberRole.MEMBER)) {
            throw new ApiException("没有权限查看该文件夹的子文件夹");
        }

        return folderMapper.findByParentId(parentId).stream()
                .map(folder -> {
                    FolderDTO dto = new FolderDTO();
                    BeanUtils.copyProperties(folder, dto);
                    return dto;
                })
                .collect(Collectors.toList());
    }
}