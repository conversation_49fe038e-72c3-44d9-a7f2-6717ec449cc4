package com.etrx.kb.controller;

import com.etrx.kb.task.DocumentStatusSyncTask;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/aikb/tasks")
@RequiredArgsConstructor
public class TaskController {

    private final DocumentStatusSyncTask documentStatusSyncTask;

    @PostMapping("/document-sync/pause")
    public void pauseDocumentSync() {
        documentStatusSyncTask.pause();
    }

    @PostMapping("/document-sync/resume")
    public void resumeDocumentSync() {
        documentStatusSyncTask.resume();
    }

    @GetMapping("/document-sync/status")
    public boolean getDocumentSyncStatus() {
        return documentStatusSyncTask.isEnabled();
    }
} 