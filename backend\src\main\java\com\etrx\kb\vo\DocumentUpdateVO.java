package com.etrx.kb.vo;

import lombok.Data;
import java.util.Map;

/**
 * 文档更新请求VO
 */
@Data
public class DocumentUpdateVO {
    /**
     * 文档名称
     */
    private String name;

    /**
     * 文档元数据字段
     */
    private Map<String, Object> meta_fields;

    /**
     * 文档解析方法:
     * naive: 通用
     * manual: 手动
     * qa: 问答
     * table: 表格
     * paper: 论文
     * book: 书籍
     * laws: 法律
     * presentation: 演示文稿
     * picture: 图片
     * one: 单文档
     * email: 邮件
     */
    private String chunk_method;

    /**
     * 解析配置
     * 注意：配置内容根据chunk_method不同而不同
     */
    private ParserConfigVO parser_config;

    @Data
    public static class ParserConfigVO {
        /**
         * 分块token数量（仅用于naive方法）
         */
        private Integer chunk_token_count;

        /**
         * 是否识别布局（仅用于naive方法）
         */
        private String layout_recognize;

        /**
         * 是否将Excel转换为HTML格式（仅用于naive方法）
         */
        private Boolean html4excel;

        /**
         * 分隔符（仅用于naive方法）
         */
        private String delimiter;

        /**
         * PDF任务页面大小（仅用于naive方法）
         */
        private Integer task_page_size;

        /**
         * Raptor配置（用于naive、qa、manual、paper、book、laws、presentation方法）
         */
        private RaptorVO raptor;
    }

    @Data
    public static class RaptorVO {
        /**
         * 是否使用Raptor
         */
        private Boolean use_raptor;
    }
} 