import { IReferenceChunk } from '@/interfaces/database/chat';
import { useMemo, useCallback, memo } from 'react';
import Markdown from 'react-markdown';
import SyntaxHighlighter from 'react-syntax-highlighter';
import rehypeKatex from 'rehype-katex';
import rehypeRaw from 'rehype-raw';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import { visitParents } from 'unist-util-visit-parents';

import { useTranslation } from 'react-i18next';

import 'katex/dist/katex.min.css'; // `rehype-katex` does not import the CSS for you

import {
  preprocessLaTeX,
  replaceThinkToSection,
} from '@/utils/chat';
import { replaceTextByOldReg } from '@/pages/chat/utils';
import { parsePieChart } from '@/utils/chart-parser';
import PieChartComponent from '@/components/pie-chart';

import classNames from 'classnames';
import { pipe } from 'lodash/fp';
import styles from './index.module.less';

// 优化的饼状图组件，使用 memo 防止不必要的重新渲染
const MemoizedPieChart = memo(({ chartConfig }: { chartConfig: any }) => {
  return (
    <div className={styles.pieChartWrapper}>
      <PieChartComponent {...chartConfig} />
    </div>
  );
});

MemoizedPieChart.displayName = 'MemoizedPieChart';

// TODO: The display of the table is inconsistent with the display previously placed in the MessageItem.
const MarkdownContent = ({
  content,
}: {
  content: string;
  loading: boolean;
  clickDocumentButton?: (documentId: string, chunk: IReferenceChunk) => void;
}) => {
  const { t } = useTranslation();
  
  const contentWithCursor = useMemo(() => {
    // let text = DOMPurify.sanitize(content);
    let text = content;
    if (text === '') {
      text = t('chat.searching');
    }
    const nextText = replaceTextByOldReg(text);
    return pipe(replaceThinkToSection, preprocessLaTeX)(nextText);
  }, [content, t]);

  const rehypeWrapReference = () => {
    return function wrapTextTransform(tree: any) {
      visitParents(tree, 'text', (node, ancestors) => {
        const latestAncestor = ancestors.at(-1);
        if (
          latestAncestor.tagName !== 'custom-typography' &&
          latestAncestor.tagName !== 'code'
        ) {
          node.type = 'element';
          node.tagName = 'custom-typography';
          node.properties = {};
          node.children = [{ type: 'text', value: node.value }];
        }
      });
    };
  };

  // 自定义代码块渲染器，支持饼状图
  const customCodeBlock = useCallback((props: any) => {
    const { children, className, node, ...rest } = props;
    const match = /language-(\w+)/.exec(className || '');
    
    if (match) {
      const language = match[1];
      
      // 处理饼状图 - 支持 pie 和 mermaid pie 格式
      if (language === 'pie' || (language === 'mermaid' && String(children).trim().startsWith('pie'))) {
        // 解析饼状图配置
        const chartConfig = parsePieChart(String(children));
        
        if (chartConfig) {
          return <MemoizedPieChart chartConfig={chartConfig} />;
        }
      }
    }

    return match ? (
      <SyntaxHighlighter
        {...rest}
        PreTag="div"
        language={match[1]}
        wrapLongLines
      >
        {String(children).replace(/\n$/, '')}
      </SyntaxHighlighter>
    ) : (
      <code {...rest} className={classNames(className, 'tw-text-wrap')}>
        {children}
      </code>
    );
  }, []);

  return (
    <Markdown
      rehypePlugins={[rehypeWrapReference, rehypeKatex, rehypeRaw]}
      remarkPlugins={[remarkGfm, remarkMath]}
      className={styles.markdownContentWrapper}
      components={
        {
          code: customCodeBlock,
        } as any
      }
    >
      {contentWithCursor}
    </Markdown>
  );
};

export default MarkdownContent;
