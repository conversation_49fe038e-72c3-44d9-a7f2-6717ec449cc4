package com.etrx.kb.config;

import org.springframework.context.annotation.Configuration;
import jakarta.annotation.PostConstruct;

/**
 * Reactor Context Propagation 配置
 * 修复 Spring AI MessageAggregator 与 context-propagation 的兼容性问题
 */
@Configuration
public class ReactorContextPropagationConfiguration {

    @PostConstruct
    public void initializeContextPropagation() {
        try {
            // 设置系统属性禁用context propagation
            System.setProperty("micrometer.context-propagation.enabled", "false");
            System.setProperty("reactor.netty.ioWorkerCount", "4");
            System.setProperty("reactor.schedulers.defaultBoundedElasticSize", "10");
            
            System.out.println("Context propagation disabled to fix Spring AI MessageAggregator compatibility issue");
        } catch (Exception e) {
            // 忽略错误，确保应用可以正常启动
            System.err.println("Warning: Failed to configure context propagation: " + e.getMessage());
        }
    }
} 