package com.etrx.kb.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 第三方登录DTO
 */
@Data
public class ThirdPartyLoginDTO {

    /**
     * API密钥
     */
    @NotBlank(message = "API密钥不能为空")
    private String apiKey;

    /**
     * 用户信息
     */
    private UserInfo userInfo;

    /**
     * 第三方用户信息
     */
    @Data
    public static class UserInfo {
        /**
         * 用户名称
         */
        @NotBlank(message = "用户名称不能为空")
        private String username;

        /**
         * 用户工号
         */
        @NotBlank(message = "用户工号不能为空")
        private String employeeNo;

        /**
         * 用户UUID（可选字段）
         */
        private String userUuid;

        /**
         * 用户昵称
         */
        private String nickname;

        /**
         * 邮箱
         */
        private String email;

        /**
         * 手机号
         */
        private String phone;

        /**
         * 头像URL
         */
        private String avatar;
    }
} 