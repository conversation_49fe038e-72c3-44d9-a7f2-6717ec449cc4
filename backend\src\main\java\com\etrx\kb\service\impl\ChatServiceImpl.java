package com.etrx.kb.service.impl;

import com.etrx.kb.common.PageResult;
import com.etrx.kb.dto.ChatAssistantMappingDTO;
import com.etrx.kb.dto.ChatRequestDTO;
import com.etrx.kb.dto.ChatResponseDTO;
import com.etrx.kb.dto.ChatHistoryResponseDTO;
import com.etrx.kb.dto.ChatSessionDTO;
import com.etrx.kb.dto.CreateChatAssistantRequestDTO;
import com.etrx.kb.dto.ConversationCompletionRequestDTO;
import com.etrx.kb.exception.ApiException;
import com.etrx.kb.service.ChatService;
import com.etrx.kb.service.ChatAssistantMappingService;
import com.etrx.kb.service.ModelProviderService;
import com.etrx.kb.config.ModelConfig;
import com.etrx.kb.domain.ChatAssistantMapping;
import com.etrx.kb.domain.ChatSessionMapping;
import com.etrx.kb.mapper.ChatSessionMappingMapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import erd.cloud.ai.ragflow.RagflowChatClient;
import erd.cloud.ai.ragflow.RagflowDatasetClient;
import erd.cloud.ai.ragflow.RagflowSessionClient;
import erd.cloud.ai.ragflow.RagflowOpenAiClient;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.messages.UserMessage;
import java.util.List;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.ollama.api.OllamaOptions;
import org.springframework.ai.azure.openai.AzureOpenAiChatOptions;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.context.ApplicationContext;
import org.springframework.util.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.io.IOException;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 聊天服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ChatServiceImpl implements ChatService {

    private final ChatAssistantMappingService chatAssistantMappingService;
    private final ChatSessionMappingMapper chatSessionMappingMapper;
    private final ObjectMapper objectMapper;
    private final ModelProviderService modelProviderService;
    private final ModelConfig modelConfig;
    private final ApplicationContext applicationContext;

    @Value("${ragflow.base-url:http://localhost:9380}")
    private String ragflowBaseUrl;

    @Value("${ragflow.api-key:}")
    private String ragflowApiKey;

    @Value("${ragflow.user-id:}")
    private String ragflowUserId;

    // 注入MCP ToolCallbackProvider，使用Optional避免Bean不存在时的启动错误
    private final Optional<org.springframework.ai.tool.ToolCallbackProvider> mcpToolCallbackProvider;

    @Override
    public ChatResponseDTO chat(ChatRequestDTO request, Long userId) {
        try {
            String chatAssistantId = getOrCreateChatAssistantForRequest(request, userId);
            String sessionId = request.getSessionId();

            // 如果没有会话ID，创建新会话
            if (!StringUtils.hasText(sessionId)) {
                sessionId = createChatSession(chatAssistantId, "新会话", userId);
            }

            // 调用RAGflow API
            RagflowSessionClient sessionClient = new RagflowSessionClient(ragflowBaseUrl, ragflowApiKey);
            String response = sessionClient.conversationCompletion(
                    sessionId,
                    request.getQuestion(),
                    false  // 非流式响应
            );

            // 解析响应
            ChatResponseDTO chatResponse = parseRagflowResponse(response);

            // 更新会话消息和引用
            if (chatResponse != null) {
                updateSessionContent(sessionId, response);
            }

            return chatResponse;

        } catch (ApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("聊天请求失败: userId={}, sessionId={}", userId, request.getSessionId(), e);
            throw new ApiException("聊天请求失败: " + e.getMessage());
        }
    }

    @Override
    public List<ChatResponseDTO> streamChat(ChatRequestDTO request, Long userId) {
        try {
            // 获取或创建聊天助手ID
            String chatAssistantId = getOrCreateChatAssistantForRequest(request, userId);
            String sessionId = request.getSessionId();

            // 如果没有会话ID，创建新会话
            if (!StringUtils.hasText(sessionId)) {
                sessionId = createChatSession(chatAssistantId, "新会话", userId);
                // 更新请求中的sessionId，以便后续使用
                request.setSessionId(sessionId);
            }

            List<ChatResponseDTO> responses;

            // 检查聊天助手是否绑定了数据集
            boolean hasDatasets = checkChatAssistantHasDatasets(chatAssistantId, userId);

            if (hasDatasets) {
                // 有数据集绑定，使用RAGflow会话API进行流式聊天
                responses = streamChatWithRAGflowSession(chatAssistantId, request);
            } else {
                // 没有数据集绑定，使用RAGflow OpenAI Chat Completion API进行流式聊天
                responses = streamChatWithOpenAiCompletion(chatAssistantId, request);
            }

            // 存储聊天记录到chat_session_mapping表
            if (responses != null && !responses.isEmpty()) {
                // 找到包含最完整回答的响应（通常是answer内容最长的那一条）
                ChatResponseDTO mostCompleteResponse = responses.stream()
                        .filter(r -> StringUtils.hasText(r.getAnswer()))
                        .max((r1, r2) -> Integer.compare(r1.getAnswer().length(), r2.getAnswer().length()))
                        .orElse(null);

                if (mostCompleteResponse != null) {
                    updateStreamChatRecord(sessionId, request.getQuestion(), mostCompleteResponse);
                    log.info("成功获取最完整的AI回答，长度: {}", mostCompleteResponse.getAnswer().length());
                } else {
                    log.warn("流式响应中未找到有效的回答内容，sessionId: {}", sessionId);
                }
            }
            return responses;

        } catch (Exception e) {
            log.error("流式聊天处理异常", e);
            throw new ApiException("聊天处理失败: " + e.getMessage());
        }
    }

    @Override
    public String createOrUpdateChatAssistant(CreateChatAssistantRequestDTO request, Long userId) {
        try {
            // 检查是否为修改操作（dialog_id存在且在系统中找到）
            boolean isUpdateOperation = false;
            ChatAssistantMappingDTO existingMapping = null;

            if (request.isUpdateOperation()) {
                existingMapping = chatAssistantMappingService.getMappingByUserAndAssistant(userId, request.getDialogId());
                isUpdateOperation = existingMapping != null;
            }

            // 统一处理新增和修改操作
            return processCreateOrUpdateAssistant(request, userId, isUpdateOperation, existingMapping);

        } catch (ApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("新增/修改聊天助手异常", e);
            throw new ApiException("操作聊天助手失败: " + e.getMessage());
        }
    }

    /**
     * 统一处理新增和修改聊天助手
     */
    private String processCreateOrUpdateAssistant(CreateChatAssistantRequestDTO request, Long userId,
                                                  boolean isUpdateOperation, ChatAssistantMappingDTO existingMapping) throws IOException {
        // 创建RAGflow聊天客户端
        RagflowChatClient chatClient = new RagflowChatClient(ragflowBaseUrl, ragflowApiKey);

        // 构建LLM配置
        String llmConfig = buildLLMConfigFromRequest(request);

        // 构建提示配置
        String promptConfig = buildPromptConfigFromRequest(request);

        // 确定助理名称
        String assistantName;
        if (StringUtils.hasText(request.getName())) {
            assistantName = request.getName();
        } else if (isUpdateOperation && existingMapping != null) {
            // 修改操作且未提供新名称，保持原有名称
            assistantName = existingMapping.getAssistantName();
        } else {
            // 新增操作且未提供名称，生成默认名称
            assistantName = "新的助手——" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        }

        // 准备知识库ID数组，如果为空则传入空数组
        List<String> knowledgeBaseIds = request.getKnowledgeBaseIds();
        String[] kbIdsArray = (knowledgeBaseIds != null && !knowledgeBaseIds.isEmpty()) ?
                knowledgeBaseIds.toArray(new String[0]) :
                new String[0];

        // 根据操作类型调用不同的RAGflow API
        String response;
        if (isUpdateOperation) {
            // 修改聊天助手
            response = chatClient.updateChat(
                    request.getDialogId(), // 使用原有的助手ID
                    assistantName,
                    request.getIcon() != null ? request.getIcon() : "", // 头像
                    kbIdsArray, // 知识库ID（可以为空）
                    llmConfig,
                    promptConfig
            );
            return request.getDialogId();
        } else {
            // 创建聊天助手
            response = chatClient.createChat(
                    assistantName,
                    request.getIcon() != null ? request.getIcon() : "", // 头像
                    kbIdsArray, // 知识库ID（可以为空）
                    llmConfig,
                    promptConfig
            );
        }

        // 解析响应
        JsonNode responseNode = objectMapper.readTree(response);
        if (responseNode.get("code").asInt() == 0) {
            String ragflowAssistantId;

            if (isUpdateOperation) {
                // 修改操作，返回原有的助手ID
                ragflowAssistantId = request.getDialogId();

                // 更新本地映射记录（助手名称、图标和知识库ID）
                chatAssistantMappingService.updateAssistantInfo(
                        userId,
                        ragflowAssistantId,
                        assistantName,
                        request.getIcon(),
                        knowledgeBaseIds, // RAGflow知识库ID列表
                        null // 本地知识库ID为空
                );
                log.info("成功修改聊天助手: {}", request.getDialogId());
            } else {
                // 新增操作，获取新创建的助手ID
                ragflowAssistantId = responseNode.get("data").get("id").asText();

                // 记录新的映射关系
                chatAssistantMappingService.createMapping(
                        userId,
                        ragflowAssistantId,
                        assistantName,
                        request.getIcon(), // 添加icon参数
                        knowledgeBaseIds, // RAGflow知识库ID列表（可以为空）
                        null, // 本地知识库ID为空
                        ChatAssistantMapping.AssistantType.RAGFLOW_KB.getCode()
                );
                log.info("成功创建聊天助手: {}", response);
            }

            return ragflowAssistantId;
        } else {
            String errorMessage = responseNode.get("message").asText();
            String operation = isUpdateOperation ? "修改" : "创建";
            throw new ApiException(operation + "聊天助手失败: " + errorMessage);
        }
    }

    @Override
    public String createChatSession(String chatAssistantId, String sessionName, Long userId) {
        try {
            RagflowSessionClient sessionClient = new RagflowSessionClient(ragflowBaseUrl, ragflowApiKey);
            String finalSessionName = sessionName != null ? sessionName : "新会话——" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            String response = sessionClient.createSession(
                    chatAssistantId,
                    finalSessionName,
                    ragflowUserId  // 使用配置的统一用户ID
            );

            // 解析响应获取会话ID
            JsonNode responseNode = objectMapper.readTree(response);
            if (responseNode.get("code").asInt() == 0) {
                String sessionId = responseNode.get("data").get("id").asText();
                // 记录会话映射关系
                ChatSessionMapping sessionMapping = new ChatSessionMapping();
                sessionMapping.setUserId(userId);
                sessionMapping.setSessionId(sessionId);
                sessionMapping.setChatAssistantId(chatAssistantId);
                sessionMapping.setSessionName(finalSessionName);
                sessionMapping.setStatus(ChatSessionMapping.Status.ENABLED.getCode());

                chatSessionMappingMapper.insert(sessionMapping);

                return sessionId;
            } else {
                throw new ApiException("创建会话失败: " + responseNode.get("message").asText());
            }

        } catch (IOException e) {
            log.error("创建聊天会话失败", e);
            throw new ApiException("创建聊天会话失败");
        }
    }

    @Override
    public String updateChatSession(String sessionId, String sessionName, Long userId) {
        try {
            // 1. 验证会话归属权
            ChatSessionMapping sessionMapping = chatSessionMappingMapper.selectByUserIdAndSessionId(userId, sessionId);
            if (sessionMapping == null) {
                throw new ApiException("会话不存在或无权限访问");
            }

            // 2. 调用RAGflow API更新会话
            RagflowSessionClient sessionClient = new RagflowSessionClient(ragflowBaseUrl, ragflowApiKey);
            String response = sessionClient.updateSession(
                    sessionMapping.getChatAssistantId(), // chatId
                    sessionId,                            // sessionId
                    sessionName,                          // 新的会话名称
                    ragflowUserId                         // 使用配置的统一用户ID
            );

            // 3. 解析响应
            JsonNode responseNode = objectMapper.readTree(response);
            if (responseNode.get("code").asInt() == 0) {
                // 4. 更新本地映射记录
                chatSessionMappingMapper.updateSessionNameBySessionId(sessionId, sessionName);

                log.info("成功更新会话: sessionId={}, newName={}", sessionId, sessionName);
                return "更新成功";
            } else {
                String errorMessage = responseNode.get("message").asText();
                throw new ApiException("更新会话失败: " + errorMessage);
            }

        } catch (ApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新聊天会话失败: sessionId={}, userId={}", sessionId, userId, e);
            throw new ApiException("更新聊天会话失败: " + e.getMessage());
        }
    }

    @Override
    public void deleteChatSession(String sessionId, Long userId) {
        try {
            log.info("开始删除聊天会话: sessionId={}, userId={}", sessionId, userId);

            // 1. 验证会话归属权
            ChatSessionMapping sessionMapping = chatSessionMappingMapper.selectByUserIdAndSessionId(userId, sessionId);
            if (sessionMapping == null) {
                throw new ApiException("会话不存在或无权限删除");
            }
            String chatAssistantId = sessionMapping.getChatAssistantId();
            // 2. 调用RAGflow API删除会话
            try {
                RagflowSessionClient sessionClient = new RagflowSessionClient(ragflowBaseUrl, ragflowApiKey);
                String response = sessionClient.deleteSessions(chatAssistantId, new String[]{sessionId});

                // 解析删除响应
                JsonNode responseNode = objectMapper.readTree(response);
                if (responseNode.get("code").asInt() != 0) {
                    String errorMessage = responseNode.get("message").asText();
                    log.warn("RAGflow删除会话失败，但继续删除本地记录: {}", errorMessage);
                    // 不抛出异常，继续删除本地记录
                } else {
                    log.info("成功删除RAGflow会话: sessionId={}", sessionId);
                }
            } catch (Exception e) {
                log.warn("RAGflow删除会话失败，但继续删除本地记录: {}", e.getMessage());
                // 不抛出异常，继续删除本地记录
            }

            // 3. 删除本地会话映射记录
            int deletedRows = chatSessionMappingMapper.deleteBySessionId(sessionId);
            if (deletedRows > 0) {
                log.info("成功删除本地会话映射记录: sessionId={}, 删除行数={}", sessionId, deletedRows);
            } else {
                log.warn("删除本地会话映射记录失败，可能记录不存在: sessionId={}", sessionId);
            }

            log.info("成功删除聊天会话及相关数据: sessionId={}", sessionId);

        } catch (ApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("删除聊天会话失败: sessionId={}, userId={}", sessionId, userId, e);
            throw new ApiException("删除聊天会话失败: " + e.getMessage());
        }
    }

    @Override
    public String listRagflowDatasets() {
        try {
            // 创建RAGflow数据集客户端
            RagflowDatasetClient datasetClient = new RagflowDatasetClient(ragflowBaseUrl, ragflowApiKey);

            // 获取数据集列表
            String response = datasetClient.listDatasets();

            return response;

        } catch (IOException e) {
            log.error("获取RAGflow数据集列表失败", e);
            throw new ApiException("RAGflow服务连接失败，请稍后重试");
        } catch (Exception e) {
            log.error("获取RAGflow数据集列表异常", e);
            throw new ApiException("获取数据集列表失败: " + e.getMessage());
        }
    }

    @Override
    public List<ChatAssistantMappingDTO> getUserChatAssistants(Long userId) {
        return chatAssistantMappingService.getUserChatAssistants(userId);
    }

    @Override
    public PageResult<ChatAssistantMappingDTO> getUserChatAssistantsPage(Long userId, Integer pageNum, Integer pageSize) {
        return chatAssistantMappingService.getUserChatAssistantsPage(userId, pageNum, pageSize);
    }

    @Override
    public String getChatAssistantIdBySessionId(String sessionId, Long userId) {
        if (!StringUtils.hasText(sessionId)) {
            throw new ApiException("会话ID不能为空");
        }

        try {
            // 根据sessionId查询映射记录
            ChatSessionMapping mapping = chatSessionMappingMapper.selectByUserIdAndSessionId(userId, sessionId);

            if (mapping == null) {
                throw new ApiException("会话不存在或已过期");
            }

            return mapping.getChatAssistantId();

        } catch (Exception e) {
            log.error("根据sessionId获取chatAssistantId失败: sessionId={}, userId={}", sessionId, userId, e);
            throw new ApiException("获取聊天助手ID失败: " + e.getMessage());
        }
    }

    @Override
    public void deleteChatAssistant(String assistantId, Long userId) {
        try {
            log.info("开始删除聊天助理: assistantId={}, userId={}", assistantId, userId);

            // 1. 验证助理归属权
            ChatAssistantMappingDTO mapping = chatAssistantMappingService.getMappingByUserAndAssistant(userId, assistantId);
            if (mapping == null) {
                throw new ApiException("聊天助理不存在或无权限删除");
            }

            // 2. 调用RAGflow API删除助理
            try {
                RagflowChatClient chatClient = new RagflowChatClient(ragflowBaseUrl, ragflowApiKey);
                String response = chatClient.deleteChats(new String[]{assistantId});

                // 解析删除响应
                JsonNode responseNode = objectMapper.readTree(response);
                if (responseNode.get("code").asInt() != 0) {
                    String errorMessage = responseNode.get("message").asText();
                    log.warn("RAGflow删除助理失败，但继续删除本地记录: {}", errorMessage);
                    // 不抛出异常，继续删除本地记录
                }
                log.info("成功删除RAGflow助理: {}", assistantId);
            } catch (Exception e) {
                log.warn("RAGflow删除助理失败，但继续删除本地记录: {}", e.getMessage());
                // 不抛出异常，继续删除本地记录
            }

            // 3. 删除相关的会话映射
            try {
                // 先查询相关会话
                List<ChatSessionMapping> sessions = chatSessionMappingMapper.selectByChatAssistantId(assistantId);
                // 删除会话映射
                for (ChatSessionMapping session : sessions) {
                    if (session.getUserId().equals(userId)) {
                        chatSessionMappingMapper.deleteBySessionId(session.getSessionId());
                    }
                }
                log.info("删除了 {} 个相关会话映射", sessions.size());
            } catch (Exception e) {
                log.error("删除会话映射失败: {}", e.getMessage());
                // 继续执行，不中断流程
            }

            // 4. 删除助理映射记录
            chatAssistantMappingService.deleteChatAssistant(mapping.getId(), userId);

            log.info("成功删除聊天助理及相关数据: assistantId={}", assistantId);

        } catch (ApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("删除聊天助理失败: assistantId={}, userId={}", assistantId, userId, e);
            throw new ApiException("删除聊天助理失败: " + e.getMessage());
        }
    }

    @Override
    public String getChatAssistantDetails(String assistantId, Long userId) {
        try {
            log.info("获取助手详情: assistantId={}, userId={}", assistantId, userId);

            // 1. 验证助理归属权
            ChatAssistantMappingDTO mapping = chatAssistantMappingService.getMappingByUserAndAssistant(userId, assistantId);
            if (mapping == null) {
                throw new ApiException("聊天助理不存在或无权限访问");
            }

            // 2. 调用RAGflow API获取助理详情（通过listChats的id过滤）
            RagflowChatClient chatClient = new RagflowChatClient(ragflowBaseUrl, ragflowApiKey);
            String response = chatClient.listChats(1, 1, null, false, null, assistantId);

            // 3. 解析响应并提取单个助手详情
            JsonNode responseNode = objectMapper.readTree(response);
            if (responseNode.get("code").asInt() == 0) {
                JsonNode dataNode = responseNode.get("data");
                if (dataNode != null && dataNode.isArray() && dataNode.size() > 0) {
                    // 获取第一个（也是唯一的）助手详情
                    JsonNode assistantNode = dataNode.get(0);

                    // 如果本地有icon信息，更新助手详情中的icon字段
                    if (StringUtils.hasText(mapping.getIcon()) &&
                            assistantNode instanceof com.fasterxml.jackson.databind.node.ObjectNode) {
                        ((com.fasterxml.jackson.databind.node.ObjectNode) assistantNode).put("icon", mapping.getIcon());
                        log.debug("已注入本地icon信息到助手详情中");
                    }

                    // 构建与RAGflow原生格式一致的响应
                    Map<String, Object> result = new HashMap<>();
                    result.put("code", 0);
                    result.put("data", objectMapper.convertValue(assistantNode, Map.class));
                    result.put("message", "success");

                    return objectMapper.writeValueAsString(result);
                } else {
                    throw new ApiException("助手不存在或已被删除");
                }
            } else {
                String errorMessage = responseNode.get("message").asText();
                throw new ApiException("获取助手详情失败: " + errorMessage);
            }

        } catch (ApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取助手详情失败: assistantId={}, userId={}", assistantId, userId, e);
            throw new ApiException("获取助手详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取或创建聊天助手
     */
    private String getOrCreateChatAssistantForRequest(ChatRequestDTO request, Long userId) {
        // 优先尝试通过sessionId获取chatAssistantId
        if (StringUtils.hasText(request.getSessionId())) {
            try {
                return getChatAssistantIdBySessionId(request.getSessionId(), userId);
            } catch (ApiException e) {
                log.warn("无法通过sessionId获取chatAssistantId: {}", e.getMessage());
                throw new ApiException("会话不存在或已过期，请重新开始对话");
            }
        }

        // 如果没有sessionId，说明需要先创建助手和会话
        throw new ApiException("缺少会话ID，请先创建聊天助手和会话");
    }

    /**
     * 构建LLM配置
     */
    private String buildLLMConfig() {
        Map<String, Object> llmConfig = new HashMap<>();
        llmConfig.put("model_name", "qwen3:8b");
        llmConfig.put("temperature", 0.2);
        llmConfig.put("top_p", 0.3);
        llmConfig.put("presence_penalty", 0.4);
        llmConfig.put("frequency_penalty", 0.7);
        llmConfig.put("max_tokens", 512);

        try {
            return objectMapper.writeValueAsString(llmConfig);
        } catch (Exception e) {
            log.error("构建LLM配置失败", e);
            return "{}";
        }
    }

    /**
     * 构建提示配置
     */
    private String buildPromptConfig() {
        Map<String, Object> promptConfig = new HashMap<>();
        promptConfig.put("similarity_threshold", 0.2);
        promptConfig.put("keywords_similarity_weight", 0.7);
        promptConfig.put("top_n", 8);
        promptConfig.put("empty_response", "抱歉，我在知识库中没有找到相关信息。");
        promptConfig.put("opener", "您好！我是您的AI助手，有什么可以帮助您的吗？");
        promptConfig.put("show_quote", true);
        promptConfig.put("prompt", "您是一个智能助手。请根据以下知识库内容回答问题：{{knowledge}} 请基于上述内容详细回答用户问题，并列出相关的数据来源。当知识库内容都与问题无关时，您的回答必须包含\"在知识库中没有找到您要的答案！\"这句话。回答需要考虑聊天历史。");

        // 添加变量配置
        List<Map<String, Object>> variables = new ArrayList<>();
        Map<String, Object> knowledgeVar = new HashMap<>();
        knowledgeVar.put("key", "knowledge");
        knowledgeVar.put("optional", false);
        variables.add(knowledgeVar);
        promptConfig.put("variables", variables);

        try {
            // 确保使用UTF-8编码
            ObjectMapper utf8ObjectMapper = new ObjectMapper();
            String jsonString = utf8ObjectMapper.writeValueAsString(promptConfig);

            // 记录日志以便调试
            log.debug("构建的提示配置JSON: {}", jsonString);

            return jsonString;
        } catch (Exception e) {
            log.error("构建提示配置失败", e);
            return "{}";
        }
    }

    /**
     * 解析RAGflow响应
     */
    private ChatResponseDTO parseRagflowResponse(String response) {
        try {
            JsonNode responseNode = objectMapper.readTree(response);
            ChatResponseDTO chatResponse = new ChatResponseDTO();

            // 添加日志以调试响应格式
            log.debug("RAGflow响应内容: {}", response);

            // 检查响应是否有code字段
            JsonNode codeNode = responseNode.get("code");
            if (codeNode != null && codeNode.asInt() == 0) {
                JsonNode dataNode = responseNode.get("data");
                if (dataNode != null) {
                    // 安全地获取answer字段
                    JsonNode answerNode = dataNode.get("answer");
                    if (answerNode != null) {
                        chatResponse.setAnswer(answerNode.asText());
                    }

                    // 安全地获取session_id字段
                    JsonNode sessionIdNode = dataNode.get("session_id");
                    if (sessionIdNode != null) {
                        chatResponse.setSessionId(sessionIdNode.asText());
                    }

                    // 安全地获取id字段（消息ID）
                    JsonNode idNode = dataNode.get("id");
                    if (idNode != null && !idNode.isNull()) {
                        chatResponse.setMessageId(idNode.asText());
                    }

                    // 解析引用信息 - 改进空对象处理
                    JsonNode referenceNode = dataNode.get("reference");
                    if (referenceNode != null && !referenceNode.isEmpty() && !isEmptyReference(referenceNode)) {
                        Map<String, Object> reference = objectMapper.convertValue(referenceNode, Map.class);
                        chatResponse.setReference(reference);
                    }

                    // 获取prompt字段（如果存在）
                    JsonNode promptNode = dataNode.get("prompt");
                    if (promptNode != null) {
                        log.debug("RAGflow返回的prompt: {}", promptNode.asText());
                    }

                    // 检查audio_binary字段（如果存在）
                    JsonNode audioBinaryNode = dataNode.get("audio_binary");
                    if (audioBinaryNode != null && !audioBinaryNode.isNull()) {
                        log.debug("RAGflow返回了音频数据");
                    }

                    chatResponse.setFinished(true);
                } else {
                    chatResponse.setError("响应格式错误：缺少data字段");
                    chatResponse.setFinished(true);
                }
            } else {
                // 处理错误响应或非标准格式
                JsonNode messageNode = responseNode.get("message");
                String errorMessage = messageNode != null ? messageNode.asText() : "未知错误";

                // 特殊处理一些常见错误
                if (codeNode != null) {
                    int code = codeNode.asInt();
                    switch (code) {
                        case 102:
                            errorMessage = "请输入您的问题";
                            break;
                        case 101:
                            errorMessage = "认证失败，请检查API密钥";
                            break;
                        default:
                            errorMessage = "聊天请求失败 (错误码: " + code + "): " + errorMessage;
                    }
                }

                chatResponse.setError(errorMessage);
                chatResponse.setFinished(true);
            }

            return chatResponse;

        } catch (Exception e) {
            log.error("解析RAGflow响应失败: {}", response, e);
            ChatResponseDTO errorResponse = new ChatResponseDTO();
            errorResponse.setError("响应解析失败: " + e.getMessage());
            errorResponse.setFinished(true);
            return errorResponse;
        }
    }

    /**
     * 检查reference是否为空对象
     */
    private boolean isEmptyReference(JsonNode referenceNode) {
        if (referenceNode.isObject() && referenceNode.size() == 0) {
            return true; // 空对象 {}
        }
        if (referenceNode.isObject()) {
            JsonNode totalNode = referenceNode.get("total");
            if (totalNode != null && totalNode.asInt() == 0) {
                return true; // total为0的引用
            }
        }
        return false;
    }

    /**
     * 解析流式响应
     */
    private List<ChatResponseDTO> parseStreamResponse(String response) {
        List<ChatResponseDTO> responses = new ArrayList<>();

        try {
            // RAGflow的流式响应是多行JSON，每行以"data:"开头
            String[] lines = response.split("\n");
            log.debug("解析流式响应，共 {} 行", lines.length);

            for (int i = 0; i < lines.length; i++) {
                String line = lines[i].trim();
                if (line.startsWith("data:")) {
                    String jsonData = line.substring(5).trim();
                    if (!jsonData.isEmpty()) {
                        log.debug("处理第 {} 行数据: {}", i + 1, jsonData);

                        try {
                            JsonNode responseNode = objectMapper.readTree(jsonData);

                            // 检查是否为结束标志: {"code": 0, "data": true}
                            if (isStreamEndMarker(responseNode)) {
                                log.debug("检测到流式响应结束标志");
                                // 创建结束标志响应
                                ChatResponseDTO endResponse = new ChatResponseDTO();
                                endResponse.setFinished(true);
                                endResponse.setAnswer(""); // 空答案
                                responses.add(endResponse);
                                break;
                            }

                            // 解析正常的数据响应
                            ChatResponseDTO chatResponse = parseStreamMessage(responseNode);
                            if (chatResponse != null) {
                                responses.add(chatResponse);
                            }

                        } catch (Exception e) {
                            log.warn("解析第 {} 行数据失败: {}", i + 1, e.getMessage());
                        }
                    }
                }
            }

            // 如果没有检测到结束标志，手动添加一个
            if (responses.isEmpty() || responses.get(responses.size() - 1).getFinished() == null || !responses.get(responses.size() - 1).getFinished()) {
                log.debug("手动添加流式响应结束标志");
                ChatResponseDTO endResponse = new ChatResponseDTO();
                endResponse.setFinished(true);
                endResponse.setAnswer("");
                responses.add(endResponse);
            }

        } catch (Exception e) {
            log.error("解析流式响应失败", e);
            ChatResponseDTO errorResponse = new ChatResponseDTO();
            errorResponse.setError("流式响应解析失败: " + e.getMessage());
            errorResponse.setFinished(true);
            responses.add(errorResponse);
        }

        log.debug("流式响应解析完成，共 {} 条消息", responses.size());
        return responses;
    }

    /**
     * 检查是否为流式响应结束标志
     */
    private boolean isStreamEndMarker(JsonNode responseNode) {
        JsonNode codeNode = responseNode.get("code");
        JsonNode dataNode = responseNode.get("data");

        return codeNode != null && codeNode.asInt() == 0
                && dataNode != null && dataNode.isBoolean() && dataNode.asBoolean();
    }

    /**
     * 解析单条流式消息
     */
    private ChatResponseDTO parseStreamMessage(JsonNode responseNode) {
        try {
            ChatResponseDTO chatResponse = new ChatResponseDTO();

            JsonNode codeNode = responseNode.get("code");
            if (codeNode == null || codeNode.asInt() != 0) {
                // 处理错误响应
                JsonNode messageNode = responseNode.get("message");
                String errorMessage = messageNode != null ? messageNode.asText() : "未知错误";
                chatResponse.setError("聊天请求失败: " + errorMessage);
                chatResponse.setFinished(true);
                return chatResponse;
            }

            JsonNode dataNode = responseNode.get("data");
            if (dataNode == null || dataNode.isBoolean()) {
                // 跳过布尔值data（结束标志）
                return null;
            }

            // 解析正常的数据响应
            JsonNode answerNode = dataNode.get("answer");
            if (answerNode != null) {
                chatResponse.setAnswer(answerNode.asText());
            }

            JsonNode sessionIdNode = dataNode.get("session_id");
            if (sessionIdNode != null) {
                chatResponse.setSessionId(sessionIdNode.asText());
            }

            JsonNode idNode = dataNode.get("id");
            if (idNode != null && !idNode.isNull()) {
                chatResponse.setMessageId(idNode.asText());
            }

            // 解析引用信息
            JsonNode referenceNode = dataNode.get("reference");
            if (referenceNode != null && !referenceNode.isEmpty()) {
                Map<String, Object> reference = objectMapper.convertValue(referenceNode, Map.class);
                chatResponse.setReference(reference);
            }

            // 流式响应中的消息默认未完成（除非是最后一条）
            chatResponse.setFinished(false);

            return chatResponse;

        } catch (Exception e) {
            log.error("解析流式消息失败", e);
            ChatResponseDTO errorResponse = new ChatResponseDTO();
            errorResponse.setError("消息解析失败: " + e.getMessage());
            errorResponse.setFinished(true);
            return errorResponse;
        }
    }

    /**
     * 根据请求构建LLM配置
     */
    private String buildLLMConfigFromRequest(CreateChatAssistantRequestDTO request) {
        Map<String, Object> llmConfig = new HashMap<>();

        if (request.getLlmSetting() != null) {
            CreateChatAssistantRequestDTO.LlmSetting setting = request.getLlmSetting();
            llmConfig.put("model_name", request.getLlmId() != null ? request.getLlmId() : "qwen3:8b");
            llmConfig.put("temperature", setting.getTemperature() != null ? setting.getTemperature() : 0.2);
            llmConfig.put("top_p", setting.getTopP() != null ? setting.getTopP() : 0.3);
            llmConfig.put("presence_penalty", setting.getPresencePenalty() != null ? setting.getPresencePenalty() : 0.4);
            llmConfig.put("frequency_penalty", setting.getFrequencyPenalty() != null ? setting.getFrequencyPenalty() : 0.7);
            llmConfig.put("max_tokens", setting.getMaxTokens() != null ? setting.getMaxTokens() : 512);
        } else {
            // 使用默认配置
            llmConfig.put("model_name", request.getLlmId() != null ? request.getLlmId() : "qwen3:8b");
            llmConfig.put("temperature", 0.2);
            llmConfig.put("top_p", 0.3);
            llmConfig.put("presence_penalty", 0.4);
            llmConfig.put("frequency_penalty", 0.7);
            llmConfig.put("max_tokens", 512);
        }

        try {
            return objectMapper.writeValueAsString(llmConfig);
        } catch (Exception e) {
            log.error("构建LLM配置失败", e);
            return "{}";
        }
    }

    /**
     * 根据请求构建提示配置
     */
    private String buildPromptConfigFromRequest(CreateChatAssistantRequestDTO request) {
        Map<String, Object> promptConfig = new HashMap<>();

        // 设置基本配置
        promptConfig.put("similarity_threshold", request.getSimilarityThreshold() != null ? request.getSimilarityThreshold() : 0.2);
        promptConfig.put("keywords_similarity_weight", request.getVectorSimilarityWeight() != null ? request.getVectorSimilarityWeight() : 0.7);
        promptConfig.put("top_n", request.getTopN() != null ? request.getTopN() : 8);

        if (request.getPromptConfig() != null) {
            CreateChatAssistantRequestDTO.PromptConfig promptCfg = request.getPromptConfig();
            promptConfig.put("empty_response",
                    StringUtils.hasText(promptCfg.getEmptyResponse()) ?
                            promptCfg.getEmptyResponse() : "");
            promptConfig.put("opener",
                    StringUtils.hasText(promptCfg.getPrologue()) ?
                            promptCfg.getPrologue() : "您好！我是您的AI助手，有什么可以帮助您的吗？");
            promptConfig.put("show_quote", promptCfg.getQuote() != null ? promptCfg.getQuote() : true);
            promptConfig.put("prompt",
                    StringUtils.hasText(promptCfg.getSystem()) ?
                            promptCfg.getSystem() : "您是一个智能助手。请根据以下知识库内容回答问题：{knowledge} 请基于上述内容详细回答用户问题，并列出相关的数据来源。当知识库内容都与问题无关时，您的回答必须包含\"在知识库中没有找到您要的答案！\"这句话。回答需要考虑聊天历史。");

            // 添加变量配置
            List<Map<String, Object>> variables = new ArrayList<>();
            if (promptCfg.getParametersList() != null && !promptCfg.getParametersList().isEmpty()) {
                for (CreateChatAssistantRequestDTO.Parameter param : promptCfg.getParametersList()) {
                    Map<String, Object> variable = new HashMap<>();
                    variable.put("key", param.getKey());
                    variable.put("optional", param.getOptional() != null ? param.getOptional() : false);
                    variables.add(variable);
                }
            }
            promptConfig.put("variables", variables);
        } else {
            // 使用默认配置
            promptConfig.put("empty_response", "抱歉，我在知识库中没有找到相关信息。");
            promptConfig.put("opener", "您好！我是您的AI助手，有什么可以帮助您的吗？");
            promptConfig.put("show_quote", true);
            promptConfig.put("prompt", "您是一个智能助手。请根据以下知识库内容回答问题：{knowledge} 请基于上述内容详细回答用户问题，并列出相关的数据来源。当知识库内容都与问题无关时，您的回答必须包含\"在知识库中没有找到您要的答案！\"这句话。回答需要考虑聊天历史。");

            // 添加默认变量配置
            List<Map<String, Object>> variables = new ArrayList<>();
            Map<String, Object> knowledgeVar = new HashMap<>();
            knowledgeVar.put("key", "knowledge");
            knowledgeVar.put("optional", false);
            variables.add(knowledgeVar);
            promptConfig.put("variables", variables);
        }

        try {
            return objectMapper.writeValueAsString(promptConfig);
        } catch (Exception e) {
            log.error("构建提示配置失败", e);
            return "{}";
        }
    }

    /**
     * 检查聊天助手是否绑定了数据集
     */
    private boolean checkChatAssistantHasDatasets(String chatAssistantId, Long userId) {
        try {
            // 通过映射服务获取助手信息
            ChatAssistantMappingDTO mapping = chatAssistantMappingService.getMappingByUserAndAssistant(userId, chatAssistantId);
            if (mapping != null) {
                // 检查是否有RAGflow知识库ID或本地知识库ID
                return (mapping.getRagflowKnowledgeBaseIds() != null && !mapping.getRagflowKnowledgeBaseIds().isEmpty()) ||
                        (mapping.getLocalKnowledgeBaseIds() != null && !mapping.getLocalKnowledgeBaseIds().isEmpty());
            }
            return false;
        } catch (Exception e) {
            log.warn("检查聊天助手数据集绑定状态失败，默认使用会话API: {}", e.getMessage());
            return true; // 如果检查失败，默认使用会话API
        }
    }

    /**
     * 使用RAGflow会话API进行聊天
     */
    private ChatResponseDTO chatWithRAGflowSession(String chatAssistantId, ChatRequestDTO request) throws IOException {
        // 创建RAGflow会话客户端
        RagflowSessionClient sessionClient = new RagflowSessionClient(ragflowBaseUrl, ragflowApiKey);

        // 发送聊天请求 - 使用统一的RAGflow用户ID
        String response = sessionClient.converseWithChatAssistant(
                chatAssistantId,  // 这是chatId参数
                request.getQuestion(),
                request.getStream() != null ? request.getStream() : false,
                request.getSessionId(),  // 会话ID
                ragflowUserId  // 使用配置的统一用户ID
        );

        // 解析响应
        return parseRagflowResponse(response);
    }

    /**
     * 使用RAGflow OpenAI Chat Completion API进行聊天
     */
    private ChatResponseDTO chatWithOpenAiCompletion(String chatAssistantId, ChatRequestDTO request) throws IOException {
        // 创建RAGflow OpenAI客户端
        RagflowOpenAiClient openAiClient = new RagflowOpenAiClient(ragflowBaseUrl, ragflowApiKey);

        // 构建消息列表
        List<Map<String, Object>> messages = buildMessagesFromRequest(request);

        // 获取LLM模型名称
        String modelName = getLLMModelName(chatAssistantId);

        // 发送聊天补全请求
        String response = openAiClient.createChatCompletion(
                chatAssistantId,
                modelName,
                messages,
                request.getStream() != null ? request.getStream() : false
        );

        // 解析响应
        return parseOpenAiResponse(response, request.getSessionId());
    }

    /**
     * 获取聊天助手的LLM模型名称
     */
    private String getLLMModelName(String chatAssistantId) {
        // 这里可以根据实际需求从数据库或配置中获取
        // 暂时返回默认模型
        return "qwen3:8b";
    }

    /**
     * 解析OpenAI格式的响应
     */
    private ChatResponseDTO parseOpenAiResponse(String response, String sessionId) {
        try {
            log.debug("解析OpenAI响应: {}", response);

            // 检查是否为流式响应（包含"data:"前缀）
            if (response.contains("data:")) {
                return parseOpenAiStreamResponse(response, sessionId);
            }

            // 检查是否为RAGflow格式的错误响应
            if (response.contains("\"code\"")) {
                return parseRagflowErrorResponse(response);
            }

            // 解析标准OpenAI格式的非流式响应
            JsonNode responseNode = objectMapper.readTree(response);
            ChatResponseDTO chatResponse = new ChatResponseDTO();

            if (responseNode.has("choices") && responseNode.get("choices").isArray() &&
                    responseNode.get("choices").size() > 0) {

                JsonNode firstChoice = responseNode.get("choices").get(0);
                if (firstChoice.has("message")) {
                    JsonNode message = firstChoice.get("message");
                    JsonNode contentNode = message.get("content");
                    if (contentNode != null) {
                        chatResponse.setAnswer(contentNode.asText());
                    }
                    chatResponse.setSessionId(sessionId);
                    chatResponse.setFinished(true);
                } else {
                    chatResponse.setError("响应格式错误：缺少message字段");
                    chatResponse.setFinished(true);
                }
            } else if (responseNode.has("error")) {
                JsonNode errorNode = responseNode.get("error");
                String errorMessage = errorNode.has("message") ? errorNode.get("message").asText() : "未知错误";
                chatResponse.setError("聊天请求失败: " + errorMessage);
                chatResponse.setFinished(true);
            } else {
                chatResponse.setError("未知的响应格式");
                chatResponse.setFinished(true);
            }

            return chatResponse;

        } catch (Exception e) {
            log.error("解析OpenAI响应失败: {}", response, e);
            ChatResponseDTO errorResponse = new ChatResponseDTO();
            errorResponse.setError("响应解析失败: " + e.getMessage());
            errorResponse.setFinished(true);
            return errorResponse;
        }
    }

    /**
     * 解析OpenAI格式的流式响应
     */
    private ChatResponseDTO parseOpenAiStreamResponse(String response, String sessionId) {
        try {
            log.debug("解析OpenAI流式响应");
            StringBuilder fullContent = new StringBuilder();
            String[] lines = response.split("\n");

            for (String line : lines) {
                if (line.trim().startsWith("data:")) {
                    String jsonData = line.substring(5).trim();
                    if (!jsonData.isEmpty() && !jsonData.equals("[DONE]")) {
                        try {
                            JsonNode dataNode = objectMapper.readTree(jsonData);
                            if (dataNode.has("choices") && dataNode.get("choices").isArray() &&
                                    dataNode.get("choices").size() > 0) {

                                JsonNode firstChoice = dataNode.get("choices").get(0);
                                if (firstChoice.has("delta")) {
                                    JsonNode delta = firstChoice.get("delta");
                                    JsonNode contentNode = delta.get("content");
                                    if (contentNode != null && !contentNode.isNull()) {
                                        fullContent.append(contentNode.asText());
                                    }
                                }
                            }
                        } catch (Exception e) {
                            log.warn("解析流式响应行失败: {}", e.getMessage());
                        }
                    }
                }
            }

            ChatResponseDTO chatResponse = new ChatResponseDTO();
            chatResponse.setAnswer(fullContent.toString());
            chatResponse.setSessionId(sessionId);
            chatResponse.setFinished(true);

            return chatResponse;

        } catch (Exception e) {
            log.error("解析OpenAI流式响应失败", e);
            ChatResponseDTO errorResponse = new ChatResponseDTO();
            errorResponse.setError("流式响应解析失败: " + e.getMessage());
            errorResponse.setFinished(true);
            return errorResponse;
        }
    }

    /**
     * 解析RAGflow格式的错误响应
     */
    private ChatResponseDTO parseRagflowErrorResponse(String response) {
        try {
            JsonNode responseNode = objectMapper.readTree(response);
            ChatResponseDTO chatResponse = new ChatResponseDTO();

            JsonNode codeNode = responseNode.get("code");
            JsonNode messageNode = responseNode.get("message");

            if (codeNode != null && codeNode.asInt() != 0) {
                String errorMessage = messageNode != null ? messageNode.asText() : "未知错误";
                int code = codeNode.asInt();

                // 特殊处理一些常见错误
                switch (code) {
                    case 102:
                        if ("The last content of this conversation is not from user.".equals(errorMessage)) {
                            errorMessage = "对话的最后一条内容不是来自用户";
                        } else {
                            errorMessage = "请输入您的问题";
                        }
                        break;
                    case 101:
                        errorMessage = "认证失败，请检查API密钥";
                        break;
                    default:
                        errorMessage = "聊天请求失败 (错误码: " + code + "): " + errorMessage;
                }

                chatResponse.setError(errorMessage);
            } else {
                chatResponse.setError("未知错误");
            }

            chatResponse.setFinished(true);
            return chatResponse;

        } catch (Exception e) {
            log.error("解析RAGflow错误响应失败", e);
            ChatResponseDTO errorResponse = new ChatResponseDTO();
            errorResponse.setError("错误响应解析失败: " + e.getMessage());
            errorResponse.setFinished(true);
            return errorResponse;
        }
    }

    /**
     * 使用RAGflow会话API进行流式聊天
     */
    private List<ChatResponseDTO> streamChatWithRAGflowSession(String chatAssistantId, ChatRequestDTO request) throws IOException {
        // 创建RAGflow会话客户端
        RagflowSessionClient sessionClient = new RagflowSessionClient(ragflowBaseUrl, ragflowApiKey);

        // 发送流式聊天请求 - 使用统一的RAGflow用户ID
        String response = sessionClient.converseWithChatAssistant(
                chatAssistantId,
                request.getQuestion(),
                true, // 启用流式响应
                request.getSessionId(),
                ragflowUserId  // 使用配置的统一用户ID
        );

        // 解析流式响应
        return parseStreamResponse(response);
    }

    /**
     * 使用RAGflow OpenAI Chat Completion API进行流式聊天
     */
    private List<ChatResponseDTO> streamChatWithOpenAiCompletion(String chatAssistantId, ChatRequestDTO request) throws IOException {
        // 创建RAGflow OpenAI客户端
        RagflowOpenAiClient openAiClient = new RagflowOpenAiClient(ragflowBaseUrl, ragflowApiKey);

        // 构建消息列表
        List<Map<String, Object>> messages = buildMessagesFromRequest(request);

        // 获取LLM模型名称
        String modelName = getLLMModelName(chatAssistantId);

        // 发送流式聊天补全请求
        String response = openAiClient.createChatCompletion(
                chatAssistantId,
                modelName,
                messages,
                true // 强制启用流式响应
        );

        // 解析OpenAI流式响应
        return parseOpenAiStreamResponseToList(response, request.getSessionId());
    }

    /**
     * 从请求构建消息列表
     */
    private List<Map<String, Object>> buildMessagesFromRequest(ChatRequestDTO request) {
        List<Map<String, Object>> messages = new ArrayList<>();

        // 优先使用messages字段（如果提供）
        if (request.getMessages() != null && !request.getMessages().isEmpty()) {
            log.debug("使用完整的消息历史，共 {} 条消息", request.getMessages().size());

            for (ChatRequestDTO.ChatMessage chatMessage : request.getMessages()) {
                // 跳过空内容的assistant消息（通常是前端占位符）
                if ("assistant".equals(chatMessage.getRole()) &&
                        (chatMessage.getContent() == null || chatMessage.getContent().trim().isEmpty())) {
                    continue;
                }

                Map<String, Object> message = new HashMap<>();
                message.put("role", chatMessage.getRole());
                message.put("content", chatMessage.getContent());

                // 如果有文档ID，也添加进去
                if (chatMessage.getDocIds() != null && !chatMessage.getDocIds().isEmpty()) {
                    message.put("doc_ids", chatMessage.getDocIds());
                }

                messages.add(message);
            }
        } else {
            // 回退到使用question字段
            log.debug("使用单条问题作为消息");
            Map<String, Object> userMessage = new HashMap<>();
            userMessage.put("role", "user");
            userMessage.put("content", request.getQuestion());
            messages.add(userMessage);
        }

        log.debug("构建的消息列表包含 {} 条有效消息", messages.size());
        return messages;
    }

    /**
     * 解析OpenAI格式的流式响应为列表
     */
    private List<ChatResponseDTO> parseOpenAiStreamResponseToList(String response, String sessionId) {
        List<ChatResponseDTO> responses = new ArrayList<>();

        try {
            log.debug("解析OpenAI流式响应为列表");
            StringBuilder fullContent = new StringBuilder();
            String[] lines = response.split("\n");

            for (String line : lines) {
                if (line.trim().startsWith("data:")) {
                    String jsonData = line.substring(5).trim();
                    if (!jsonData.isEmpty() && !jsonData.equals("[DONE]")) {
                        try {
                            JsonNode dataNode = objectMapper.readTree(jsonData);
                            if (dataNode.has("choices") && dataNode.get("choices").isArray() &&
                                    dataNode.get("choices").size() > 0) {

                                JsonNode firstChoice = dataNode.get("choices").get(0);
                                JsonNode finishReasonNode = firstChoice.get("finish_reason");

                                if (firstChoice.has("delta")) {
                                    JsonNode delta = firstChoice.get("delta");
                                    JsonNode contentNode = delta.get("content");
                                    if (contentNode != null && !contentNode.isNull()) {
                                        fullContent.append(contentNode.asText());

                                        // 创建增量响应
                                        ChatResponseDTO incrementalResponse = new ChatResponseDTO();
                                        incrementalResponse.setAnswer(fullContent.toString());
                                        incrementalResponse.setSessionId(sessionId);
                                        incrementalResponse.setFinished(finishReasonNode != null && !finishReasonNode.isNull());
                                        responses.add(incrementalResponse);
                                    }
                                }

                                // 检查是否为最后一条消息
                                if (finishReasonNode != null && "stop".equals(finishReasonNode.asText())) {
                                    ChatResponseDTO finalResponse = new ChatResponseDTO();
                                    finalResponse.setAnswer(fullContent.toString());
                                    finalResponse.setSessionId(sessionId);
                                    finalResponse.setFinished(true);
                                    responses.add(finalResponse);
                                    break;
                                }
                            }
                        } catch (Exception e) {
                            log.warn("解析OpenAI流式响应行失败: {}", e.getMessage());
                        }
                    }
                }
            }

            // 如果没有正常结束，手动添加结束标志
            if (responses.isEmpty() || responses.get(responses.size() - 1).getFinished() == null || !responses.get(responses.size() - 1).getFinished()) {
                ChatResponseDTO endResponse = new ChatResponseDTO();
                endResponse.setAnswer(fullContent.toString());
                endResponse.setSessionId(sessionId);
                endResponse.setFinished(true);
                responses.add(endResponse);
            }

        } catch (Exception e) {
            log.error("解析OpenAI流式响应列表失败", e);
            ChatResponseDTO errorResponse = new ChatResponseDTO();
            errorResponse.setError("OpenAI流式响应解析失败: " + e.getMessage());
            errorResponse.setFinished(true);
            responses.add(errorResponse);
        }

        log.debug("OpenAI流式响应解析完成，共 {} 条消息", responses.size());
        return responses;
    }

    @Override
    public List<ChatSessionDTO> getChatSessionsByAssistantId(String chatAssistantId, Long userId) {
        try {
            // 查询该助理的会话列表
            List<ChatSessionMapping> sessionMappings = chatSessionMappingMapper.selectByChatAssistantId(chatAssistantId);
            // 过滤用户的会话并转换为DTO
            return sessionMappings.stream()
                    .filter(session -> session.getUserId().equals(userId))
                    .map(this::convertToSessionDTO)
                    .collect(java.util.stream.Collectors.toList());

        } catch (ApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取会话列表失败: chatAssistantId={}, userId={}", chatAssistantId, userId, e);
            throw new ApiException("获取会话列表失败: " + e.getMessage());
        }
    }

    /**
     * 转换会话映射为DTO
     */
    private ChatSessionDTO convertToSessionDTO(ChatSessionMapping mapping) {
        ChatSessionDTO dto = new ChatSessionDTO();
        dto.setId(mapping.getId());
        dto.setUserId(mapping.getUserId());
        dto.setSessionId(mapping.getSessionId());
        dto.setChatAssistantId(mapping.getChatAssistantId());
        dto.setSessionName(mapping.getSessionName());
        dto.setStatus(mapping.getStatus());
        dto.setStatusDesc(mapping.getStatus() == 1 ? "启用" : "禁用");
        dto.setCreateTime(mapping.getCreateTime());
        dto.setUpdateTime(mapping.getUpdateTime());
        return dto;
    }

    /**
     * 解析RAGflow listSessions响应，转换为ChatResponseDTO列表
     */
    private List<ChatResponseDTO> parseChatHistoryFromListSessions(String response) {
        List<ChatResponseDTO> chatHistory = new ArrayList<>();

        try {
            JsonNode responseNode = objectMapper.readTree(response);
            if (responseNode.get("code").asInt() == 0) {
                JsonNode dataNode = responseNode.get("data");
                if (dataNode != null && dataNode.isArray() && dataNode.size() > 0) {
                    // 获取第一个会话（应该是我们要查询的会话）
                    JsonNode sessionNode = dataNode.get(0);

                    // 检查是否有消息历史
                    JsonNode messagesNode = sessionNode.get("messages");
                    if (messagesNode != null && messagesNode.isArray()) {
                        for (JsonNode messageNode : messagesNode) {
                            ChatResponseDTO chatResponse = new ChatResponseDTO();

                            String role = messageNode.get("role").asText();
                            String content = messageNode.get("content").asText();
                            String messageId = messageNode.has("id") ? messageNode.get("id").asText() : null;

                            // 根据角色设置消息内容
                            if ("user".equals(role)) {
                                // 用户消息：在answer字段前加上前缀标识
                                chatResponse.setAnswer("用户: " + content);
                            } else if ("assistant".equals(role)) {
                                // 助手消息作为回答
                                chatResponse.setAnswer(content);
                            }

                            chatResponse.setMessageId(messageId);
                            chatResponse.setSessionId(sessionNode.get("id").asText());
                            chatResponse.setFinished(true);
                            chatHistory.add(chatResponse);
                        }
                    } else {
                        // 如果没有消息历史，返回空的聊天记录
                        log.info("会话 {} 暂无聊天历史", sessionNode.get("id").asText());
                    }
                } else {
                    // 如果没有找到会话，可能会话不存在
                    log.warn("未找到指定的会话");
                    ChatResponseDTO errorResponse = new ChatResponseDTO();
                    errorResponse.setError("未找到指定的会话");
                    errorResponse.setFinished(true);
                    chatHistory.add(errorResponse);
                }
            } else {
                // 如果请求失败，返回错误信息
                ChatResponseDTO errorResponse = new ChatResponseDTO();
                errorResponse.setError("获取聊天历史失败: " + responseNode.get("message").asText());
                errorResponse.setFinished(true);
                chatHistory.add(errorResponse);
            }

        } catch (Exception e) {
            log.error("解析聊天历史响应失败", e);
            ChatResponseDTO errorResponse = new ChatResponseDTO();
            errorResponse.setError("解析聊天历史失败");
            errorResponse.setFinished(true);
            chatHistory.add(errorResponse);
        }

        return chatHistory;
    }

    @Override
    public List<ChatResponseDTO> conversationCompletion(ConversationCompletionRequestDTO request, Long userId) {
        try {
            // 验证会话归属权
            ChatSessionMapping sessionMapping = chatSessionMappingMapper.selectByUserIdAndSessionId(userId, request.getConversationId());
            if (sessionMapping == null) {
                throw new ApiException("会话不存在或无权限访问");
            }

            // 转换messages为JSON字符串
            String messagesJson = objectMapper.writeValueAsString(request.getMessages());

            // 调用RAGflow API
            RagflowSessionClient sessionClient = new RagflowSessionClient(ragflowBaseUrl, ragflowApiKey);
            String response = sessionClient.conversationCompletion(
                    request.getConversationId(),
                    messagesJson,
                    request.getStream() != null ? request.getStream() : true
            );

            // 解析响应
            if (request.getStream() != null && request.getStream()) {
                // 流式响应
                return parseStreamResponse(response);
            } else {
                // 非流式响应
                ChatResponseDTO chatResponse = parseRagflowResponse(response);
                List<ChatResponseDTO> responses = new ArrayList<>();
                responses.add(chatResponse);
                return responses;
            }

        } catch (ApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("Conversation completion失败: conversationId={}, userId={}", request.getConversationId(), userId, e);
            throw new ApiException("Conversation completion失败: " + e.getMessage());
        }
    }

    /**
     * 更新会话内容
     */
    private void updateSessionContent(String sessionId, String response) {
        try {
            JsonNode responseNode = objectMapper.readTree(response);
            if (responseNode.get("code").asInt() == 0) {
                JsonNode dataNode = responseNode.get("data");
                if (dataNode != null) {
                    // 获取消息和引用
                    String message = dataNode.has("messages") ? dataNode.get("messages").toString() : null;
                    String reference = dataNode.has("reference") ? dataNode.get("reference").toString() : null;

                    // 更新数据库
                    chatSessionMappingMapper.updateSessionContent(sessionId, message, reference);
                }
            }
        } catch (Exception e) {
            log.error("更新会话内容失败: sessionId={}", sessionId, e);
        }
    }

    /**
     * 更新流式聊天记录
     */
    private void updateStreamChatRecord(String sessionId, String userQuestion, ChatResponseDTO aiResponse) {
        try {
            // 获取当前会话的历史记录
            ChatSessionMapping currentSession = chatSessionMappingMapper.selectBySessionId(sessionId);
            List<Map<String, Object>> chatHistory = new ArrayList<>();
            List<Object> referenceHistory = new ArrayList<>();

            // 如果已有历史记录，先解析出来
            if (currentSession != null && StringUtils.hasText(currentSession.getMessage())) {
                try {
                    chatHistory = objectMapper.readValue(currentSession.getMessage(),
                            objectMapper.getTypeFactory().constructCollectionType(List.class,
                                    objectMapper.getTypeFactory().constructMapType(Map.class, String.class, Object.class)));
                } catch (Exception e) {
                    log.warn("解析历史聊天记录失败，将创建新的记录: {}", e.getMessage());
                    chatHistory = new ArrayList<>();
                }
            }

            // 如果已有参考资料历史，先解析出来
            if (currentSession != null && StringUtils.hasText(currentSession.getReference())) {
                try {
                    JsonNode existingReferenceNode = objectMapper.readTree(currentSession.getReference());
                    if (existingReferenceNode.isArray()) {
                        for (JsonNode ref : existingReferenceNode) {
                            referenceHistory.add(objectMapper.treeToValue(ref, Object.class));
                        }
                    } else {
                        // 兼容旧格式：如果是单个对象，转换为数组
                        referenceHistory.add(objectMapper.treeToValue(existingReferenceNode, Object.class));
                    }
                } catch (Exception e) {
                    log.warn("解析历史参考资料失败，将创建新的记录: {}", e.getMessage());
                    referenceHistory = new ArrayList<>();
                }
            }

            // 添加用户问题
            Map<String, Object> userMessage = new HashMap<>();
            userMessage.put("role", "user");
            userMessage.put("content", userQuestion);
            userMessage.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            chatHistory.add(userMessage);

            // 添加AI回答
            Map<String, Object> assistantMessage = new HashMap<>();
            assistantMessage.put("role", "assistant");
            assistantMessage.put("content", aiResponse.getAnswer());
            assistantMessage.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            if (StringUtils.hasText(aiResponse.getMessageId())) {
                assistantMessage.put("messageId", aiResponse.getMessageId());
            }
            chatHistory.add(assistantMessage);

            // 添加当前回答的参考资料到历史记录
            if (aiResponse.getReference() != null && !aiResponse.getReference().isEmpty()) {
                referenceHistory.add(aiResponse.getReference());
            } else {
                // 即使没有参考资料，也要添加一个空对象保持索引对应
                Map<String, Object> emptyReference = new HashMap<>();
                emptyReference.put("chunks", new ArrayList<>());
                emptyReference.put("doc_aggs", new ArrayList<>());
                emptyReference.put("total", 0);
                referenceHistory.add(emptyReference);
            }

            // 转换为JSON字符串
            String chatHistoryJson = objectMapper.writeValueAsString(chatHistory);
            String referenceJson = objectMapper.writeValueAsString(referenceHistory);

            // 更新数据库
            chatSessionMappingMapper.updateSessionContent(sessionId, chatHistoryJson, referenceJson);

            log.info("成功更新流式聊天记录: sessionId={}, 对话轮次={}, 参考资料数量={}",
                    sessionId, chatHistory.size() / 2, referenceHistory.size());
            log.debug("保存的参考资料内容: {}", referenceJson);

        } catch (Exception e) {
            log.error("更新流式聊天记录失败: sessionId={}", sessionId, e);
        }
    }

    @Override
    public List<ChatResponseDTO> getChatHistory(String sessionId, Long userId) {
        try {
            // 验证会话归属权
            ChatSessionMapping sessionMapping = chatSessionMappingMapper.selectByUserIdAndSessionId(userId, sessionId);
            if (sessionMapping == null) {
                throw new ApiException("会话不存在或无权限访问");
            }
            List<ChatResponseDTO> chatHistory = new ArrayList<>();
            // 从chat_session_mapping表中的message字段获取聊天历史
            if (StringUtils.hasText(sessionMapping.getMessage())) {
                try {
                    // 解析JSON格式的聊天记录
                    List<Map<String, Object>> messages = objectMapper.readValue(sessionMapping.getMessage(),
                            objectMapper.getTypeFactory().constructCollectionType(List.class,
                                    objectMapper.getTypeFactory().constructMapType(Map.class, String.class, Object.class)));

                    // 转换为ChatResponseDTO格式
                    for (Map<String, Object> message : messages) {
                        ChatResponseDTO responseDTO = new ChatResponseDTO();

                        String role = (String) message.get("role");
                        String content = (String) message.get("content");
                        String timestamp = (String) message.get("timestamp");
                        String messageId = (String) message.get("messageId");

                        // 根据角色设置消息内容
                        if ("user".equals(role)) {
                            responseDTO.setAnswer("【用户】" + content);
                        } else if ("assistant".equals(role)) {
                            responseDTO.setAnswer(content);
                        }

                        responseDTO.setSessionId(sessionId);
                        responseDTO.setMessageId(messageId);
                        responseDTO.setFinished(true);

                        // 如果有时间戳，可以添加到响应中（作为扩展字段）
                        if (StringUtils.hasText(timestamp)) {
                            // 可以在这里添加时间戳处理逻辑
                            log.debug("消息时间戳: {}", timestamp);
                        }

                        chatHistory.add(responseDTO);
                    }

                    log.info("成功从本地获取聊天历史: sessionId={}, 消息数量={}", sessionId, chatHistory.size());

                } catch (Exception e) {
                    log.error("解析本地聊天历史失败: sessionId={}", sessionId, e);
                    throw new ApiException("解析聊天历史失败: " + e.getMessage());
                }
            } else {
                log.info("会话暂无聊天历史: sessionId={}", sessionId);
            }

            return chatHistory;

        } catch (ApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取聊天历史失败: sessionId={}, userId={}", sessionId, userId, e);
            throw new ApiException("获取聊天历史失败: " + e.getMessage());
        }
    }

    @Override
    public ChatHistoryResponseDTO getChatHistoryStandard(String sessionId, Long userId) {
        try {
            // 验证会话归属权
            ChatSessionMapping sessionMapping = chatSessionMappingMapper.selectByUserIdAndSessionId(userId, sessionId);
            if (sessionMapping == null) {
                throw new ApiException("会话不存在或无权限访问");
            }

            // 创建响应对象
            ChatHistoryResponseDTO response = new ChatHistoryResponseDTO();
            response.setCode(0);
            response.setMessage("success");

            // 创建数据对象
            ChatHistoryResponseDTO.ChatHistoryData data = new ChatHistoryResponseDTO.ChatHistoryData();

            // 设置基本信息
            data.setAvatar("data:image/png;base64,EcIdQSCTA8b"); // 默认头像
            data.setDialog_id(sessionMapping.getChatAssistantId()); // 使用助手ID作为dialog_id
            data.setId(sessionId);
            data.setUser_id(userId.toString());
            data.setName(sessionMapping.getSessionName() != null ? sessionMapping.getSessionName() : "你好");

            // 设置时间信息
            LocalDateTime createTime = sessionMapping.getCreateTime();
            LocalDateTime updateTime = sessionMapping.getUpdateTime();

            if (createTime != null) {
                ZonedDateTime createZdt = createTime.atZone(ZoneId.systemDefault()).withZoneSameInstant(ZoneId.of("GMT"));
                data.setCreate_date(createZdt.format(DateTimeFormatter.ofPattern("EEE, dd MMM yyyy HH:mm:ss 'GMT'")));
                data.setCreate_time(createZdt.toInstant().toEpochMilli());
            }

            if (updateTime != null) {
                ZonedDateTime updateZdt = updateTime.atZone(ZoneId.systemDefault()).withZoneSameInstant(ZoneId.of("GMT"));
                data.setUpdate_date(updateZdt.format(DateTimeFormatter.ofPattern("EEE, dd MMM yyyy HH:mm:ss 'GMT'")));
                data.setUpdate_time(updateZdt.toInstant().toEpochMilli());
            }

            // 解析聊天消息
            List<ChatHistoryResponseDTO.ChatMessage> messages = new ArrayList<>();
            List<ChatHistoryResponseDTO.Reference> references = new ArrayList<>();

            if (StringUtils.hasText(sessionMapping.getMessage())) {
                try {
                    List<Map<String, Object>> messageList = objectMapper.readValue(sessionMapping.getMessage(),
                            objectMapper.getTypeFactory().constructCollectionType(List.class,
                                    objectMapper.getTypeFactory().constructMapType(Map.class, String.class, Object.class)));

                    // 统计answer的数量，用于创建对应的reference
                    int answerCount = 0;

                    for (Map<String, Object> msg : messageList) {
                        ChatHistoryResponseDTO.ChatMessage chatMessage = new ChatHistoryResponseDTO.ChatMessage();
                        chatMessage.setContent((String) msg.get("content"));
                        chatMessage.setRole((String) msg.get("role"));
                        chatMessage.setId((String) msg.get("messageId"));

                        // 处理时间戳
                        Object timestamp = msg.get("timestamp");
                        if (timestamp != null) {
                            try {
                                if (timestamp instanceof String) {
                                    LocalDateTime msgTime = LocalDateTime.parse((String) timestamp, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
                                    chatMessage.setCreated_at((double) msgTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() / 1000.0);
                                } else if (timestamp instanceof Number) {
                                    chatMessage.setCreated_at(((Number) timestamp).doubleValue());
                                }
                            } catch (Exception e) {
                                log.warn("解析消息时间戳失败: {}", timestamp, e);
                                chatMessage.setCreated_at((double) System.currentTimeMillis() / 1000.0);
                            }
                        } else {
                            chatMessage.setCreated_at((double) System.currentTimeMillis() / 1000.0);
                        }

                        messages.add(chatMessage);

                        // 如果是assistant角色的消息，计数answer
                        if ("assistant".equals(chatMessage.getRole())) {
                            answerCount++;
                        }
                    }

                    // 解析参考资料
                    if (StringUtils.hasText(sessionMapping.getReference())) {
                        try {
                            log.debug("解析参考资料内容: {}", sessionMapping.getReference());
                            JsonNode referenceNode = objectMapper.readTree(sessionMapping.getReference());
                            if (referenceNode.isArray()) {
                                // 如果是数组，按顺序解析，确保每个answer都有对应的reference
                                for (int i = 0; i < answerCount; i++) {
                                    if (i < referenceNode.size()) {
                                        // 有对应的reference数据
                                        ChatHistoryResponseDTO.Reference reference = parseReferenceNode(referenceNode.get(i));
                                        references.add(reference);
                                    } else {
                                        // 没有对应的reference数据，创建空reference
                                        ChatHistoryResponseDTO.Reference emptyRef = new ChatHistoryResponseDTO.Reference();
                                        emptyRef.setChunks(new ArrayList<>());
                                        emptyRef.setDoc_aggs(new ArrayList<>());
                                        emptyRef.setTotal(0);
                                        references.add(emptyRef);
                                    }
                                }
                            } else {
                                // 兼容旧格式：如果是单个对象，只为第一个answer添加，其他补空
                                for (int i = 0; i < answerCount; i++) {
                                    if (i == 0) {
                                        ChatHistoryResponseDTO.Reference reference = parseReferenceNode(referenceNode);
                                        references.add(reference);
                                    } else {
                                        ChatHistoryResponseDTO.Reference emptyRef = new ChatHistoryResponseDTO.Reference();
                                        emptyRef.setChunks(new ArrayList<>());
                                        emptyRef.setDoc_aggs(new ArrayList<>());
                                        emptyRef.setTotal(0);
                                        references.add(emptyRef);
                                    }
                                }
                            }
                        } catch (Exception e) {
                            log.error("解析参考资料失败: sessionId={}", sessionId, e);
                            // 解析失败时，为所有answer创建空reference
                            for (int i = 0; i < answerCount; i++) {
                                ChatHistoryResponseDTO.Reference emptyRef = new ChatHistoryResponseDTO.Reference();
                                emptyRef.setChunks(new ArrayList<>());
                                emptyRef.setDoc_aggs(new ArrayList<>());
                                emptyRef.setTotal(0);
                                references.add(emptyRef);
                            }
                        }
                    } else {
                        // 没有参考资料数据，为所有answer创建空reference
                        for (int i = 0; i < answerCount; i++) {
                            ChatHistoryResponseDTO.Reference emptyRef = new ChatHistoryResponseDTO.Reference();
                            emptyRef.setChunks(new ArrayList<>());
                            emptyRef.setDoc_aggs(new ArrayList<>());
                            emptyRef.setTotal(0);
                            references.add(emptyRef);
                        }
                    }

                } catch (Exception e) {
                    log.error("解析聊天消息失败: sessionId={}", sessionId, e);
                }
            }

            data.setMessage(messages);
            data.setReference(references);

            response.setData(data);
            return response;

        } catch (ApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取标准格式聊天历史失败: sessionId={}, userId={}", sessionId, userId, e);
            throw new ApiException("获取聊天历史失败: " + e.getMessage());
        }
    }

    /**
     * 解析参考资料节点
     */
    private ChatHistoryResponseDTO.Reference parseReferenceNode(JsonNode referenceNode) {
        ChatHistoryResponseDTO.Reference reference = new ChatHistoryResponseDTO.Reference();

        // 解析chunks
        List<ChatHistoryResponseDTO.Chunk> chunks = new ArrayList<>();
        if (referenceNode.has("chunks") && referenceNode.get("chunks").isArray()) {
            for (JsonNode chunkNode : referenceNode.get("chunks")) {
                ChatHistoryResponseDTO.Chunk chunk = new ChatHistoryResponseDTO.Chunk();
                chunk.setContent(chunkNode.has("content") ? chunkNode.get("content").asText() : "");
                chunk.setDataset_id(chunkNode.has("dataset_id") ? chunkNode.get("dataset_id").asText() : "");
                chunk.setDoc_type(chunkNode.has("doc_type") ? chunkNode.get("doc_type").asText() : "");
                chunk.setDocument_id(chunkNode.has("document_id") ? chunkNode.get("document_id").asText() : "");
                chunk.setDocument_name(chunkNode.has("document_name") ? chunkNode.get("document_name").asText() : "");
                chunk.setId(chunkNode.has("id") ? chunkNode.get("id").asText() : "");
                chunk.setImage_id(chunkNode.has("image_id") ? chunkNode.get("image_id").asText() : "");

                // 解析positions
                List<List<Integer>> positions = new ArrayList<>();
                if (chunkNode.has("positions") && chunkNode.get("positions").isArray()) {
                    for (JsonNode positionArray : chunkNode.get("positions")) {
                        List<Integer> position = new ArrayList<>();
                        if (positionArray.isArray()) {
                            for (JsonNode pos : positionArray) {
                                position.add(pos.asInt());
                            }
                        }
                        positions.add(position);
                    }
                }
                chunk.setPositions(positions);
                chunks.add(chunk);
            }
        }
        reference.setChunks(chunks);

        // 解析doc_aggs
        List<ChatHistoryResponseDTO.DocAgg> docAggs = new ArrayList<>();
        if (referenceNode.has("doc_aggs") && referenceNode.get("doc_aggs").isArray()) {
            for (JsonNode docAggNode : referenceNode.get("doc_aggs")) {
                ChatHistoryResponseDTO.DocAgg docAgg = new ChatHistoryResponseDTO.DocAgg();
                docAgg.setCount(docAggNode.has("count") ? docAggNode.get("count").asInt() : 0);
                docAgg.setDoc_id(docAggNode.has("doc_id") ? docAggNode.get("doc_id").asText() : "");
                docAgg.setDoc_name(docAggNode.has("doc_name") ? docAggNode.get("doc_name").asText() : "");
                docAggs.add(docAgg);
            }
        }
        reference.setDoc_aggs(docAggs);

        // 设置total
        reference.setTotal(referenceNode.has("total") ? referenceNode.get("total").asInt() : 0);

        return reference;
    }

    @Override
    public void streamChatWithCallback(ChatRequestDTO request, Long userId, Consumer<ChatResponseDTO> responseCallback) {
        try {
            // 获取或创建聊天助手ID
            String chatAssistantId = getOrCreateChatAssistantForRequest(request, userId);
            String sessionId = request.getSessionId();

            // 如果没有会话ID，创建新会话
            if (!StringUtils.hasText(sessionId)) {
                sessionId = createChatSession(chatAssistantId, "新会话", userId);
                // 更新请求中的sessionId，以便后续使用
                request.setSessionId(sessionId);
            }

            final String finalSessionId = sessionId;
            // 用于保存最完整的回答（只保存最后一个完整的回答，而不是累加）
            final ChatResponseDTO[] latestCompleteResponse = {null};

            // 检查聊天助手是否绑定了数据集
            boolean hasDatasets = checkChatAssistantHasDatasets(chatAssistantId, userId);

            if (hasDatasets) {
                // 使用RAGflow会话API进行流式聊天
                handleStreamChatWithRAG(chatAssistantId, request, finalSessionId, latestCompleteResponse, responseCallback);
            } else {
                // 没有数据集绑定，使用Spring AI处理
                handleStreamChatWithSpringAI(chatAssistantId, request, finalSessionId, userId, responseCallback);
            }
        } catch (Exception e) {
            log.error("流式聊天回调处理异常", e);
            ChatResponseDTO errorResponse = new ChatResponseDTO();
            errorResponse.setError("聊天处理失败: " + e.getMessage());
            errorResponse.setFinished(true);
            responseCallback.accept(errorResponse);
        }
    }

    /**
     * 使用RAG流式聊天处理
     */
    private void handleStreamChatWithRAG(String chatAssistantId, ChatRequestDTO request, String finalSessionId,
                                         ChatResponseDTO[] latestCompleteResponse, Consumer<ChatResponseDTO> responseCallback) {
        try {
            RagflowSessionClient sessionClient = new RagflowSessionClient(ragflowBaseUrl, ragflowApiKey);

            // 异步处理RAGflow响应
            sessionClient.converseWithChatAssistantStream(
                    chatAssistantId,
                    request.getQuestion(),
                    finalSessionId,
                    ragflowUserId,
                    (String rawLine) -> {
                        try {
                            // 解析流式响应行
                            ChatResponseDTO response = parseStreamLine(rawLine, finalSessionId);
                            if (response != null) {
                                // 如果有回答内容，保存为最新的完整回答（覆盖之前的，不累加）
                                if (StringUtils.hasText(response.getAnswer())) {
                                    latestCompleteResponse[0] = response;
                                }

                                // 实时回调给前端
                                responseCallback.accept(response);

                                // 如果是完成标志，存储聊天记录
                                if (response.getFinished() != null && response.getFinished()) {
                                    // 异步存储聊天记录，避免阻塞流式响应
                                    CompletableFuture.runAsync(() -> {
                                        try {
                                            // 使用最完整的回答进行存储
                                            ChatResponseDTO completeResponse = latestCompleteResponse[0];
                                            if (completeResponse != null && StringUtils.hasText(completeResponse.getAnswer())) {
                                                completeResponse.setSessionId(finalSessionId);
                                                // 如果完成响应有reference，使用完成响应的reference
                                                if (response.getReference() != null) {
                                                    completeResponse.setReference(response.getReference());
                                                }
                                                updateStreamChatRecord(finalSessionId, request.getQuestion(), completeResponse);
                                                log.info("成功存储最完整的AI回答，长度: {}", completeResponse.getAnswer().length());
                                            } else {
                                                log.warn("流式响应中未找到有效的回答内容，sessionId: {}", finalSessionId);
                                            }
                                        } catch (Exception e) {
                                            log.error("存储聊天记录失败", e);
                                        }
                                    });
                                }
                            }
                        } catch (Exception e) {
                            log.error("解析流式响应行失败: {}", rawLine, e);
                            // 发送错误响应
                            ChatResponseDTO errorResponse = new ChatResponseDTO();
                            errorResponse.setError("解析响应失败: " + e.getMessage());
                            errorResponse.setFinished(true);
                            responseCallback.accept(errorResponse);
                        }
                    }
            );
        } catch (Exception e) {
            log.error("流式聊天回调处理异常", e);
            ChatResponseDTO errorResponse = new ChatResponseDTO();
            errorResponse.setError("聊天处理失败: " + e.getMessage());
            errorResponse.setFinished(true);
            responseCallback.accept(errorResponse);
        }
    }

    /**
     * 使用Spring AI进行流式聊天处理（无数据集绑定时）
     * TODO: 后续集成MCP处理
     */
    private void handleStreamChatWithSpringAI(String chatAssistantId, ChatRequestDTO request, String finalSessionId,
                                              Long userId, Consumer<ChatResponseDTO> responseCallback) {
        try {
            log.info("使用Spring AI处理无数据集绑定的聊天请求: chatAssistantId={}, userId={}", chatAssistantId, userId);
            // 使用Spring AI直接调用大模型，传入chatAssistantId以获取助手配置
            processSpringAIStreaming(request, finalSessionId, chatAssistantId, responseCallback, userId);

        } catch (Exception e) {
            log.error("Spring AI流式聊天处理失败", e);
            ChatResponseDTO errorResponse = new ChatResponseDTO();
            errorResponse.setError("Spring AI聊天处理失败: " + e.getMessage());
            errorResponse.setFinished(true);
            responseCallback.accept(errorResponse);
        }
    }

    /**
     * Spring AI流式处理核心逻辑
     */
    private void processSpringAIStreaming(ChatRequestDTO request, String sessionId, String chatAssistantId, Consumer<ChatResponseDTO> responseCallback, Long userId) {
        try {
            // 发送开始处理的状态
            ChatResponseDTO startResponse = new ChatResponseDTO();
            startResponse.setAnswer("正在通过AI助手为您处理...");
            startResponse.setFinished(false);
            startResponse.setSessionId(sessionId);
            responseCallback.accept(startResponse);

            // 使用Spring AI进行流式调用
            CompletableFuture.runAsync(() -> {
                try {
                    // 使用真正的流式调用
                    Flux<String> responseStream = callAIModelStream(request, chatAssistantId, null, null, userId);
                    StringBuilder fullAnswer = new StringBuilder();
                    AtomicReference<String> finalAnswer = new AtomicReference<>("");
                    // 订阅流式响应
                    responseStream
                            .doOnNext(chunk -> {
                                // 处理每个流式片段
                                fullAnswer.append(chunk);

                                // 创建流式响应
                                ChatResponseDTO streamResponse = new ChatResponseDTO();
                                streamResponse.setAnswer(fullAnswer.toString());
                                streamResponse.setFinished(false);
                                streamResponse.setSessionId(sessionId);

                                // 实时回调给前端
                                responseCallback.accept(streamResponse);
                            })
                            .doOnComplete(() -> {
                                // 流式响应完成
                                finalAnswer.set(fullAnswer.toString());

                                // 发送最终完成响应
                                ChatResponseDTO finalResponse = new ChatResponseDTO();
                                finalResponse.setAnswer(finalAnswer.get());
                                finalResponse.setFinished(true);
                                finalResponse.setSessionId(sessionId);
                                responseCallback.accept(finalResponse);

                                // 异步存储聊天记录
                                CompletableFuture.runAsync(() -> {
                                    try {
                                        updateStreamChatRecord(sessionId, request.getQuestion(), finalResponse);
                                        log.info("成功存储Spring AI流式聊天记录，sessionId: {}", sessionId);
                                    } catch (Exception e) {
                                        log.error("存储流式聊天记录失败", e);
                                    }
                                });
                            })
                            .doOnError(error -> {
                                // 处理流式响应错误
                                log.error("Spring AI流式调用过程中发生错误", error);
                                ChatResponseDTO errorResponse = new ChatResponseDTO();
                                errorResponse.setError("AI流式处理失败: " + error.getMessage());
                                errorResponse.setFinished(true);
                                errorResponse.setSessionId(sessionId);
                                responseCallback.accept(errorResponse);
                            })
                            .subscribe(); // 启动流式处理

                } catch (Exception e) {
                    log.error("Spring AI流式调用初始化失败，回退到模拟响应", e);
                    // 回退到模拟流式响应
                    String mockAnswer = generateMockSpringAIResponse(request.getQuestion());
                    sendMockStreamingResponse(mockAnswer, sessionId, responseCallback);
                }
            });

        } catch (Exception e) {
            log.error("Spring AI流式处理失败", e);
            ChatResponseDTO errorResponse = new ChatResponseDTO();
            errorResponse.setError("AI处理失败: " + e.getMessage());
            errorResponse.setFinished(true);
            responseCallback.accept(errorResponse);
        }
    }

    /**
     * 使用Spring AI ChatClient进行流式调用
     *
     * @param request         用户请求
     * @param chatAssistantId 聊天助手ID（用于获取模型配置）
     * @param providerName    模型提供商名称（可选，优先级低于助手配置）
     * @param modelName       模型名称（可选，优先级低于助手配置）
     * @return AI回答的流式响应
     */
    private Flux<String> callAIModelStream(ChatRequestDTO request, String chatAssistantId, String providerName, String modelName, Long userId) {
        try {
            // 适配历史消息
            List<Message> messages = new ArrayList<>();
            request.getMessages().forEach(message -> {
                UserMessage msg = new UserMessage(message.getContent());
                messages.add(msg);
            });
            // 获取聊天助手配置
            String actualProviderName = providerName;
            String actualModelName = modelName;
            String systemPrompt = "你是一个智能的AI助手。请根据用户的问题提供详细、准确的回答。";
            Double temperature = 0.7;
            Integer maxTokens = 512;

            // 如果提供了chatAssistantId，尝试获取助手的模型配置
            if (StringUtils.hasText(chatAssistantId)) {
                try {
                    ChatAssistantMappingDTO assistantMapping = chatAssistantMappingService.getMappingByUserAndAssistant(userId, chatAssistantId);
                    if (assistantMapping != null) {
                        // 获取助手详情以获取模型配置
                        String assistantDetails = getChatAssistantDetailsInternal(chatAssistantId);
                        if (StringUtils.hasText(assistantDetails)) {
                            JsonNode detailsNode = objectMapper.readTree(assistantDetails);
                            if (detailsNode.get("code").asInt() == 0) {
                                JsonNode dataNode = detailsNode.get("data");

                                // 解析LLM配置
                                JsonNode llmSettingNode = dataNode.get("llm");
                                if (llmSettingNode != null) {
                                    JsonNode modelNameNode = llmSettingNode.get("model_name");
                                    if (modelNameNode != null && StringUtils.hasText(modelNameNode.asText())) {
                                        actualModelName = modelNameNode.asText();
                                        // 根据模型名称推断提供商
                                        actualProviderName = inferProviderFromModelName(actualModelName);
                                        log.info("从聊天助手配置获取模型: provider={}, model={}", actualProviderName, actualModelName);
                                    }
                                    // 获取温度参数
                                    JsonNode temperatureNode = llmSettingNode.get("temperature");
                                    if (temperatureNode != null) {
                                        temperature = temperatureNode.asDouble();
                                    }
                                    // 获取最大令牌数
                                    JsonNode maxTokensNode = llmSettingNode.get("max_tokens");
                                    if (maxTokensNode != null) {
                                        maxTokens = maxTokensNode.asInt();
                                    }
                                }

                                // 解析提示词配置
                                JsonNode promptConfigNode = dataNode.get("prompt");
                                if (promptConfigNode != null) {
                                    JsonNode promptNode = promptConfigNode.get("prompt");
                                    if (promptNode != null && StringUtils.hasText(promptNode.asText())) {
                                        systemPrompt = promptNode.asText();
                                        log.info("从聊天助手配置获取自定义提示词");
                                    }
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    log.warn("获取聊天助手配置失败，使用默认配置: {}", e.getMessage());
                }
            }

            // 获取ChatModel
            ChatModel chatModel;
            if (actualProviderName != null && actualModelName != null) {
                String realModelName = extractRealModelName(actualModelName);
                chatModel = modelProviderService.getChatModel(actualProviderName, realModelName);
            } else if (actualProviderName != null) {
                chatModel = modelProviderService.getChatModel(actualProviderName);
            } else {
                chatModel = modelProviderService.getDefaultChatModel();
            }
            // 创建ChatClient并设置默认选项
            ChatClient.Builder chatClientBuilder = ChatClient.builder(chatModel);

            // 根据提供商类型设置相应的选项
            try {
                if ("OpenAI".equals(actualProviderName)) {
                    OpenAiChatOptions options = OpenAiChatOptions.builder()
                            .temperature(temperature)
                            .maxTokens(maxTokens)
                            .build();
                    chatClientBuilder.defaultOptions(options);
                    log.info("成功设置OpenAI选项: temperature={}, maxTokens={}", temperature, maxTokens);
                } else if ("AzureOpenAI".equals(actualProviderName)) {
                    String realModelName = extractRealModelName(actualModelName);
                    AzureOpenAiChatOptions options = AzureOpenAiChatOptions.builder()
                            .deploymentName(realModelName)
                            .temperature(temperature)
                            .maxTokens(maxTokens)
                            .build();
                    chatClientBuilder.defaultOptions(options);
                    log.info("成功设置AzureOpenAI选项: deploymentName={}, temperature={}, maxTokens={}", realModelName, temperature, maxTokens);
                } else {
                    // Ollama 或其他提供商
                    String realModelName = extractRealModelName(actualModelName);
                    OllamaOptions options = OllamaOptions.builder()
                            .model(realModelName)
                            .topP(temperature)
                            .build();
                    chatClientBuilder.defaultOptions(options);
                    log.info("成功设置Ollama选项: model={}, topP={}", realModelName, temperature);
                }
            } catch (Exception e) {
                log.warn("设置模型选项失败，使用默认选项: provider={}, model={}, error={}", actualProviderName, actualModelName, e.getMessage());
                // 继续执行，使用默认选项
            }
            // 构建ChatClient
            ChatClient chatClient = chatClientBuilder.build();
            // 使用ChatClient进行流式调用，集成MCP工具
            try {
                log.info("使用ChatClient进行流式调用: provider={}, model={}", actualProviderName, actualModelName);
                // 检查是否有MCP工具可用，如果有则提取函数名称使用
                if (mcpToolCallbackProvider.isPresent()) {
                    log.info("使用MCP ToolCallbackProvider进行流式调用");
                    // 从ToolCallbackProvider中获取可用的函数名称
                    List<String> availableFunctions = getMcpFunctionNames();
                    ToolCallbackProvider toolCallbackProvider = mcpToolCallbackProvider.get();
                    if (!availableFunctions.isEmpty()) {
                        log.info("发现MCP函数: {}", availableFunctions);
                        return chatClient.prompt()
                                .system(systemPrompt)
                                .messages(messages)
                                .user(request.getQuestion())
                                .tools(toolCallbackProvider)
                                .stream()
                                .content();
                    } else {
                        log.warn("MCP ToolCallbackProvider可用但未发现函数");
                        return chatClient.prompt()
                                .system(systemPrompt)
                                .messages(messages)
                                .user(request.getQuestion())
                                .stream()
                                .content();
                    }
                } else {
                    log.info("使用标准模式进行流式调用（无MCP工具）");
                    return chatClient.prompt()
                            .system(systemPrompt)
                            .user(request.getQuestion())
                            .stream()
                            .content();
                }
            } catch (Exception e) {
                log.error("ChatClient流式调用失败: {}", e.getMessage());
                // 返回错误信息的流
                return Flux.just("AI流式处理失败: " + e.getMessage());
            }

        } catch (Exception e) {
            log.error("Spring AI ChatClient流式调用失败", e);
            // 返回错误信息的流
            return Flux.just("AI处理失败: " + e.getMessage());
        }
    }

    /**
     * 根据模型名称推断提供商
     */
    private String inferProviderFromModelName(String modelName) {
        if (modelName == null) {
            return null;
        }

        String lowerModelName = modelName.toLowerCase();

        // OpenAI模型
        if (lowerModelName.contains("gpt")) {
            return "OpenAI";
        }

        // Azure OpenAI模型
        if (lowerModelName.contains("azure")) {
            return "AzureOpenAI";
        }

        // Ollama模型（默认）
        return "Ollama";
    }

    /**
     * 内部获取聊天助手详情（不进行权限验证）
     */
    private String getChatAssistantDetailsInternal(String assistantId) {
        try {
            // 调用RAGflow API获取助理详情
            RagflowChatClient chatClient = new RagflowChatClient(ragflowBaseUrl, ragflowApiKey);
            String response = chatClient.listChats(1, 1, null, false, null, assistantId);

            JsonNode responseNode = objectMapper.readTree(response);
            if (responseNode.get("code").asInt() == 0) {
                JsonNode dataNode = responseNode.get("data");
                if (dataNode != null && dataNode.isArray() && dataNode.size() > 0) {
                    JsonNode assistantNode = dataNode.get(0);

                    // 构建与RAGflow原生格式一致的响应
                    Map<String, Object> result = new HashMap<>();
                    result.put("code", 0);
                    result.put("data", objectMapper.convertValue(assistantNode, Map.class));
                    result.put("message", "success");

                    return objectMapper.writeValueAsString(result);
                }
            }
            return null;
        } catch (Exception e) {
            log.error("获取助手详情失败: assistantId={}", assistantId, e);
            return null;
        }
    }

    /**
     * 发送模拟流式响应（当API Key未配置时使用）
     */
    private void sendMockStreamingResponse(String fullAnswer, String sessionId, Consumer<ChatResponseDTO> responseCallback) {
        // 分段发送响应，模拟流式效果
        String[] words = fullAnswer.split(" ");
        StringBuilder currentAnswer = new StringBuilder();

        for (int i = 0; i < words.length; i++) {
            currentAnswer.append(words[i]).append(" ");

            ChatResponseDTO streamResponse = new ChatResponseDTO();
            streamResponse.setAnswer(currentAnswer.toString().trim());
            streamResponse.setFinished(false);
            streamResponse.setSessionId(sessionId);
            responseCallback.accept(streamResponse);
        }

        // 发送最终完成响应
        ChatResponseDTO finalResponse = new ChatResponseDTO();
        finalResponse.setAnswer(fullAnswer);
        finalResponse.setFinished(true);
        finalResponse.setSessionId(sessionId);
        responseCallback.accept(finalResponse);

        // 异步存储聊天记录
        CompletableFuture.runAsync(() -> {
            try {
                updateStreamChatRecord(sessionId, "用户问题", finalResponse);
                log.info("成功存储模拟聊天记录，sessionId: {}", sessionId);
            } catch (Exception e) {
                log.error("存储模拟聊天记录失败", e);
            }
        });
    }

    /**
     * 生成模拟的Spring AI响应
     */
    private String generateMockSpringAIResponse(String question) {
        // 这里是临时的模拟响应，实际使用时需要替换为真正的Spring AI调用
        return "这是通过Spring AI Ollama模型生成的回答：" + question + "。我正在基于本地部署的大语言模型为您提供帮助，当前聊天助手未绑定特定的知识库数据集，因此我将基于我的预训练知识为您解答。如果您需要更专业的领域知识，建议为此聊天助手绑定相关的知识库数据集。";
    }

    /**
     * 解析流式响应行
     */
    private ChatResponseDTO parseStreamLine(String line, String sessionId) {
        try {
            // RAGflow的流式响应是以"data:"开头的行
            if (line.startsWith("data:")) {
                String jsonData = line.substring(5).trim();
                if (!jsonData.isEmpty()) {
                    JsonNode responseNode = objectMapper.readTree(jsonData);

                    // 检查是否为结束标志
                    if (isStreamEndMarker(responseNode)) {
                        ChatResponseDTO endResponse = new ChatResponseDTO();
                        endResponse.setFinished(true);
                        endResponse.setSessionId(sessionId);
                        return endResponse;
                    }

                    // 解析正常的数据响应
                    return parseStreamMessage(responseNode);
                }
            }
            return null;
        } catch (Exception e) {
            log.error("解析流式响应行失败: {}", line, e);
            return null;
        }
    }

    /**
     * 检查类或其方法是否有@Tool注解
     */
    private boolean hasToolAnnotation(Class<?> clazz) {
        try {
            // 检查类级别的@Tool注解
            if (clazz.isAnnotationPresent(org.springframework.ai.tool.annotation.Tool.class)) {
                return true;
            }

            // 检查方法级别的@Tool注解
            Method[] methods = clazz.getDeclaredMethods();
            for (Method method : methods) {
                if (method.isAnnotationPresent(org.springframework.ai.tool.annotation.Tool.class)) {
                    return true;
                }
            }

            return false;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 提取真实的模型名称，去掉供应商后缀
     * 例如: "qwen3:8b@Ollama" -> "qwen3:8b"
     * 如果没有@符号，返回原始名称
     *
     * @param modelNameWithProvider 可能包含供应商后缀的模型名称
     * @return 真实的模型名称
     */
    private String extractRealModelName(String modelNameWithProvider) {
        if (modelNameWithProvider == null || modelNameWithProvider.trim().isEmpty()) {
            return modelNameWithProvider;
        }

        int atIndex = modelNameWithProvider.indexOf('@');
        if (atIndex > 0) {
            return modelNameWithProvider.substring(0, atIndex);
        }

        return modelNameWithProvider;
    }

    @Override
    public ChatAssistantMappingDTO getOrCreateDefaultChatAssistant(Long userId) {
        try {
            log.info("获取或创建用户默认聊天助手: userId={}", userId);

            // 1. 首先查找是否已有默认助手
            List<ChatAssistantMappingDTO> existingAssistants = chatAssistantMappingService.getUserChatAssistants(userId);

            // 查找名为"默认助手"的助手
            ChatAssistantMappingDTO defaultAssistant = existingAssistants.stream()
                    .filter(assistant -> "默认助手".equals(assistant.getAssistantName()))
                    .findFirst()
                    .orElse(null);

            if (defaultAssistant != null) {
                log.info("找到现有默认助手: assistantId={}", defaultAssistant.getRagflowChatAssistantId());
                return defaultAssistant;
            }

            // 2. 如果没有默认助手，则创建一个
            log.info("未找到默认助手，开始创建默认助手");

            // 创建RAGflow聊天客户端
            RagflowChatClient chatClient = new RagflowChatClient(ragflowBaseUrl, ragflowApiKey);

            // 构建默认LLM配置
            String llmConfig = buildDefaultLLMConfig();

            // 构建默认提示配置
            String promptConfig = buildDefaultPromptConfig();

            // 调用RAGflow API创建默认聊天助手
            String[] kbIdsArray = new String[0];
            String response = chatClient.createChat(
                    "默认助手" + System.currentTimeMillis(),
                    "", // 头像
                    new String[0], // 知识库ID（可以为空）
                    llmConfig,
                    promptConfig
            );

            // 解析响应
            JsonNode responseNode = objectMapper.readTree(response);
            if (responseNode.get("code").asInt() == 0) {
                String ragflowAssistantId = responseNode.get("data").get("id").asText();

                // 创建本地映射记录
                Long mappingId = chatAssistantMappingService.createMapping(
                        userId,
                        ragflowAssistantId,
                        "默认助手",
                        null, // 图标为空
                        new ArrayList<>(), // 空的RAGflow知识库ID列表
                        null, // 本地知识库ID为空
                        ChatAssistantMapping.AssistantType.RAGFLOW_KB.getCode()
                );

                // 重新查询创建的映射记录并返回
                ChatAssistantMappingDTO newDefaultAssistant = chatAssistantMappingService.getMappingByUserAndAssistant(userId, ragflowAssistantId);

                log.info("成功创建默认聊天助手: assistantId={}, mappingId={}", ragflowAssistantId, mappingId);
                return newDefaultAssistant;
            } else {
                String errorMessage = responseNode.get("message").asText();
                throw new ApiException("创建默认聊天助手失败: " + errorMessage);
            }

        } catch (ApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取或创建默认聊天助手失败: userId={}", userId, e);
            throw new ApiException("获取或创建默认聊天助手失败: " + e.getMessage());
        }
    }

    /**
     * 构建默认LLM配置
     */
    private String buildDefaultLLMConfig() {
        try {
            // 获取默认模型信息
            ChatModel defaultChatModel = modelProviderService.getDefaultChatModel();

            // 从ModelConfig获取默认模型名称和配置
            String defaultModelName = modelConfig.getDefaultModel();
            String defaultProvider = modelConfig.getDefaultProvider();
            Double defaultTemperature = 0.7;
            Integer defaultMaxTokens = 51200;

            // 如果没有配置默认模型，尝试从默认提供商配置中获取
            if (!StringUtils.hasText(defaultModelName) && StringUtils.hasText(defaultProvider)) {
                ModelConfig.ProviderConfig providerConfig = modelConfig.getProviders().get(defaultProvider);
                if (providerConfig != null) {
                    if (StringUtils.hasText(providerConfig.getDefaultModel())) {
                        defaultModelName = providerConfig.getDefaultModel();
                    }
                    if (providerConfig.getTemperature() != null) {
                        defaultTemperature = providerConfig.getTemperature();
                    }

                    // 尝试从模型列表中获取第一个可用的模型
                    if (!StringUtils.hasText(defaultModelName) && providerConfig.getModels() != null && !providerConfig.getModels().isEmpty()) {
                        ModelConfig.ModelConfigItem firstAvailableModel = providerConfig.getModels().stream()
                                .filter(model -> model.getAvailable() != null && model.getAvailable() && "chat".equals(model.getModelType()))
                                .findFirst()
                                .orElse(null);

                        if (firstAvailableModel != null) {
                            defaultModelName = firstAvailableModel.getLlmName();
                            if (firstAvailableModel.getMaxTokens() != null) {
                                defaultMaxTokens = firstAvailableModel.getMaxTokens();
                            }
                        }
                    }
                }
            }

            // 如果仍然没有获取到模型名称，使用回退值
            if (!StringUtils.hasText(defaultModelName)) {
                defaultModelName = "qwen3:8b"; // 回退值
                log.warn("未找到配置的默认模型，使用回退模型: {}", defaultModelName);
            }

            log.info("构建默认LLM配置: provider={}, model={}, temperature={}, maxTokens={}",
                    defaultProvider, defaultModelName, defaultTemperature, defaultMaxTokens);

            Map<String, Object> llmConfig = new HashMap<>();
            llmConfig.put("model_name", defaultModelName + "@" + defaultProvider);
            llmConfig.put("temperature", defaultTemperature);
            llmConfig.put("top_p", 0.3);
            llmConfig.put("presence_penalty", 0.4);
            llmConfig.put("frequency_penalty", 0.7);
            llmConfig.put("max_tokens", defaultMaxTokens);

            return objectMapper.writeValueAsString(llmConfig);
        } catch (Exception e) {
            log.error("构建默认LLM配置失败", e);
            return "{\"model_name\":\"qwen3:8b\",\"temperature\":0.7,\"max_tokens\":512}";
        }
    }

    /**
     * 构建默认提示配置
     */
    private String buildDefaultPromptConfig() {
        try {
            Map<String, Object> promptConfig = new HashMap<>();
            promptConfig.put("similarity_threshold", 0.2);
            promptConfig.put("keywords_similarity_weight", 0.7);
            promptConfig.put("top_n", 8);
            promptConfig.put("opener", "您好！我是您的智能AI助手，有什么可以帮助您的吗？");
            promptConfig.put("show_quote", true);
            promptConfig.put("prompt", "你是一个智能的AI助手。请根据用户的问题提供详细、准确的回答。");
            promptConfig.put("empty_response", "");
            // 添加默认变量配置
            List<Map<String, Object>> variables = new ArrayList<>();
            promptConfig.put("variables", variables);

            return objectMapper.writeValueAsString(promptConfig);
        } catch (Exception e) {
            log.error("构建默认提示配置失败", e);
            return "{\"prompt\":\"你是一个智能的AI助手。请根据用户的问题提供详细、准确的回答。\"}";
        }
    }

    /**
     * 从MCP ToolCallbackProvider中获取函数名称列表
     *
     * @return MCP函数名称列表
     */
    private List<String> getMcpFunctionNames() {
        List<String> functionNames = new ArrayList<>();
        try {
            if (mcpToolCallbackProvider.isPresent()) {
                // 尝试获取所有被@McpService标注的Bean
                try {
                    Class<?> mcpServiceClass = Class.forName("com.etrx.mcp.service.annotation.McpService");
                    Map<String, Object> mcpServiceBeans = applicationContext.getBeansWithAnnotation(
                            mcpServiceClass.asSubclass(java.lang.annotation.Annotation.class)
                    );

                    // 检查每个MCP服务Bean中的@Tool方法
                    for (Map.Entry<String, Object> entry : mcpServiceBeans.entrySet()) {
                        Object service = entry.getValue();

                        // 检查方法上的@Tool注解，获取函数名称
                        Method[] methods = service.getClass().getDeclaredMethods();
                        for (Method method : methods) {
                            if (method.isAnnotationPresent(org.springframework.ai.tool.annotation.Tool.class)) {
                                org.springframework.ai.tool.annotation.Tool toolAnnotation =
                                        method.getAnnotation(org.springframework.ai.tool.annotation.Tool.class);
                                String functionName = toolAnnotation.name();
                                if (!functionName.isEmpty()) {
                                    functionNames.add(functionName);
                                    log.debug("发现MCP函数: {} (方法: {})", functionName, method.getName());
                                }
                            }
                        }
                    }
                } catch (ClassNotFoundException e) {
                    log.warn("MCP注解类不可用，回退到Function Bean检查");

                    // 回退方案：检查Function类型的Bean
                    Map<String, ?> functionBeans = applicationContext.getBeansOfType(java.util.function.Function.class);

                    for (Map.Entry<String, ?> entry : functionBeans.entrySet()) {
                        String beanName = entry.getKey();
                        Object bean = entry.getValue();

                        // 检查Bean类或方法是否有@Tool注解
                        if (hasToolAnnotation(bean.getClass())) {
                            functionNames.add(beanName);
                            log.debug("发现Function工具Bean: {}", beanName);
                        }
                    }
                }

                log.info("从MCP ToolCallbackProvider中发现 {} 个函数: {}", functionNames.size(), functionNames);
            }

        } catch (Exception e) {
            log.warn("获取MCP函数名称失败: {}", e.getMessage(), e);
        }
        return functionNames;
    }
} 
