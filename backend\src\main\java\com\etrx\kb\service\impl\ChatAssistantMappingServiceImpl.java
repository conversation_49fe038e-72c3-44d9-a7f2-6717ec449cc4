package com.etrx.kb.service.impl;

import com.etrx.kb.common.PageResult;
import com.etrx.kb.domain.ChatAssistantMapping;
import com.etrx.kb.dto.ChatAssistantMappingDTO;
import com.etrx.kb.exception.ApiException;
import com.etrx.kb.mapper.ChatAssistantMappingMapper;
import com.etrx.kb.service.ChatAssistantMappingService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 聊天助手映射服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ChatAssistantMappingServiceImpl implements ChatAssistantMappingService {

    private final ChatAssistantMappingMapper chatAssistantMappingMapper;
    private final ObjectMapper objectMapper;

    @Override
    public Long createMapping(Long userId, String ragflowChatAssistantId, String assistantName, String icon,
                              List<String> ragflowKnowledgeBaseIds, List<Long> localKnowledgeBaseIds,
                              Integer assistantType) {
        try {
            // 检查是否已存在映射
            if (existsMapping(userId, ragflowChatAssistantId)) {
                throw new ApiException("该聊天助手已存在映射记录");
            }

            ChatAssistantMapping mapping = new ChatAssistantMapping();
            mapping.setUserId(userId);
            mapping.setRagflowChatAssistantId(ragflowChatAssistantId);
            mapping.setAssistantName(assistantName);
            mapping.setIcon(icon);
            mapping.setAssistantType(assistantType);
            mapping.setStatus(ChatAssistantMapping.Status.ENABLED.getCode());

            // 转换知识库ID列表为JSON
            if (ragflowKnowledgeBaseIds != null && !ragflowKnowledgeBaseIds.isEmpty()) {
                mapping.setRagflowKnowledgeBaseIds(objectMapper.writeValueAsString(ragflowKnowledgeBaseIds));
            }
            if (localKnowledgeBaseIds != null && !localKnowledgeBaseIds.isEmpty()) {
                mapping.setLocalKnowledgeBaseIds(objectMapper.writeValueAsString(localKnowledgeBaseIds));
            }

            chatAssistantMappingMapper.insert(mapping);
            return mapping.getId();

        } catch (JsonProcessingException e) {
            log.error("序列化知识库ID列表失败", e);
            throw new ApiException("创建映射记录失败");
        }
    }

    @Override
    public List<ChatAssistantMappingDTO> getUserChatAssistants(Long userId) {
        List<ChatAssistantMapping> mappings = chatAssistantMappingMapper.selectByUserId(userId);
        return mappings.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public PageResult<ChatAssistantMappingDTO> getUserChatAssistantsPage(Long userId, Integer pageNum, Integer pageSize) {
        // 计算总数
        int total = chatAssistantMappingMapper.countByUserId(userId);
        
        // 计算偏移量
        int offset = (pageNum - 1) * pageSize;
        
        // 查询数据
        List<ChatAssistantMapping> mappings = chatAssistantMappingMapper.selectByUserIdWithPage(userId, offset, pageSize);
        List<ChatAssistantMappingDTO> dtoList = mappings.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());

        // 构建分页结果
        PageResult<ChatAssistantMappingDTO> pageResult = new PageResult<>();
        pageResult.setPageNum(pageNum);
        pageResult.setPageSize(pageSize);
        pageResult.setTotal((long) total);
        pageResult.setList(dtoList);
        
        return pageResult;
    }

    @Override
    public ChatAssistantMappingDTO getMappingByUserAndAssistant(Long userId, String ragflowAssistantId) {
        ChatAssistantMapping mapping = chatAssistantMappingMapper.selectByUserIdAndAssistantId(userId, ragflowAssistantId);
        return mapping != null ? convertToDTO(mapping) : null;
    }

    @Override
    public void disableChatAssistant(Long id, Long userId) {
        ChatAssistantMapping mapping = chatAssistantMappingMapper.selectById(id);
        if (mapping == null || !mapping.getUserId().equals(userId)) {
            throw new ApiException("聊天助手不存在或无权限操作");
        }
        chatAssistantMappingMapper.updateStatus(id, ChatAssistantMapping.Status.DISABLED.getCode());
    }

    @Override
    public void enableChatAssistant(Long id, Long userId) {
        ChatAssistantMapping mapping = chatAssistantMappingMapper.selectById(id);
        if (mapping == null || !mapping.getUserId().equals(userId)) {
            throw new ApiException("聊天助手不存在或无权限操作");
        }
        chatAssistantMappingMapper.updateStatus(id, ChatAssistantMapping.Status.ENABLED.getCode());
    }

    @Override
    public void deleteChatAssistant(Long id, Long userId) {
        ChatAssistantMapping mapping = chatAssistantMappingMapper.selectById(id);
        if (mapping == null || !mapping.getUserId().equals(userId)) {
            throw new ApiException("聊天助手不存在或无权限操作");
        }
        chatAssistantMappingMapper.deleteById(id);
    }

    @Override
    public boolean existsMapping(Long userId, String ragflowAssistantId) {
        ChatAssistantMapping mapping = chatAssistantMappingMapper.selectByUserIdAndAssistantId(userId, ragflowAssistantId);
        return mapping != null;
    }

    @Override
    public void updateAssistantInfo(Long userId, String ragflowChatAssistantId, String assistantName, String icon,
                                   List<String> ragflowKnowledgeBaseIds, List<Long> localKnowledgeBaseIds) {
        try {
            // 检查映射记录是否存在
            ChatAssistantMapping mapping = chatAssistantMappingMapper.selectByUserIdAndAssistantId(userId, ragflowChatAssistantId);
            if (mapping == null) {
                throw new ApiException("聊天助手映射记录不存在");
            }
            
            // 转换知识库ID列表为JSON
            String ragflowKbIdsJson = null;
            String localKbIdsJson = null;
            
            if (ragflowKnowledgeBaseIds != null && !ragflowKnowledgeBaseIds.isEmpty()) {
                ragflowKbIdsJson = objectMapper.writeValueAsString(ragflowKnowledgeBaseIds);
            }
            if (localKnowledgeBaseIds != null && !localKnowledgeBaseIds.isEmpty()) {
                localKbIdsJson = objectMapper.writeValueAsString(localKnowledgeBaseIds);
            }
            
            // 更新助手信息
            int updated = chatAssistantMappingMapper.updateAssistantInfo(userId, ragflowChatAssistantId, assistantName, icon,
                    ragflowKbIdsJson, localKbIdsJson);
            if (updated == 0) {
                throw new ApiException("更新助手信息失败");
            }
            
            log.info("成功更新助手信息: userId={}, ragflowAssistantId={}, assistantName={}, ragflowKbIds={}, localKbIds={}",
                    userId, ragflowChatAssistantId, assistantName, ragflowKnowledgeBaseIds, localKnowledgeBaseIds);
                    
        } catch (JsonProcessingException e) {
            log.error("序列化知识库ID列表失败", e);
            throw new ApiException("更新助手信息失败：知识库ID序列化错误");
        }
    }

    /**
     * 转换为DTO对象
     */
    private ChatAssistantMappingDTO convertToDTO(ChatAssistantMapping mapping) {
        ChatAssistantMappingDTO dto = new ChatAssistantMappingDTO();
        dto.setId(mapping.getId());
        dto.setUserId(mapping.getUserId());
        dto.setRagflowChatAssistantId(mapping.getRagflowChatAssistantId());
        dto.setAssistantName(mapping.getAssistantName());
        dto.setIcon(mapping.getIcon());
        dto.setAssistantType(mapping.getAssistantType());
        dto.setStatus(mapping.getStatus());
        dto.setCreateTime(mapping.getCreateTime());
        dto.setUpdateTime(mapping.getUpdateTime());

        // 设置类型描述
        if (mapping.getAssistantType() != null) {
            for (ChatAssistantMapping.AssistantType type : ChatAssistantMapping.AssistantType.values()) {
                if (type.getCode().equals(mapping.getAssistantType())) {
                    dto.setAssistantTypeDesc(type.getDescription());
                    break;
                }
            }
        }

        // 设置状态描述
        if (mapping.getStatus() != null) {
            for (ChatAssistantMapping.Status status : ChatAssistantMapping.Status.values()) {
                if (status.getCode().equals(mapping.getStatus())) {
                    dto.setStatusDesc(status.getDescription());
                    break;
                }
            }
        }

        // 解析知识库ID列表
        try {
            if (StringUtils.hasText(mapping.getRagflowKnowledgeBaseIds())) {
                List<String> ragflowIds = objectMapper.readValue(
                        mapping.getRagflowKnowledgeBaseIds(),
                        new TypeReference<List<String>>() {}
                );
                dto.setRagflowKnowledgeBaseIds(ragflowIds);
            }

            if (StringUtils.hasText(mapping.getLocalKnowledgeBaseIds())) {
                List<Long> localIds = objectMapper.readValue(
                        mapping.getLocalKnowledgeBaseIds(),
                        new TypeReference<List<Long>>() {}
                );
                dto.setLocalKnowledgeBaseIds(localIds);
            }
        } catch (JsonProcessingException e) {
            log.error("解析知识库ID列表失败", e);
            dto.setRagflowKnowledgeBaseIds(new ArrayList<>());
            dto.setLocalKnowledgeBaseIds(new ArrayList<>());
        }

        return dto;
    }
} 