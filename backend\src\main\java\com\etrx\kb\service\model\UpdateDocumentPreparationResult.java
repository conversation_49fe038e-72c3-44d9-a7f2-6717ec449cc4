package com.etrx.kb.service.model;

import java.util.Map;

public class UpdateDocumentPreparationResult {
    private String datasetId;
    private String documentId;
    private String fileName;
    private Map<String, Object> updateFields;

    public UpdateDocumentPreparationResult(String datasetId, String documentId, String fileName, Map<String, Object> updateFields) {
        this.datasetId = datasetId;
        this.documentId = documentId;
        this.fileName = fileName;
        this.updateFields = updateFields;
    }

    public String getDatasetId() { return datasetId; }
    public String getDocumentId() { return documentId; }
    public String getFileName() { return fileName; }
    public Map<String, Object> getUpdateFields() { return updateFields; }
}