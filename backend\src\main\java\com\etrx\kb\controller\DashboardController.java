package com.etrx.kb.controller;

import com.etrx.kb.common.Result;
import com.etrx.kb.dto.DashboardStatsDTO;
import com.etrx.kb.service.KnowledgeBaseService;
import com.etrx.kb.util.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数据看板控制器
 */
@Tag(name = "首页看板", description = "数据看板相关接口")
@RestController
@RequestMapping("/aikb/dashboard")
@RequiredArgsConstructor
public class DashboardController {

    private final KnowledgeBaseService knowledgeBaseService;
    private final SecurityUtils securityUtils;

    /**
     * 获取数据看板统计信息
     */
    @Operation(
        summary = "获取数据看板统计信息",
        description = "获取当前登录用户的数据看板统计信息，包括知识库总数、文档总数、成员总数和API密钥总数"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "成功获取统计信息"),
        @ApiResponse(responseCode = "401", description = "未登录"),
        @ApiResponse(responseCode = "403", description = "没有权限")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/stats")
    @PreAuthorize("hasRole('USER')")
    public Result<DashboardStatsDTO> getStats() {
        return Result.success(knowledgeBaseService.getDashboardStats(securityUtils.getCurrentUserId()));
    }
} 