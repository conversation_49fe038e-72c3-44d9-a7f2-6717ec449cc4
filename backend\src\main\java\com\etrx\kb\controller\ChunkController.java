package com.etrx.kb.controller;

import com.etrx.kb.common.Result;
import com.etrx.kb.dto.ChunkRequestDTO;
import com.etrx.kb.service.ChunkService;
import com.etrx.kb.mapper.KnowledgeBaseMapper;
import com.etrx.kb.common.Constants.KnowledgeBaseMemberRole;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * Chunk管理控制器
 * 提供Chunk的CRUD操作和检索功能
 */
@Slf4j
@RestController
@RequestMapping("/aikb/chunks")
@RequiredArgsConstructor
@Tag(name = "Chunk管理", description = "Chunk的增删改查和检索接口")
public class ChunkController {

    private final ChunkService chunkService;
    private final ObjectMapper objectMapper;

    /**
     * 添加Chunk
     */
    @Operation(summary = "添加Chunk", description = "向指定数据集的文档中添加新的Chunk")
    @PostMapping("/add")
    @PreAuthorize("@knowledgeBasePermission.checkDataset(#request.datasetId, T(com.etrx.kb.common.Constants$KnowledgeBaseMemberRole).ADMIN)")
    public Result<Object> addChunk(@Valid @RequestBody ChunkRequestDTO.AddChunkRequest request) {
        try {
            String response = chunkService.addChunk(request);
            JsonNode jsonNode = objectMapper.readTree(response);
            return Result.success("Chunk添加成功", jsonNode);
        } catch (Exception e) {
            log.error("添加Chunk失败", e);
            return Result.failed("添加Chunk失败: " + e.getMessage());
        }
    }

    /**
     * 查询Chunk列表
     */
    @Operation(summary = "查询Chunk列表", description = "获取指定文档的Chunk列表")
    @GetMapping("/list")
    @PreAuthorize("@knowledgeBasePermission.checkDataset(#datasetId, T(com.etrx.kb.common.Constants$KnowledgeBaseMemberRole).MEMBER) or @securityUtils.isAdmin()")
    public Result<Object> listChunks(
            @Parameter(description = "数据集ID", required = true) @RequestParam String datasetId,
            @Parameter(description = "文档ID", required = true) @RequestParam String documentId,
            @Parameter(description = "关键词过滤") @RequestParam(required = false) String keywords,
            @Parameter(description = "页码") @RequestParam(required = false, defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(required = false, defaultValue = "1024") Integer pageSize,
            @Parameter(description = "Chunk ID") @RequestParam(required = false) String id) {
        try {
            ChunkRequestDTO.ListChunkRequest request = new ChunkRequestDTO.ListChunkRequest();
            request.setDatasetId(datasetId);
            request.setDocumentId(documentId);
            request.setKeywords(keywords);
            request.setPage(page);
            request.setPageSize(pageSize);
            request.setId(id);

            String response = chunkService.listChunks(request);
            JsonNode jsonNode = objectMapper.readTree(response);
            return Result.success("查询Chunk列表成功", jsonNode);
        } catch (Exception e) {
            log.error("查询Chunk列表失败", e);
            return Result.failed("查询Chunk列表失败: " + e.getMessage());
        }
    }

    /**
     * 删除Chunk
     */
    @Operation(summary = "删除Chunk", description = "删除指定的Chunk")
    @DeleteMapping("/delete")
    @PreAuthorize("@knowledgeBasePermission.checkDataset(#request.datasetId, T(com.etrx.kb.common.Constants$KnowledgeBaseMemberRole).ADMIN)")
    public Result<Object> deleteChunks(@Valid @RequestBody ChunkRequestDTO.DeleteChunkRequest request) {
        try {
            String response = chunkService.deleteChunks(request);
            JsonNode jsonNode = objectMapper.readTree(response);
            return Result.success("Chunk删除成功", jsonNode);
        } catch (Exception e) {
            log.error("删除Chunk失败", e);
            return Result.failed("删除Chunk失败: " + e.getMessage());
        }
    }

    /**
     * 更新Chunk
     */
    @Operation(summary = "更新Chunk", description = "更新指定Chunk的内容或配置")
    @PutMapping("/update")
    @PreAuthorize("@knowledgeBasePermission.checkDataset(#request.datasetId, T(com.etrx.kb.common.Constants$KnowledgeBaseMemberRole).ADMIN)")
    public Result<Object> updateChunk(@Valid @RequestBody ChunkRequestDTO.UpdateChunkRequest request) {
        try {
            String response = chunkService.updateChunk(request);
            JsonNode jsonNode = objectMapper.readTree(response);
            return Result.success("Chunk更新成功", jsonNode);
        } catch (Exception e) {
            log.error("更新Chunk失败", e);
            return Result.failed("更新Chunk失败: " + e.getMessage());
        }
    }

    /**
     * 检索Chunk
     */
    @Operation(summary = "检索Chunk", description = "根据问题检索相关的Chunk")
    @PostMapping("/retrieve")
    @PreAuthorize("@knowledgeBasePermission.checkDatasets(#request.datasetIds, T(com.etrx.kb.common.Constants$KnowledgeBaseMemberRole).MEMBER)")
    public Result<Object> retrieveChunks(@Valid @RequestBody ChunkRequestDTO.RetrieveChunkRequest request) {
        try {
            String response = chunkService.retrieveChunks(request);
            JsonNode jsonNode = objectMapper.readTree(response);
            return Result.success("Chunk检索成功", jsonNode);
        } catch (Exception e) {
            log.error("检索Chunk失败", e);
            return Result.failed("检索Chunk失败: " + e.getMessage());
        }
    }

    /**
     * 快速添加Chunk (简化版接口)
     */
    @Operation(summary = "快速添加Chunk", description = "快速添加Chunk的简化接口")
    @PostMapping("/{datasetId}/documents/{documentId}/chunks")
    @PreAuthorize("@knowledgeBasePermission.checkDataset(#datasetId, T(com.etrx.kb.common.Constants$KnowledgeBaseMemberRole).ADMIN)")
    public Result<Object> quickAddChunk(
            @Parameter(description = "数据集ID") @PathVariable String datasetId,
            @Parameter(description = "文档ID") @PathVariable String documentId,
            @RequestBody Map<String, Object> requestBody) {
        try {
            ChunkRequestDTO.AddChunkRequest request = new ChunkRequestDTO.AddChunkRequest();
            request.setDatasetId(datasetId);
            request.setDocumentId(documentId);
            request.setContent((String) requestBody.get("content"));
            
            // 处理可选参数
            if (requestBody.containsKey("important_keywords")) {
                request.setImportantKeywords((java.util.List<String>) requestBody.get("important_keywords"));
            }
            if (requestBody.containsKey("questions")) {
                request.setQuestions((java.util.List<String>) requestBody.get("questions"));
            }

            String response = chunkService.addChunk(request);
            JsonNode jsonNode = objectMapper.readTree(response);
            return Result.success("Chunk添加成功", jsonNode);
        } catch (Exception e) {
            log.error("快速添加Chunk失败", e);
            return Result.failed("快速添加Chunk失败: " + e.getMessage());
        }
    }

    /**
     * 批量更新Chunk
     */
    @Operation(summary = "批量更新Chunk", description = "批量更新多个Chunk的状态或内容")
    @PutMapping("/batch-update")
    @PreAuthorize("@knowledgeBasePermission.checkDataset(#request.datasetId, T(com.etrx.kb.common.Constants$KnowledgeBaseMemberRole).ADMIN)")
    public Result<Object> batchUpdateChunks(@Valid @RequestBody ChunkRequestDTO.BatchUpdateChunkRequest request) {
        try {
            String response = chunkService.batchUpdateChunks(request);
            JsonNode jsonNode = objectMapper.readTree(response);
            return Result.success("批量更新Chunk成功", jsonNode);
        } catch (Exception e) {
            log.error("批量更新Chunk失败", e);
            return Result.failed("批量更新Chunk失败: " + e.getMessage());
        }
    }

    /**
     * 快速更新Chunk (简化版接口)
     */
    @Operation(summary = "快速更新Chunk", description = "快速更新Chunk的简化接口")
    @PutMapping("/{datasetId}/documents/{documentId}/chunks/{chunkId}")
    @PreAuthorize("@knowledgeBasePermission.checkDataset(#datasetId, T(com.etrx.kb.common.Constants$KnowledgeBaseMemberRole).ADMIN)")
    public Result<Object> quickUpdateChunk(
            @Parameter(description = "数据集ID") @PathVariable String datasetId,
            @Parameter(description = "文档ID") @PathVariable String documentId,
            @Parameter(description = "Chunk ID") @PathVariable String chunkId,
            @RequestBody Map<String, Object> updateFields) {
        try {
            ChunkRequestDTO.UpdateChunkRequest request = new ChunkRequestDTO.UpdateChunkRequest();
            request.setDatasetId(datasetId);
            request.setDocumentId(documentId);
            request.setChunkId(chunkId);
            request.setUpdateFields(updateFields);

            String response = chunkService.updateChunk(request);
            JsonNode jsonNode = objectMapper.readTree(response);
            return Result.success("Chunk更新成功", jsonNode);
        } catch (Exception e) {
            log.error("快速更新Chunk失败", e);
            return Result.failed("快速更新Chunk失败: " + e.getMessage());
        }
    }
} 