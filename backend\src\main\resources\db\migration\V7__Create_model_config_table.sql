-- 创建模型配置表
CREATE TABLE IF NOT EXISTS model_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    provider VARCHAR(100) NOT NULL COMMENT '提供商名称',
    llm_name VARCHAR(200) NOT NULL COMMENT '模型名称',
    model_type VARCHAR(50) NOT NULL COMMENT '模型类型：chat, embedding, tts, speech2text',
    available BOOLEAN DEFAULT TRUE COMMENT '是否可用',
    max_tokens INT COMMENT '最大令牌数',
    is_tools BOOLEAN DEFAULT FALSE COMMENT '是否支持工具',
    status VARCHAR(10) DEFAULT '1' COMMENT '状态',
    tags TEXT COMMENT '标签，用逗号分隔',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除标识：0-未删除，1-已删除',
    INDEX idx_provider (provider),
    INDEX idx_model_type (model_type),
    INDEX idx_available (available),
    UNIQUE KEY uk_provider_model (provider, llm_name, deleted)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模型配置表'; 