package com.etrx.kb.service;

import com.etrx.kb.common.PageResult;
import com.etrx.kb.dto.LoginResponseDTO;
import com.etrx.kb.dto.UserInfoDTO;
import com.etrx.kb.dto.UserLoginDTO;
import com.etrx.kb.dto.UserRegisterDTO;
import com.etrx.kb.dto.ThirdPartyLoginDTO;
import com.etrx.kb.domain.User;

/**
 * 用户服务接口
 */
public interface UserService {

    /**
     * 用户注册
     *
     * @param registerDTO 注册信息
     * @return 用户信息
     */
    UserInfoDTO register(UserRegisterDTO registerDTO);

    /**
     * 用户登录
     *
     * @param loginDTO 登录信息
     * @return 登录响应信息
     */
    LoginResponseDTO login(UserLoginDTO loginDTO);

    /**
     * 获取当前用户信息
     *
     * @return 当前用户信息
     */
    UserInfoDTO getCurrentUserInfo();

    /**
     * 获取用户信息
     *
     * @param id 用户ID
     * @return 用户信息
     */
    UserInfoDTO getUserInfo(Long id);

    /**
     * 更新用户信息
     *
     * @param id      用户ID
     * @param userDTO 用户信息
     * @return 更新后的用户信息
     */
    UserInfoDTO updateUserInfo(Long id, UserInfoDTO userDTO);

    /**
     * 修改密码
     *
     * @param id          用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     */
    void changePassword(Long id, String oldPassword, String newPassword);

    /**
     * 分页查询用户列表
     *
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @param keyword  搜索关键字
     * @return 用户分页列表
     */
    PageResult<UserInfoDTO> listUsers(Integer pageNum, Integer pageSize, String keyword);

    /**
     * 更新用户状态
     *
     * @param id     用户ID
     * @param status 状态(0:禁用,1:启用)
     */
    void updateStatus(Long id, Integer status);

    /**
     * 更新用户角色
     *
     * @param id   用户ID
     * @param role 角色(1:普通用户,2:管理员,3:超级管理员)
     */
    void updateRole(Long id, Integer role);

    /**
     * 删除用户
     *
     * @param id 用户ID
     */
    void deleteUser(Long id);

    /**
     * 更新当前用户信息
     *
     * @param userDTO 用户信息
     * @return 更新后的用户信息
     */
    UserInfoDTO updateCurrentUserInfo(UserInfoDTO userDTO);

    /**
     * 第三方登录
     *
     * @param apiKey API密钥
     * @param userInfo 第三方用户信息
     * @return 登录响应信息
     */
    LoginResponseDTO thirdPartyLogin(String apiKey, ThirdPartyLoginDTO.UserInfo userInfo);

    /**
     * 根据工号获取用户
     *
     * @param employeeNo 工号
     * @return 用户信息
     */
    User getUserByEmployeeNo(String employeeNo);
}