package com.etrx.kb.mapper;

import com.etrx.kb.domain.ChatSessionMapping;
import org.apache.ibatis.annotations.*;

/**
 * 聊天会话映射Mapper
 */
@Mapper
public interface ChatSessionMappingMapper {

    /**
     * 插入会话映射
     */
    @Insert("INSERT INTO chat_session_mapping (user_id, session_id, chat_assistant_id, session_name, message, reference, status) " +
            "VALUES (#{userId}, #{sessionId}, #{chatAssistantId}, #{sessionName}, #{message}, #{reference}, #{status})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(ChatSessionMapping mapping);

    /**
     * 根据会话ID查询映射
     */
    @Select("SELECT * FROM chat_session_mapping WHERE session_id = #{sessionId} AND status = 1")
    ChatSessionMapping selectBySessionId(String sessionId);

    /**
     * 根据用户ID和会话ID查询映射
     */
    @Select("SELECT * FROM chat_session_mapping WHERE user_id = #{userId} AND session_id = #{sessionId} AND status = 1")
    ChatSessionMapping selectByUserIdAndSessionId(@Param("userId") Long userId, @Param("sessionId") String sessionId);

    /**
     * 根据聊天助手ID查询会话列表
     */
    @Select("SELECT * FROM chat_session_mapping WHERE chat_assistant_id = #{chatAssistantId} AND status = 1 ORDER BY create_time DESC")
    java.util.List<ChatSessionMapping> selectByChatAssistantId(String chatAssistantId);

    /**
     * 根据用户ID查询会话列表
     */
    @Select("SELECT * FROM chat_session_mapping WHERE user_id = #{userId} AND status = 1 ORDER BY create_time DESC")
    java.util.List<ChatSessionMapping> selectByUserId(Long userId);

    /**
     * 更新会话状态
     */
    @Update("UPDATE chat_session_mapping SET status = #{status} WHERE session_id = #{sessionId}")
    int updateStatusBySessionId(@Param("sessionId") String sessionId, @Param("status") Integer status);

    /**
     * 更新会话名称
     */
    @Update("UPDATE chat_session_mapping SET session_name = #{sessionName} WHERE session_id = #{sessionId}")
    int updateSessionNameBySessionId(@Param("sessionId") String sessionId, @Param("sessionName") String sessionName);

    /**
     * 更新会话消息和引用
     */
    @Update("UPDATE chat_session_mapping SET message = #{message}, reference = #{reference} WHERE session_id = #{sessionId}")
    int updateSessionContent(@Param("sessionId") String sessionId, @Param("message") String message, @Param("reference") String reference);

    /**
     * 删除会话映射
     */
    @Delete("DELETE FROM chat_session_mapping WHERE session_id = #{sessionId}")
    int deleteBySessionId(String sessionId);
} 