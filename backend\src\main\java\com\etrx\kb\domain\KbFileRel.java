package com.etrx.kb.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("tb_kb_file_rel")
public class KbFileRel {
    @TableId(type = IdType.AUTO)
    private Long id;
    private Long kbId;
    private Long fileNodeId;
    private Integer relType;  // 1:直接上传,2:手动关联
    private String documentId;     // Ragflow文档ID
    private Integer documentStatus;  // 文档状态：0-未处理，1-处理中，2-处理完成，3-处理失败，4-已取消
    private String documentInfo;    // 文档详细信息（JSON格式）
    private Long creatorId;
    @TableLogic
    private Integer deleted;
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    @TableField(exist = false)
    private KnowledgeBase knowledgeBase;  // 关联的知识库信息
    @TableField(exist = false)
    private FileNode fileNode;  // 关联的文件节点信息
} 