package com.etrx.kb.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

@Configuration
@EnableScheduling
public class SchedulingConfig {
    // 配置类，启用Spring的定时任务功能
    
    @Bean(name = "threadPoolTaskScheduler")
    public ThreadPoolTaskScheduler taskScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(10); // 设置线程池大小
        scheduler.setThreadNamePrefix("scheduled-task-"); // 设置线程名前缀
        scheduler.setAwaitTerminationSeconds(60); // 设置等待终止的时间
        scheduler.setWaitForTasksToCompleteOnShutdown(true); // 等待所有任务完成后再关闭线程池
        scheduler.setErrorHandler(throwable -> {
            // 统一的异常处理
            throw new RuntimeException("定时任务执行异常", throwable);
        });
        return scheduler;
    }
} 