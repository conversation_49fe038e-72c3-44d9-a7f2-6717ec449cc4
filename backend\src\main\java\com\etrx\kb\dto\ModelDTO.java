package com.etrx.kb.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

/**
 * 模型信息DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ModelDTO {

    /**
     * 是否可用
     */
    private Boolean available;

    /**
     * 创建日期（字符串格式）
     */
    private String createDate;

    /**
     * 创建时间（时间戳）
     */
    private Long createTime;

    /**
     * 提供商ID
     */
    private String fid;

    /**
     * 是否支持工具
     */
    private Boolean isTools;

    /**
     * 模型名称
     */
    private String llmName;

    /**
     * 最大令牌数
     */
    private Integer maxTokens;

    /**
     * 模型类型：chat, embedding, tts, speech2text
     */
    private String modelType;

    /**
     * 状态
     */
    private String status;

    /**
     * 标签
     */
    private String tags;

    /**
     * 更新日期（字符串格式）
     */
    private String updateDate;

    /**
     * 更新时间（时间戳）
     */
    private Long updateTime;

    /**
     * 简化构造函数（用于基本模型）
     */
    public ModelDTO(Boolean available, String fid, String llmName, String modelType) {
        this.available = available;
        this.fid = fid;
        this.llmName = llmName;
        this.modelType = modelType;
    }

    /**
     * 完整构造函数（用于详细模型）
     */
    public ModelDTO(Boolean available, String fid, String llmName, String modelType, 
                   Integer maxTokens, Boolean isTools, String status, String tags) {
        this.available = available;
        this.fid = fid;
        this.llmName = llmName;
        this.modelType = modelType;
        this.maxTokens = maxTokens;
        this.isTools = isTools;
        this.status = status;
        this.tags = tags;
        
        // 设置默认时间
        long currentTime = System.currentTimeMillis();
        this.createTime = currentTime;
        this.updateTime = currentTime;
        
        // 生成GMT格式的日期字符串，格式如：Mon, 26 May 2025 13:00:56 GMT
        SimpleDateFormat sdf = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss 'GMT'", Locale.ENGLISH);
        sdf.setTimeZone(TimeZone.getTimeZone("GMT"));
        Date date = new Date(currentTime);
        this.createDate = sdf.format(date);
        this.updateDate = sdf.format(date);
    }
} 