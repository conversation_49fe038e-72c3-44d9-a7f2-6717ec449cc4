package com.etrx.kb.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 聊天助手映射DTO
 */
@Data
public class ChatAssistantMappingDTO {

    /**
     * ID
     */
    private Long id;

    /**
     * 平台用户ID
     */
    private Long userId;

    /**
     * RAGflow聊天助手ID
     */
    private String ragflowChatAssistantId;

    /**
     * 聊天助手名称
     */
    private String assistantName;

    /**
     * 助手图标（Base64编码）
     */
    private String icon;

    /**
     * RAGflow知识库ID列表
     */
    private List<String> ragflowKnowledgeBaseIds;

    /**
     * 本地知识库ID列表
     */
    private List<Long> localKnowledgeBaseIds;

    /**
     * 助手类型: 1-RAGflow知识库, 2-本地知识库
     */
    private Integer assistantType;

    /**
     * 助手类型描述
     */
    private String assistantTypeDesc;

    /**
     * 状态: 0-禁用, 1-启用
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 