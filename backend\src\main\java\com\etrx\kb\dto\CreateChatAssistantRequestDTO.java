package com.etrx.kb.dto;

import lombok.Data;

import java.util.*;

import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * 聊天助理新增/修改请求DTO（统一格式）
 * 支持RAGflow前端格式和原生格式
 * 根据dialogId是否存在来判断是新增还是修改操作
 */
@Data
public class CreateChatAssistantRequestDTO {

    /**
     * 对话ID（RAGflow助理ID）
     * 如果存在且在系统中找到，则为修改操作
     * 如果不存在或为空，则为新增操作
     */
    private String dialogId;

    /**
     * 助理名称
     */
    private String name;

    /**
     * 描述
     */
    private String description;

    /**
     * 图标（Base64编码）
     */
    private String icon;

    /**
     * 语言
     */
    private String language;

    /**
     * 提示配置
     */
    private PromptConfig promptConfig;

    /**
     * 数据集ID列表（RAGflow前端格式）
     * 与kbIds字段二选一使用，优先使用datasetIds
     */
    private List<String> datasetIds;

    /**
     * LLM模型ID
     */
    private String llmId;

    /**
     * LLM设置
     */
    private LlmSetting llmSetting;

    /**
     * 相似度阈值
     */
    private Double similarityThreshold;

    /**
     * 向量相似度权重
     */
    private Double vectorSimilarityWeight;

    /**
     * 返回文档数量
     */
    private Integer topN;

    /**
     * 重排序ID
     */
    private String rerankId;

    /**
     * 提示配置
     */
    @Data
    public static class PromptConfig {
        /**
         * 空回复
         */
        private String emptyResponse;

        /**
         * 开场白
         */
        private String prologue;

        /**
         * 是否引用
         */
        private Boolean quote;

        /**
         * 关键词
         */
        private Boolean keyword;

        /**
         * 文字转语音
         */
        private Boolean tts;

        /**
         * 系统提示
         */
        private String system;

        /**
         * 多轮对话优化
         */
        private Boolean refineMultiturn;

        /**
         * 使用知识图谱
         */
        private Boolean useKg;

        /**
         * 推理
         */
        private Boolean reasoning;

        /**
         * 参数列表/对象
         * 支持两种格式：
         * 1. 数组格式：[{"key": "knowledge", "optional": false}]
         * 2. 对象格式：{}
         */
        private Object parameters;

        /**
         * 获取参数列表
         * 处理前端可能发送对象或数组的情况
         */
        @JsonIgnore
        public List<Parameter> getParametersList() {
            if (parameters == null) {
                return new ArrayList<>();
            }
            
            // 如果是List，直接返回
            if (parameters instanceof List) {
                try {
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> paramList = (List<Map<String, Object>>) parameters;
                    List<Parameter> result = new ArrayList<>();
                    for (Map<String, Object> param : paramList) {
                        Parameter p = new Parameter();
                        p.setKey((String) param.get("key"));
                        p.setOptional((Boolean) param.get("optional"));
                        result.add(p);
                    }
                    return result;
                } catch (Exception e) {
                    return new ArrayList<>();
                }
            }
            
            // 如果是Map（对象），返回空列表
            if (parameters instanceof Map) {
                return new ArrayList<>();
            }
            
            return new ArrayList<>();
        }

        /**
         * 设置参数列表
         */
        @JsonIgnore
        public void setParametersList(List<Parameter> parametersList) {
            this.parameters = parametersList;
        }
    }

    /**
     * 参数
     */
    @Data
    public static class Parameter {
        /**
         * 参数键
         */
        private String key;

        /**
         * 是否可选
         */
        private Boolean optional;
    }

    /**
     * LLM设置
     */
    @Data
    public static class LlmSetting {
        /**
         * 温度
         */
        private Double temperature;

        /**
         * top_p
         */
        private Double topP;

        /**
         * 存在惩罚
         */
        private Double presencePenalty;

        /**
         * 频率惩罚
         */
        private Double frequencyPenalty;

        /**
         * 最大令牌数
         */
        private Integer maxTokens;
    }

    /**
     * 获取知识库ID列表（统一方法）
     * 优先使用datasetIds，如果为空则使用kbIds
     */
    public List<String> getKnowledgeBaseIds() {
        if (datasetIds != null && !datasetIds.isEmpty()) {
            return datasetIds;
        }
        return Collections.emptyList();
    }

    /**
     * 检查是否为修改操作
     */
    public boolean isUpdateOperation() {
        return dialogId != null && !dialogId.trim().isEmpty();
    }
} 