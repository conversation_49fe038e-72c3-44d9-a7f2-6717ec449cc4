package com.etrx.kb.security;

import com.etrx.kb.common.Constants;
import com.etrx.kb.domain.ApiKey;
import com.etrx.kb.domain.User;
import com.etrx.kb.exception.ApiException;
import com.etrx.kb.mapper.ApiKeyMapper;
import com.etrx.kb.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * API密钥权限检查类
 */
@Component
@RequiredArgsConstructor
public class ApiKeyPermission {

    private final ApiKeyMapper apiKeyMapper;
    private final SecurityUtils securityUtils;

    /**
     * 检查用户对API密钥的权限
     *
     * @param apiKeyId API密钥ID
     * @param requiredPermission 需要的权限
     * @return 是否有权限
     */
    public boolean check(Long apiKeyId, Integer requiredPermission) {
        User user = securityUtils.getCurrentUser();
        if (user == null) {
            throw new ApiException("登录信息已过期，请重新登录");
        }

        // 超级管理员直接允许所有操作
        if (user.getRole().equals(Constants.UserRole.SUPER_ADMIN)) {
            return true;
        }

        // 如果角色权限小于需要的权限，则抛出异常
        if (user.getRole() < requiredPermission) {
            throw new ApiException("没有权限操作该API密钥");
        }

        // 如果API密钥ID不为空，则检查API密钥是否存在及权限范围
        if (apiKeyId != null) {
            // 检查API密钥是否存在
            ApiKey apiKey = apiKeyMapper.selectById(apiKeyId);
            if (apiKey == null) {
                throw new ApiException("API密钥不存在");
            }

            // 有权限且不是超级管理员，检查用户是否是创建者
            if (!apiKey.getCreatorId().equals(user.getId())) {
                throw new ApiException("没有权限操作该API密钥");
            }
        }

        return true;
    }
}