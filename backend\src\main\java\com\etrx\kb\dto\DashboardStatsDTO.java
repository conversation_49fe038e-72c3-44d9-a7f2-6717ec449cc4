package com.etrx.kb.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 数据看板统计信息DTO
 */
@Data
@Schema(description = "数据看板统计信息")
public class DashboardStatsDTO {
    /**
     * 知识库总数
     */
    @Schema(description = "知识库总数", example = "10")
    private Long knowledgeBaseCount;

    /**
     * 文档总数
     */
    @Schema(description = "文档总数", example = "100")
    private Long documentCount;

    /**
     * 成员总数
     */
    @Schema(description = "成员总数", example = "50")
    private Long memberCount;

    /**
     * API密钥总数
     */
    @Schema(description = "API密钥总数", example = "5")
    private Long apiKeyCount;

    /**
     * 本周新增知识库数量
     */
    @Schema(description = "本周新增知识库数量", example = "2")
    private Long weeklyNewKnowledgeBaseCount;

    /**
     * 本周删除知识库数量
     */
    @Schema(description = "本周删除知识库数量", example = "1")
    private Long weeklyDeletedKnowledgeBaseCount;

    /**
     * 本周新增文档数量
     */
    @Schema(description = "本周新增文档数量", example = "5")
    private Long weeklyNewDocumentCount;

    /**
     * 本周删除文档数量
     */
    @Schema(description = "本周删除文档数量", example = "2")
    private Long weeklyDeletedDocumentCount;

    /**
     * 本周新增成员数量
     */
    @Schema(description = "本周新增成员数量", example = "3")
    private Long weeklyNewMemberCount;

    /**
     * 本周删除成员数量
     */
    @Schema(description = "本周删除成员数量", example = "1")
    private Long weeklyDeletedMemberCount;
} 