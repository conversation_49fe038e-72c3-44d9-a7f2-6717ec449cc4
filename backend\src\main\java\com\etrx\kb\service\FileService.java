package com.etrx.kb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.etrx.kb.domain.FileNode;
import com.etrx.kb.domain.KnowledgeBase;
import com.etrx.kb.service.model.*;
import com.etrx.kb.vo.DocumentUpdateVO;
import com.etrx.kb.vo.FileListVO;
import com.etrx.kb.vo.RagflowUploadVO;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import com.etrx.kb.dto.BatchCreateFolderRequest;

public interface FileService extends IService<FileNode> {
    // 知识库文件操作
    FileNode uploadKbFile(Long kbId, Long folderId, MultipartFile file, Long userId);
    void deleteKbFile(Long kbId, Long fileId, Long userId);
    void bindKbFile(List<Long> kbIds, Long fileId, Long userId);
    void unbindKbFile(List<Long> kbIds, Long fileId, Long userId);
    
    // 文件夹操作
    FileNode createFolder(String name, Long parentId, Long userId);
    void deleteFolder(Long folderId, Long userId);
    Map<String, Long> createFoldersInBatch(List<BatchCreateFolderRequest.FolderInfo> folders, Long parentId, Long userId);
    FileListVO listFolder(Long folderId, Long kbId, Integer pageNum, Integer pageSize, String keyword);
    List<FileNode> searchFiles(String keyword);
    
    // 独立文件操作
    FileNode uploadFile(Long parentId, KnowledgeBase kb, MultipartFile file, Long userId);
    void deleteFile(Long fileId, Long userId);
    void downloadFile(Long fileId, HttpServletResponse response, Long userId);
    
    // 通用操作
    String generateStoragePath(String originalFilename, Long userId, KnowledgeBase kb);
    String buildFullPath(Long parentId, String name);

    // 知识库根目录操作
    FileNode getOrCreateKbRootFolder(Long kbId, Long userId);

    // 文件解析操作
    void parseFiles(Long kbId, List<Long> fileIds, Long userId);
    void stopParsingFiles(Long kbId, List<Long> fileIds, Long userId);

    /**
     * 更新文档元数据
     *
     * @param kbId 知识库ID
     * @param fileId 文件ID
     * @param updateVO 更新参数
     * @param userId 用户ID
     */
    void updateDocument(Long kbId, Long fileId, RagflowUploadVO.UploadFileVO updateVO, Long userId);

    /**
     * 批量更新文档状态
     *
     * @param kbId 知识库ID
     * @param fileIds 文件ID列表
     * @param status 要更新的状态
     * @param userId 用户ID
     */
    void batchUpdateDocumentStatus(Long kbId, List<Long> fileIds, String status, Long userId);

    /**
     * 移动文件或文件夹到新的位置
     * @param nodeId 要移动的节点ID
     * @param targetFolderId 目标文件夹ID，如果为null则移动到根目录
     * @param userId 当前用户ID
     */
    void moveNode(Long nodeId, Long targetFolderId, Long userId);

    /**
     * 重命名文件或文件夹
     * @param nodeId 要重命名的节点ID
     * @param newName 新的名称
     * @param userId 当前用户ID
     */
    void renameNode(Long nodeId, String newName, Long userId);

    void processDocsStatus(Long kbId, List<FileNode> processingDocs, KnowledgeBase kb);

    /**
     * 检查知识库文件夹是否已存在
     * @param kbName 知识库名称
     * @return true如果文件夹已存在，false如果不存在
     */
    boolean checkKbFolderExists(String kbName);
    
    /**
     * 获取知识库关联的所有文件
     * @param kbId 知识库ID
     * @return 文件关联信息列表
     */
    List<com.etrx.kb.domain.KbFileRel> getKbFileRelations(Long kbId);
    
    /**
     * 删除知识库目录
     * @param kbId 知识库ID
     * @param userId 用户ID
     */
    void deleteKbFolder(Long kbId, Long userId);
    
    /**
     * 根据知识库ID查询所有关联文档信息
     * @param kbId 知识库ID
     * @param userId 当前用户ID
     * @return 文档信息列表（包含文档ID、文档名称、文档路径）
     */
    List<com.etrx.kb.vo.DocumentInfoVO> getAllDocumentsByKbId(Long kbId, Long userId);

    /**
     * 批量删除知识库文件
     * @param kbId 知识库ID
     * @param fileIds 文件ID列表
     * @param userId 当前用户ID
     */
    void batchDeleteKbFiles(Long kbId, List<Long> fileIds, Long userId);

    /**
     * 获取文档图片
     * @param imageId 图片ID
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    void getDocumentImage(String imageId, HttpServletResponse response) throws IOException;


    // 新增：分离ragflow操作的方法

    /**
     * 验证解析文件的前置条件并准备数据
     * @param kbId 知识库ID
     * @param fileIds 文件ID列表
     * @param userId 用户ID
     * @return 返回需要提交给ragflow的数据信息
     */
    ParseFilePreparationResult prepareParseFiles(Long kbId, List<Long> fileIds, Long userId);

    /**
     * 完成解析文件的后续处理
     * @param kbId 知识库ID
     * @param fileIds 文件ID列表
     * @param userId 用户ID
     * @param success 是否成功
     */
    void completeParseFiles(Long kbId, List<Long> fileIds, Long userId, boolean success);

    /**
     * 验证停止解析文件的前置条件并准备数据
     */
    StopParsingPreparationResult prepareStopParsingFiles(Long kbId, List<Long> fileIds, Long userId);

    /**
     * 完成停止解析文件的后续处理
     */
    void completeStopParsingFiles(Long kbId, List<Long> fileIds, Long userId, boolean success);

    /**
     * 验证更新文档的前置条件并准备数据
     */
    UpdateDocumentPreparationResult prepareUpdateDocument(Long kbId, Long fileId, RagflowUploadVO.UploadFileVO updateVO, Long userId);

    /**
     * 完成更新文档的后续处理
     */
    void completeUpdateDocument(Long kbId, Long fileId, RagflowUploadVO.UploadFileVO updateVO, Long userId, boolean success);

    /**
     * 验证批量更新文档状态的前置条件并准备数据
     */
    BatchUpdateStatusPreparationResult prepareBatchUpdateDocumentStatus(Long kbId, List<Long> fileIds, String status, Long userId);

    /**
     * 完成批量更新文档状态的后续处理
     */
    void completeBatchUpdateDocumentStatus(Long kbId, List<Long> fileIds, String status, Long userId, boolean success);


}