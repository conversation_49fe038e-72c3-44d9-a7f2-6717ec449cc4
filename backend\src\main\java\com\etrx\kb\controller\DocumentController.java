package com.etrx.kb.controller;

import com.etrx.kb.service.FileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

/**
 * 文档管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/aikb/document")
@RequiredArgsConstructor
@Tag(name = "文档管理", description = "文档相关的管理接口")
public class DocumentController {

    private final FileService fileService;

    /**
     * 获取文档图片
     */
    @Operation(summary = "获取文档图片", description = "代理访问ragflow的文档图片")
    @GetMapping("/image/{imageId}")
    public void getDocumentImage(
            @Parameter(description = "图片ID") @PathVariable String imageId,
            HttpServletResponse response) throws IOException {
        fileService.getDocumentImage(imageId, response);
    }
} 