// @ts-nocheck
// This file is generated by <PERSON><PERSON> automatically
// DO NOT CHANGE IT MANUALLY!
import React from 'react';

export async function getRoutes() {
  const routes = {"1":{"path":"/","layout":false,"id":"1"},"2":{"path":"/test","layout":false,"id":"2"},"3":{"path":"/pie-chart-test","layout":false,"id":"3"},"@@/global-layout":{"id":"@@/global-layout","path":"/","isLayout":true}} as const;
  return {
    routes,
    routeComponents: {
'1': React.lazy(() => import(/* webpackChunkName: "p__chat__index" */'@/pages/chat/index.tsx')),
'2': React.lazy(() => import(/* webpackChunkName: "components__chat-widget__index" */'@/components/chat-widget/index.tsx')),
'3': React.lazy(() => import(/* webpackChunkName: "p__pie-chart-test__index" */'@/pages/pie-chart-test/index.tsx')),
'@@/global-layout': React.lazy(() => import(/* webpackChunkName: "layouts__index" */'D:/Work/AI Knowledge/code/etrx-ai-kb/chat/src/layouts/index.tsx')),
},
  };
}
