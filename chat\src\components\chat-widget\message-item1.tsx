import React from 'react';
import { Avatar, Flex, Typography } from 'antd';
import classNames from 'classnames';
import styles from './message-item.module.less';

const { Text } = Typography;

interface IProps {
  item: {
    role: 'user' | 'assistant';
    content: string;
  };
  loading?: boolean;
  nickname?: string;
  avatar?: string;
  avatarDialog?: string;
  index: number;
}

const MessageItem1: React.FC<IProps> = ({
  item,
  loading = false,
  avatar,
  avatarDialog,
  index,
}) => {
  const isAssistant = item.role === 'assistant';
  const isUser = item.role === 'user';

  return (
    <div
      className={classNames(styles.messageItem, {
        [styles.messageItemLeft]: isAssistant,
        [styles.messageItemRight]: isUser,
      })}
    >
      <section
        className={classNames(styles.messageItemSection, {
          [styles.messageItemSectionLeft]: isAssistant,
          [styles.messageItemSectionRight]: isUser,
        })}
      >
        <div
          className={classNames(styles.messageItemContent, {
            [styles.messageItemContentReverse]: isUser,
          })}
        >
          {isUser ? (
            <Avatar size={40} src={avatar ?? '/logo.svg'} />
          ) : (
            <Avatar size={40} src={avatarDialog ?? '/assistant.svg'} />
          )}

          <Flex vertical gap={8} flex={1}>
            <div
              className={
                isAssistant
                  ? styles.messageText
                  : styles.messageUserText
              }
            >
              <Text>{item.content}</Text>
            </div>
          </Flex>
        </div>
      </section>
    </div>
  );
};

export default MessageItem1;