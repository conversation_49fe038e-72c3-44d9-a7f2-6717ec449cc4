import React, { ChangeEventHandler, useCallback } from 'react';
import { Button, Input, Space, Tag } from 'antd';
import { CircleStop, Paperclip, SendHorizontal } from 'lucide-react';
import styles from './message-input.module.less';

const { TextArea } = Input;

interface ContextTag {
  id: string;
  name: string;
}

interface IProps {
  disabled: boolean;
  value: string;
  sendDisabled: boolean;
  sendLoading: boolean;
  onPressEnter(question?:string, documentIds?: string[]): void;
  onInputChange: ChangeEventHandler<HTMLTextAreaElement>;
  conversationId: string;
  showUploadIcon?: boolean;
  stopOutputMessage?(): void;
  contextTags?: ContextTag[];
  onRemoveContextTag?: (tagId: string) => void;
}

const MessageInput: React.FC<IProps> = ({
  disabled,
  value,
  onPressEnter,
  sendDisabled,
  sendLoading,
  onInputChange,
  showUploadIcon = false,
  stopOutputMessage,
  contextTags = [],
  onRemoveContextTag,
}) => {
  const handleKeyDown = useCallback(
    async (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
      if (event.key === 'Enter' && event.shiftKey) return;
      if (event.key !== 'Enter') return;
      if (sendDisabled || sendLoading) return;

      event.preventDefault();
      onPressEnter();
    },
    [sendDisabled, sendLoading, onPressEnter],
  );
  const handleStopOutputMessage = useCallback(() => {
    stopOutputMessage?.();
  }, [stopOutputMessage]);

  const handlePressEnter = useCallback(async () => {
    onPressEnter();
  }, [onPressEnter]);

  return (
    <div className={styles.messageInputWrapper}>
      {contextTags.length > 0 && (
        <div className={styles.contextTags}>
          {contextTags.map((tag) => (
            <div key={tag.id} className={styles.tag}>
              <span className={styles.tagContent}>{tag.name}</span>
              <button 
                className={styles.tagClose}
                onClick={() => onRemoveContextTag?.(tag.id)}
                type="button"
              >
                ×
              </button>
            </div>
          ))}
        </div>
      )}
      <div className={styles.inputContainer}>
        <TextArea
          placeholder="请输入消息..."
          value={value}
          disabled={disabled}
          style={{
            border: 'none',
            boxShadow: 'none',
            padding: '0px 10px',
            resize: 'none',
          }}
          autoSize={{ minRows: 1, maxRows: 10 }}
          onKeyDown={handleKeyDown}
          onChange={onInputChange}
        />
        {sendLoading ? (
          <Button type="text" onClick={handleStopOutputMessage} size="small">
            <CircleStop className="tw-size-4" />
          </Button>
        ) : (
          <Button
            type="text"
            size="small"
            onClick={handlePressEnter}
            loading={sendLoading}
            disabled={sendDisabled || sendLoading}
          >
            <SendHorizontal className="tw-size-4" />
          </Button>
        )}
      </div>
    </div>
  );
};

export default MessageInput; 