package com.etrx.kb.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.etrx.kb.enums.TaskExecutionStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 任务队列实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("tb_task_queue")
public class TaskQueue implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID（队列序号）
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务定义ID
     */
    private Long taskDefinitionId;

    /**
     * 业务数据（JSON格式）
     */
    private String businessData;

    /**
     * 执行状态
     */
    @TableField(value = "execution_status")
    private TaskExecutionStatus executionStatus;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 下次执行时间
     */
    private LocalDateTime nextExecutionTime;

    /**
     * 创建者ID
     */
    private Long createdBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 任务定义对象（不映射到数据库）
     */
    @TableField(exist = false)
    private TaskDefinition taskDefinition;

} 