package com.etrx.kb.service;

import com.etrx.kb.domain.Dict;
import com.etrx.kb.vo.DictVO;
import java.util.List;

/**
 * 数据字典Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-10
 */
public interface DictService {
    
    /**
     * 根据字典类型查询启用的数据字典列表
     * 
     * @param dictType 字典类型
     * @return 数据字典列表
     */
    List<DictVO> getEnabledDictList(String dictType);
    
    /**
     * 根据字典类型查询所有数据字典列表（包括禁用的）
     * 
     * @param dictType 字典类型
     * @return 数据字典列表
     */
    List<DictVO> getAllDictList(String dictType);
    
    /**
     * 根据字典类型构建树形结构
     * 
     * @param dictType 字典类型
     * @param onlyEnabled 是否只查询启用的
     * @return 树形结构的数据字典列表
     */
    List<DictVO> getDictTree(String dictType, boolean onlyEnabled);
    
    /**
     * 根据字典类型和键获取字典值
     * 
     * @param dictType 字典类型
     * @param dictKey 字典键
     * @return 字典对象
     */
    DictVO getDictByTypeAndKey(String dictType, String dictKey);
    
    /**
     * 根据字典键获取字典值（不需要指定类型）
     * 
     * @param dictKey 字典键
     * @return 字典对象
     */
    DictVO getDictByKey(String dictKey);

    /**
     * 根据字典键获取字典值（不需要指定类型）
     *
     * @param dictKey 字典键
     * @param defaultValue 默认值
     * @return 字典值
     */
    String getDictByKey(String dictKey, String defaultValue);
    
    /**
     * 根据字典类型查询启用的数据字典实体列表（用于需要原始实体的场景）
     * 
     * @param dictType 字典类型
     * @return 数据字典实体列表
     */
    List<Dict> getEnabledDictEntities(String dictType);
    
    /**
     * 根据字典类型查询所有数据字典实体列表（用于需要原始实体的场景）
     * 
     * @param dictType 字典类型
     * @return 数据字典实体列表
     */
    List<Dict> getAllDictEntities(String dictType);
} 