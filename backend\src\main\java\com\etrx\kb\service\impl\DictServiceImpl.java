package com.etrx.kb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.etrx.kb.domain.Dict;
import com.etrx.kb.mapper.DictMapper;
import com.etrx.kb.service.DictService;
import com.etrx.kb.vo.DictVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据字典Service实现类
 * 
 * <AUTHOR>
 * @date 2024-01-10
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DictServiceImpl implements DictService {
    
    private final DictMapper dictMapper;
    
    @Override
    public List<DictVO> getEnabledDictList(String dictType) {
        if (!StringUtils.hasText(dictType)) {
            log.warn("查询数据字典时字典类型为空");
            return Collections.emptyList();
        }
        
        List<Dict> dictList = dictMapper.selectEnabledConfigsByType(dictType);
        return convertToVOList(dictList);
    }
    
    @Override
    public List<DictVO> getAllDictList(String dictType) {
        if (!StringUtils.hasText(dictType)) {
            log.warn("查询数据字典时字典类型为空");
            return Collections.emptyList();
        }
        
        List<Dict> dictList = dictMapper.selectAllByType(dictType);
        return convertToVOList(dictList);
    }
    
    @Override
    public List<DictVO> getDictTree(String dictType, boolean onlyEnabled) {
        if (!StringUtils.hasText(dictType)) {
            log.warn("查询数据字典树时字典类型为空");
            return Collections.emptyList();
        }
        
        // 查询数据
        List<Dict> dictList = onlyEnabled ? 
            dictMapper.selectEnabledConfigsByType(dictType) : 
            dictMapper.selectAllByType(dictType);
        
        if (CollectionUtils.isEmpty(dictList)) {
            return Collections.emptyList();
        }
        
        // 转换为VO列表
        List<DictVO> voList = convertToVOList(dictList);
        
        // 构建树形结构
        return buildTree(voList);
    }
    
    @Override
    public DictVO getDictByTypeAndKey(String dictType, String dictKey) {
        if (!StringUtils.hasText(dictType) || !StringUtils.hasText(dictKey)) {
            log.warn("查询数据字典时参数不完整: dictType={}, dictKey={}", dictType, dictKey);
            return null;
        }
        
        LambdaQueryWrapper<Dict> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Dict::getDictType, dictType)
                   .eq(Dict::getDictKey, dictKey)
                   .eq(Dict::getDeleted, 0)
                   .eq(Dict::getStatus, 1)
                   .last("LIMIT 1");
        
        Dict dict = dictMapper.selectOne(queryWrapper);
        if (dict == null) {
            return null;
        }
        
        return convertToVO(dict);
    }
    
    @Override
    public DictVO getDictByKey(String dictKey) {
        if (!StringUtils.hasText(dictKey)) {
            log.warn("查询数据字典时字典键为空");
            return null;
        }
        
        Dict dict = dictMapper.selectEnabledByKey(dictKey);
        if (dict == null) {
            log.info("未找到字典键为 {} 的数据字典", dictKey);
            return null;
        }
        
        return convertToVO(dict);
    }

    @Override
    public String getDictByKey(String dictKey, String defaultValue) {
        DictVO dict = getDictByKey(dictKey);
        if (dict == null
            || org.apache.commons.lang3.StringUtils.isEmpty(dict.getDictValue())) {
            return defaultValue;
        }

        return dict.getDictValue();
    }
    
    @Override
    public List<Dict> getEnabledDictEntities(String dictType) {
        if (!StringUtils.hasText(dictType)) {
            log.warn("查询数据字典实体时字典类型为空");
            return Collections.emptyList();
        }
        
        return dictMapper.selectEnabledConfigsByType(dictType);
    }
    
    @Override
    public List<Dict> getAllDictEntities(String dictType) {
        if (!StringUtils.hasText(dictType)) {
            log.warn("查询数据字典实体时字典类型为空");
            return Collections.emptyList();
        }
        
        return dictMapper.selectAllByType(dictType);
    }
    
    /**
     * 将实体列表转换为VO列表
     */
    private List<DictVO> convertToVOList(List<Dict> dictList) {
        if (CollectionUtils.isEmpty(dictList)) {
            return Collections.emptyList();
        }
        
        return dictList.stream()
                      .map(this::convertToVO)
                      .collect(Collectors.toList());
    }
    
    /**
     * 将实体转换为VO
     */
    private DictVO convertToVO(Dict dict) {
        if (dict == null) {
            return null;
        }
        
        DictVO vo = new DictVO();
        BeanUtils.copyProperties(dict, vo);
        return vo;
    }
    
    /**
     * 构建树形结构
     */
    private List<DictVO> buildTree(List<DictVO> allNodes) {
        if (CollectionUtils.isEmpty(allNodes)) {
            return Collections.emptyList();
        }
        
        // 创建节点映射
        Map<Long, DictVO> nodeMap = allNodes.stream()
                .collect(Collectors.toMap(DictVO::getId, node -> node));
        
        // 构建树形结构
        List<DictVO> rootNodes = new ArrayList<>();
        
        for (DictVO node : allNodes) {
            Long parentId = node.getParentId();
            if (parentId == null || parentId == 0) {
                // 根节点
                rootNodes.add(node);
            } else {
                // 子节点
                DictVO parent = nodeMap.get(parentId);
                if (parent != null) {
                    if (parent.getChildren() == null) {
                        parent.setChildren(new ArrayList<>());
                    }
                    parent.getChildren().add(node);
                }
            }
        }
        
        // 对每个节点的子节点进行排序
        sortChildren(rootNodes);
        
        return rootNodes;
    }
    
    /**
     * 递归排序子节点
     */
    private void sortChildren(List<DictVO> nodes) {
        if (CollectionUtils.isEmpty(nodes)) {
            return;
        }
        
        // 按sortOrder和id排序
        nodes.sort((a, b) -> {
            int result = Integer.compare(
                a.getSortOrder() != null ? a.getSortOrder() : 0,
                b.getSortOrder() != null ? b.getSortOrder() : 0
            );
            if (result == 0) {
                result = Long.compare(a.getId(), b.getId());
            }
            return result;
        });
        
        // 递归排序子节点
        for (DictVO node : nodes) {
            if (!CollectionUtils.isEmpty(node.getChildren())) {
                sortChildren(node.getChildren());
            }
        }
    }
} 