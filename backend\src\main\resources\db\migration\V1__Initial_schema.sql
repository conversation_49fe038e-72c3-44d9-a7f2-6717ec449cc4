-- 用户表
CREATE TABLE IF NOT EXISTS `tb_user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(100) NOT NULL COMMENT '密码',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `role` tinyint NOT NULL DEFAULT '1' COMMENT '角色(1:普通用户,2:管理员,3:超级管理员)',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(0:禁用,1:启用)',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除(0:未删除,1:已删除)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_username` (`username`),
  UNIQUE KEY `idx_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户表';

-- 知识库表
CREATE TABLE IF NOT EXISTS `tb_knowledge_base` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '知识库名称',
  `description` varchar(500) DEFAULT NULL COMMENT '知识库描述',
  `type` varchar(20) NOT NULL COMMENT '类型(Open,App)',
  `api_key` varchar(100) DEFAULT NULL COMMENT 'API Key(App类型使用)',
  `owner_id` bigint NOT NULL COMMENT '所有者ID',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(0:禁用,1:启用)',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除(0:未删除,1:已删除)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_owner_id` (`owner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='知识库表';

-- 知识库成员表
CREATE TABLE IF NOT EXISTS `tb_kb_member` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `kb_id` bigint NOT NULL COMMENT '知识库ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `role` tinyint NOT NULL DEFAULT '10' COMMENT '角色(10:member,20:admin,30:owner)',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除(0:未删除,1:已删除)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_kb_user` (`kb_id`,`user_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='知识库成员表';

-- 文件夹表
CREATE TABLE IF NOT EXISTS `tb_folder` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `kb_id` bigint NOT NULL COMMENT '知识库ID',
  `name` varchar(100) NOT NULL COMMENT '文件夹名称',
  `parent_id` bigint DEFAULT NULL COMMENT '父文件夹ID',
  `creator_id` bigint NOT NULL COMMENT '创建者ID',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除(0:未删除,1:已删除)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_kb_id` (`kb_id`),
  KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='文件夹表';

-- 文档表
CREATE TABLE IF NOT EXISTS `tb_document` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `kb_id` bigint NOT NULL COMMENT '知识库ID',
  `name` varchar(255) NOT NULL COMMENT '文档名称',
  `file_path` varchar(500) NOT NULL COMMENT '文件路径',
  `file_type` varchar(50) NOT NULL COMMENT '文件类型',
  `file_size` bigint NOT NULL COMMENT '文件大小(字节)',
  `folder_id` bigint DEFAULT NULL COMMENT '文件夹ID',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态(0:处理中,1:已完成,2:失败)',
  `embed_status` tinyint NOT NULL DEFAULT '0' COMMENT '嵌入状态(0:未嵌入,1:嵌入中,2:已嵌入,3:失败)',
  `uploader_id` bigint NOT NULL COMMENT '上传者ID',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除(0:未删除,1:已删除)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_kb_id` (`kb_id`),
  KEY `idx_folder_id` (`folder_id`),
  KEY `idx_uploader_id` (`uploader_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='文档表';

-- API密钥表
CREATE TABLE IF NOT EXISTS `tb_api_key` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `app_name` varchar(100) NOT NULL COMMENT '应用名称',
  `api_key` varchar(100) NOT NULL COMMENT 'API密钥',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(0:禁用,1:启用)',
  `creator_id` bigint NOT NULL COMMENT '创建者ID',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除(0:未删除,1:已删除)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_api_key` (`api_key`),
  KEY `idx_creator_id` (`creator_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='API密钥表';

-- 外键约束
ALTER TABLE `tb_knowledge_base` ADD CONSTRAINT `fk_kb_owner` FOREIGN KEY (`owner_id`) REFERENCES `tb_user` (`id`);
ALTER TABLE `tb_kb_member` ADD CONSTRAINT `fk_member_kb` FOREIGN KEY (`kb_id`) REFERENCES `tb_knowledge_base` (`id`);
ALTER TABLE `tb_kb_member` ADD CONSTRAINT `fk_member_user` FOREIGN KEY (`user_id`) REFERENCES `tb_user` (`id`);
ALTER TABLE `tb_folder` ADD CONSTRAINT `fk_folder_kb` FOREIGN KEY (`kb_id`) REFERENCES `tb_knowledge_base` (`id`);
ALTER TABLE `tb_folder` ADD CONSTRAINT `fk_folder_parent` FOREIGN KEY (`parent_id`) REFERENCES `tb_folder` (`id`);
ALTER TABLE `tb_document` ADD CONSTRAINT `fk_doc_kb` FOREIGN KEY (`kb_id`) REFERENCES `tb_knowledge_base` (`id`);
ALTER TABLE `tb_document` ADD CONSTRAINT `fk_doc_folder` FOREIGN KEY (`folder_id`) REFERENCES `tb_folder` (`id`);
ALTER TABLE `tb_document` ADD CONSTRAINT `fk_doc_uploader` FOREIGN KEY (`uploader_id`) REFERENCES `tb_user` (`id`);
ALTER TABLE `tb_api_key` ADD CONSTRAINT `fk_api_key_creator` FOREIGN KEY (`creator_id`) REFERENCES `tb_user` (`id`);