package com.etrx.kb.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.etrx.kb.vo.base.RagflowBaseVO;

import java.util.List;
import java.util.Map;

/**
 * Ragflow文件上传返回结果
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class RagflowUploadVO extends RagflowBaseVO {
    private List<UploadFileVO> data;

    @Data
    @EqualsAndHashCode(callSuper = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class UploadFileVO extends BaseUploadFileVO {
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ParserConfigVO extends BaseParserConfigVO {
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class RaptorVO extends BaseRaptorVO {
    }
} 