package com.etrx.kb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.etrx.kb.domain.KnowledgeBaseMember;
import com.etrx.kb.vo.KnowledgeBaseMemberVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 知识库成员Mapper接口
 */
@Mapper
public interface KnowledgeBaseMemberMapper extends BaseMapper<KnowledgeBaseMember> {

    /**
     * 获取用户的所有知识库成员关系
     *
     * @param userId 用户ID
     * @return 知识库成员关系列表
     */
    @Select("""
            SELECT * 
            FROM tb_kb_member 
            WHERE user_id = #{userId} 
            AND deleted = 0
            """)
    List<KnowledgeBaseMember> findByUserId(@Param("userId") Long userId);

    /**
     * 获取知识库的所有成员关系
     *
     * @param kbId 知识库ID
     * @return 知识库成员关系列表
     */
    @Select("""
            SELECT * 
            FROM tb_kb_member 
            WHERE kb_id = #{kbId} 
            AND deleted = 0
            """)
    List<KnowledgeBaseMember> findByKbId(@Param("kbId") Long kbId);

    /**
     * 获取知识库的所有成员关系及用户信息
     *
     * @param kbId 知识库ID
     * @return 知识库成员关系列表
     */
    @Select("""
            SELECT m.*, u.username, u.nickname, u.avatar 
            FROM tb_kb_member m 
            LEFT JOIN tb_user u ON m.user_id = u.id 
            WHERE m.kb_id = #{kbId} 
            AND m.deleted = 0
            """)
    List<KnowledgeBaseMemberVO> findMembersWithUserInfo(@Param("kbId") Long kbId);

    /**
     * 获取用户在指定知识库中的成员关系（不包括已删除的）
     *
     * @param kbId   知识库ID
     * @param userId 用户ID
     * @return 知识库成员关系
     */
    @Select("""
            SELECT * 
            FROM tb_kb_member 
            WHERE kb_id = #{kbId} 
            AND user_id = #{userId}
            AND deleted = 0
            """)
    KnowledgeBaseMember findByKbIdAndUserId(@Param("kbId") Long kbId, @Param("userId") Long userId);

    /**
     * 获取用户在指定知识库中的所有成员关系（包括已删除的）
     *
     * @param kbId   知识库ID
     * @param userId 用户ID
     * @return 知识库成员关系
     */
    @Select("""
            SELECT * 
            FROM tb_kb_member 
            WHERE kb_id = #{kbId} 
            AND user_id = #{userId}
            """)
    KnowledgeBaseMember findByKbIdAndUserIdWithDeleted(@Param("kbId") Long kbId, @Param("userId") Long userId);

    /**
     * 恢复已删除的成员并更新角色
     *
     * @param id         成员ID
     * @param role       新角色
     * @param updateTime 更新时间
     * @return 更新的记录数
     */
    @Update("""
            UPDATE tb_kb_member 
            SET deleted = 0, 
                role = #{role}, 
                update_time = #{updateTime} 
            WHERE id = #{id} 
            AND deleted = 1
            """)
    int restoreDeletedMember(@Param("id") Long id, @Param("role") Integer role, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 获取知识库的成员数量
     *
     * @param kbId 知识库ID
     * @return 成员数量
     */
    @Select("""
            SELECT COUNT(*) 
            FROM tb_kb_member 
            WHERE kb_id = #{kbId} 
            AND deleted = 0
            """)
    Long countByKbId(@Param("kbId") Long kbId);

    /**
     * 批量获取知识库的成员数量
     */
    @Select("""
            <script>
            SELECT kb_id, COUNT(*) as count 
            FROM tb_kb_member 
            WHERE kb_id IN 
            <foreach collection='kbIds' item='item' open='(' separator=',' close=')'>
                #{item}
            </foreach>
            AND deleted = 0 
            GROUP BY kb_id
            </script>
            """)
    List<Map<String, Object>> batchCountByKbIdsList(@Param("kbIds") List<Long> kbIds);

    /**
     * 批量获取知识库的成员数量（返回Map形式）
     */
    default Map<Long, Long> batchCountByKbIds(List<Long> kbIds) {
        if (kbIds == null || kbIds.isEmpty()) {
            return new HashMap<>();
        }
        return batchCountByKbIdsList(kbIds).stream()
                .collect(java.util.stream.Collectors.toMap(
                        map -> Long.valueOf(String.valueOf(map.get("kb_id"))),
                        map -> Long.valueOf(String.valueOf(map.get("count")))
                ));
    }

    /**
     * 批量获取用户在多个知识库中的角色
     */
    @Select("""
            <script>
            SELECT * 
            FROM tb_kb_member 
            WHERE kb_id IN 
            <foreach collection='kbIds' item='item' open='(' separator=',' close=')'>
                #{item}
            </foreach>
            AND user_id = #{userId}
            AND deleted = 0
            </script>
            """)
    List<KnowledgeBaseMember> findByKbIdsAndUserId(@Param("kbIds") List<Long> kbIds, @Param("userId") Long userId);
}