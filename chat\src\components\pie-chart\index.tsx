import {
  <PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Responsive<PERSON>ontainer,
  Toolt<PERSON>,
} from 'recharts';
import { PieChartConfig } from '@/utils/chart-parser';
import { memo, useMemo } from 'react';

interface PieChartProps extends PieChartConfig {}

const COLORS = [
  '#0088FE',
  '#00C49F',
  '#FFBB28',
  '#FF8042',
  '#8884D8',
  '#82CA9D',
  '#FFC658',
  '#FF6B6B',
  '#4ECDC4',
  '#45B7D1',
];

const PieChartComponent = memo(({
  data,
  width = '100%',
  height = 300,
  showLegend = true,
  showTooltip = true,
}: PieChartProps) => {
  // 使用 useMemo 缓存图表数据，只有当 data 真正变化时才重新计算
  const chartData = useMemo(() => {
    return data.map((item, index) => ({
      ...item,
      color: item.color || COLORS[index % COLORS.length],
    }));
  }, [data]);

  // 生成稳定的 key 用于 React 的 diff 算法
  const chartKey = useMemo(() => {
    return data.map(item => `${item.name}-${item.value}`).join('|');
  }, [data]);

  return (
    <ResponsiveContainer width={width} height={height}>
      <PieChart key={chartKey}>
        <Pie
          data={chartData}
          cx="50%"
          cy="50%"
          labelLine={false}
          label={({ name, percent }) =>
            `${name} ${(percent * 100).toFixed(0)}%`
          }
          outerRadius={80}
          fill="#8884d8"
          dataKey="value"
        >
          {chartData.map((entry, index) => (
            <Cell key={`cell-${index}-${entry.name}`} fill={entry.color} />
          ))}
        </Pie>
        {showTooltip && <Tooltip />}
        {showLegend && <Legend />}
      </PieChart>
    </ResponsiveContainer>
  );
});

PieChartComponent.displayName = 'PieChartComponent';

export default PieChartComponent; 