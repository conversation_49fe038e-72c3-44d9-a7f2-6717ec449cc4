package com.etrx.kb.service.impl;

import com.etrx.kb.dto.ChunkRequestDTO;
import com.etrx.kb.service.ChunkService;
import erd.cloud.ai.ragflow.RagflowChunkClient;
import erd.cloud.ai.ragflow.RagflowChunkClientFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * Chunk服务实现类
 * 使用RagflowChunkClient调用Ragflow API
 */
@Slf4j
@Service
public class ChunkServiceImpl implements ChunkService {

    /**
     * 添加Chunk
     *
     * @param request 添加Chunk请求参数
     * @return 添加结果JSON字符串
     */
    @Override
    public String addChunk(ChunkRequestDTO.AddChunkRequest request) {
        try {
            RagflowChunkClient client = RagflowChunkClientFactory.getClient();
            return client.addChunk(
                request.getDatasetId(),
                request.getDocumentId(),
                request.getContent(),
                request.getImportantKeywords(),
                request.getQuestions()
            );
        } catch (IOException e) {
            log.error("添加Chunk失败: datasetId={}, documentId={}, error={}",
                request.getDatasetId(), request.getDocumentId(), e.getMessage(), e);
            throw new RuntimeException("添加Chunk失败: " + e.getMessage(), e);
        }
    }

    /**
     * 查询Chunk列表
     *
     * @param request 查询请求参数
     * @return Chunk列表JSON字符串
     */
    @Override
    public String listChunks(ChunkRequestDTO.ListChunkRequest request) {
        try {
            RagflowChunkClient client = RagflowChunkClientFactory.getClient();
            
            // 构建查询参数
            Map<String, String> queryParams = new HashMap<>();
            if (request.getKeywords() != null) {
                queryParams.put("keywords", request.getKeywords());
            }
            if (request.getPage() != null) {
                queryParams.put("page", request.getPage().toString());
            }
            if (request.getPageSize() != null) {
                queryParams.put("page_size", request.getPageSize().toString());
            }
            if (request.getId() != null) {
                queryParams.put("id", request.getId());
            }
            
            return client.listChunks(
                request.getDatasetId(),
                request.getDocumentId(),
                queryParams
            );
        } catch (IOException e) {
            log.error("查询Chunk列表失败: datasetId={}, documentId={}, error={}",
                request.getDatasetId(), request.getDocumentId(), e.getMessage(), e);
            throw new RuntimeException("查询Chunk列表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 删除Chunk
     *
     * @param request 删除请求参数
     * @return 删除结果JSON字符串
     */
    @Override
    public String deleteChunks(ChunkRequestDTO.DeleteChunkRequest request) {
        try {
            RagflowChunkClient client = RagflowChunkClientFactory.getClient();
            return client.deleteChunks(
                request.getDatasetId(),
                request.getDocumentId(),
                request.getChunkIds()
            );
        } catch (IOException e) {
            log.error("删除Chunk失败: datasetId={}, documentId={}, chunkIds={}, error={}",
                request.getDatasetId(), request.getDocumentId(), request.getChunkIds(), e.getMessage(), e);
            throw new RuntimeException("删除Chunk失败: " + e.getMessage(), e);
        }
    }

    /**
     * 更新Chunk
     *
     * @param request 更新请求参数
     * @return 更新结果JSON字符串
     */
    @Override
    public String updateChunk(ChunkRequestDTO.UpdateChunkRequest request) {
        try {
            RagflowChunkClient client = RagflowChunkClientFactory.getClient();
            return client.updateChunk(
                request.getDatasetId(),
                request.getDocumentId(),
                request.getChunkId(),
                request.getUpdateFields()
            );
        } catch (IOException e) {
            log.error("更新Chunk失败: datasetId={}, documentId={}, chunkId={}, error={}",
                request.getDatasetId(), request.getDocumentId(), request.getChunkId(), e.getMessage(), e);
            throw new RuntimeException("更新Chunk失败: " + e.getMessage(), e);
        }
    }

    /**
     * 批量更新Chunk
     *
     * @param request 批量更新请求参数
     * @return 更新结果JSON字符串
     */
    @Override
    public String batchUpdateChunks(ChunkRequestDTO.BatchUpdateChunkRequest request) {
        try {
            RagflowChunkClient client = RagflowChunkClientFactory.getClient();
            
            // 批量更新通过循环调用单个更新方法实现
            int successCount = 0;
            int failureCount = 0;
            StringBuilder resultBuilder = new StringBuilder();
            
            for (String chunkId : request.getChunkIds()) {
                try {
                    String result = client.updateChunk(
                        request.getDatasetId(),
                        request.getDocumentId(),
                        chunkId,
                        request.getUpdateFields()
                    );
                    successCount++;
                    log.debug("更新Chunk成功: chunkId={}, result={}", chunkId, result);
                } catch (IOException e) {
                    failureCount++;
                    log.error("更新Chunk失败: chunkId={}, error={}", chunkId, e.getMessage(), e);
                }
            }
            
            // 构建批量更新结果
            Map<String, Object> batchResult = new HashMap<>();
            batchResult.put("success_count", successCount);
            batchResult.put("failure_count", failureCount);
            batchResult.put("total_count", request.getChunkIds().size());
            batchResult.put("message", String.format("批量更新完成，成功: %d, 失败: %d", successCount, failureCount));
            
            return new com.fasterxml.jackson.databind.ObjectMapper().writeValueAsString(batchResult);
        } catch (Exception e) {
            log.error("批量更新Chunk失败: datasetId={}, documentId={}, chunkIds={}, error={}",
                request.getDatasetId(), request.getDocumentId(), request.getChunkIds(), e.getMessage(), e);
            throw new RuntimeException("批量更新Chunk失败: " + e.getMessage(), e);
        }
    }

    /**
     * 检索Chunk
     *
     * @param request 检索请求参数
     * @return 检索结果JSON字符串
     */
    @Override
    public String retrieveChunks(ChunkRequestDTO.RetrieveChunkRequest request) {
        try {
            RagflowChunkClient client = RagflowChunkClientFactory.getClient();
            
            // 构建查询参数
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("question", request.getQuestion());
            
            if (request.getDatasetIds() != null && !request.getDatasetIds().isEmpty()) {
                queryParams.put("dataset_ids", request.getDatasetIds());
            }
            if (request.getDocumentIds() != null && !request.getDocumentIds().isEmpty()) {
                queryParams.put("document_ids", request.getDocumentIds());
            }
            if (request.getPage() != null) {
                queryParams.put("page", request.getPage());
            }
            if (request.getPageSize() != null) {
                queryParams.put("page_size", request.getPageSize());
            }
            if (request.getSimilarityThreshold() != null) {
                queryParams.put("similarity_threshold", request.getSimilarityThreshold());
            }
            if (request.getVectorSimilarityWeight() != null) {
                queryParams.put("vector_similarity_weight", request.getVectorSimilarityWeight());
            }
            if (request.getTopK() != null) {
                queryParams.put("top_k", request.getTopK());
            }
            if (request.getRerankId() != null) {
                queryParams.put("rerank_id", request.getRerankId());
            }
            if (request.getKeyword() != null) {
                queryParams.put("keyword", request.getKeyword());
            }
            if (request.getHighlight() != null) {
                queryParams.put("highlight", request.getHighlight());
            }
            
            return client.retrieveChunks(queryParams);
        } catch (IOException e) {
            log.error("检索Chunk失败: question={}, error={}", request.getQuestion(), e.getMessage(), e);
            throw new RuntimeException("检索Chunk失败: " + e.getMessage(), e);
        }
    }
} 