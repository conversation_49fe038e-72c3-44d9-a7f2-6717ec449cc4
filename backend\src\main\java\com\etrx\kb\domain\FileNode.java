package com.etrx.kb.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;
import java.util.List;

@Data
@EqualsAndHashCode
@TableName("tb_file_node")
public class FileNode {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String name;
    private Integer nodeType;  // 1:文件夹,2:文件
    private Long parentId;
    private String fullPath;
    private String fileType;
    private Long fileSize;
    private String storagePath;
    private Integer status;    // 0:处理中,1:正常,2:异常
    private Integer embedStatus; // 0:未嵌入,1:嵌入中,2:已嵌入,3:失败
    private Long creatorId;
    
    @TableField(exist = false)
    private String creatorName;
    
    @TableField(exist = false)
    private List<KnowledgeBaseInfo> knowledgeBases;  // 关联的知识库列表
    
    @TableField(exist = false)
    private Boolean isCurrentKnowledgeBase;  // 是否属于当前知识库
    
    @TableLogic
    private Integer deleted;
    
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    
    /**
     * 知识库信息内部类
     */
    @Data
    public static class KnowledgeBaseInfo {
        private Long id;          // 知识库ID
        private String name;      // 知识库名称
        private Integer relType;  // 关联类型：1-直接上传，2-手动关联
        private String documentId;     // Ragflow文档ID
        private Integer documentStatus; // 文档状态
        private String documentInfo;    // 文档详细信息
    }
} 