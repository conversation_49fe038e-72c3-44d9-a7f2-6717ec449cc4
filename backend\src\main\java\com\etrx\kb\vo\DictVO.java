package com.etrx.kb.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 数据字典VO
 * 
 * <AUTHOR>
 * @date 2024-01-10
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DictVO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 字典类型
     */
    private String dictType;
    
    /**
     * 字典键
     */
    private String dictKey;
    
    /**
     * 字典值
     */
    private String dictValue;
    
    /**
     * 字典标签
     */
    private String dictLabel;
    
    /**
     * 父节点ID
     */
    private Long parentId;
    
    /**
     * 节点类型（1:目录,2:配置项）
     */
    private Integer nodeType;
    
    /**
     * 层级深度
     */
    private Integer pathLevel;
    
    /**
     * 匹配类型（1:精确匹配,2:类通配符,3:包通配符）
     */
    private Integer matchType;
    
    /**
     * HTTP方法（GET,POST,PUT,DELETE等,可选）
     */
    private String httpMethod;
    
    /**
     * URL模式（可选,用于双重验证）
     */
    private String urlPattern;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 排序
     */
    private Integer sortOrder;
    
    /**
     * 状态（0:禁用,1:启用）
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 子节点列表（用于构建树形结构）
     */
    private List<DictVO> children;
} 