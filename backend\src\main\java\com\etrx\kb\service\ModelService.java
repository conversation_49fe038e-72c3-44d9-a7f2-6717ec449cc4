package com.etrx.kb.service;

import com.etrx.kb.dto.ModelDTO;
import com.etrx.kb.config.ModelConfig;

import java.util.List;
import java.util.Map;

/**
 * 模型服务接口
 */
public interface ModelService {

    /**
     * 获取所有可用的模型列表
     * 按提供商分组返回
     *
     * @return 按提供商分组的模型列表
     */
    Map<String, List<ModelDTO>> getAllModels();

    /**
     * 获取指定提供商的模型列表
     *
     * @param provider 提供商名称
     * @return 模型列表
     */
    List<ModelDTO> getModelsByProvider(String provider);

    /**
     * 获取指定类型的模型列表
     *
     * @param modelType 模型类型（chat, embedding, tts, speech2text）
     * @return 模型列表
     */
    List<ModelDTO> getModelsByType(String modelType);

    /**
     * 检查模型是否可用
     *
     * @param provider 提供商名称
     * @param modelName 模型名称
     * @return 是否可用
     */
    Boolean isModelAvailable(String provider, String modelName);

    /**
     * 添加新的模型配置
     *
     * @param provider 提供商名称
     * @param modelConfigItem 模型配置项
     * @return 是否添加成功
     */
    Boolean addModelConfig(String provider, ModelConfig.ModelConfigItem modelConfigItem);

    /**
     * 更新模型配置
     *
     * @param provider 提供商名称
     * @param modelName 模型名称
     * @param modelConfigItem 更新后的模型配置项
     * @return 是否更新成功
     */
    Boolean updateModelConfig(String provider, String modelName, ModelConfig.ModelConfigItem modelConfigItem);

    /**
     * 删除模型配置
     *
     * @param provider 提供商名称
     * @param modelName 模型名称
     * @return 是否删除成功
     */
    Boolean deleteModelConfig(String provider, String modelName);

    /**
     * 添加新的提供商
     *
     * @param provider 提供商名称
     * @param providerConfig 提供商配置（包含模型列表）
     * @return 是否添加成功
     */
    Boolean addProvider(String provider, ModelConfig.ProviderConfig providerConfig);

    /**
     * 删除提供商及其所有模型
     *
     * @param provider 提供商名称
     * @return 是否删除成功
     */
    Boolean deleteProvider(String provider);

    /**
     * 重新加载配置
     *
     * @return 是否重载成功
     */
    Boolean reloadConfig();

    /**
     * 获取当前完整的模型配置
     *
     * @return 模型配置
     */
    ModelConfig getModelConfig();
} 