package com.etrx.kb.service.impl;

import com.etrx.kb.config.ModelConfig;
import com.etrx.kb.domain.ModelConfigEntity;
import com.etrx.kb.dto.ModelDTO;
import com.etrx.kb.mapper.ModelConfigMapper;
import com.etrx.kb.service.ModelService;
import com.etrx.kb.util.ModelConfigConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 模型服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ModelServiceImpl implements ModelService, CommandLineRunner {

    private final ModelConfig modelConfig;
    private final ModelConfigMapper modelConfigMapper;

    /**
     * 应用启动时初始化数据库配置
     */
    @Override
    public void run(String... args) throws Exception {
        initializeModelConfigFromYaml();
    }

    /**
     * 如果数据库中没有模型配置，则从YAML配置文件初始化
     */
    @Transactional
    public void initializeModelConfigFromYaml() {
        try {
            // 检查数据库中是否已有配置
            long count = modelConfigMapper.countAll();
            if (count > 0) {
                log.info("数据库中已存在 {} 条模型配置，跳过初始化", count);
                return;
            }

            // 从YAML配置文件导入
            if (modelConfig.getProviders() == null || modelConfig.getProviders().isEmpty()) {
                log.warn("YAML配置文件中未找到模型配置");
                return;
            }

            int totalImported = 0;
            for (Map.Entry<String, ModelConfig.ProviderConfig> entry : modelConfig.getProviders().entrySet()) {
                String provider = entry.getKey();
                ModelConfig.ProviderConfig providerConfig = entry.getValue();
                
                // 检查提供商是否启用
                if (providerConfig.getEnabled() == null || !providerConfig.getEnabled()) {
                    log.info("跳过未启用的提供商: {}", provider);
                    continue;
                }
                
                // 获取模型列表
                List<ModelConfig.ModelConfigItem> items = providerConfig.getModels();
                if (items == null || items.isEmpty()) {
                    log.warn("提供商 {} 没有配置模型", provider);
                    continue;
                }
                
                for (ModelConfig.ModelConfigItem item : items) {
                    ModelConfigEntity entity = ModelConfigConverter.configItemToEntity(provider, item);
                    modelConfigMapper.insert(entity);
                    totalImported++;
                }
            }
            
            log.info("成功从YAML配置文件导入 {} 条模型配置到数据库", totalImported);
        } catch (Exception e) {
            log.error("初始化模型配置失败", e);
        }
    }

    @Override
    public Map<String, List<ModelDTO>> getAllModels() {
        Map<String, List<ModelDTO>> result = new LinkedHashMap<>();
        try {
            List<ModelConfigEntity> entities = modelConfigMapper.selectList(null);
            // 按提供商分组
            Map<String, List<ModelConfigEntity>> groupedByProvider = entities.stream()
                    .collect(Collectors.groupingBy(ModelConfigEntity::getProvider));
            for (Map.Entry<String, List<ModelConfigEntity>> entry : groupedByProvider.entrySet()) {
                String provider = entry.getKey();
                List<ModelDTO> models = entry.getValue().stream()
                        .map(ModelConfigConverter::entityToDTO)
                        .collect(Collectors.toList());
                result.put(provider, models);
            }
            log.debug("获取所有模型列表，共 {} 个提供商", result.size());
        } catch (Exception e) {
            log.error("获取所有模型列表失败", e);
        }
        
        return result;
    }

    @Override
    public List<ModelDTO> getModelsByProvider(String provider) {
        try {
            List<ModelConfigEntity> entities = modelConfigMapper.findByProvider(provider);
            List<ModelDTO> models = entities.stream()
                    .map(ModelConfigConverter::entityToDTO)
                    .collect(Collectors.toList());
                    
            log.debug("获取提供商 {} 的模型列表，共 {} 个模型", provider, models.size());
            return models;
        } catch (Exception e) {
            log.error("获取提供商 {} 的模型列表失败", provider, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<ModelDTO> getModelsByType(String modelType) {
        try {
            List<ModelConfigEntity> entities = modelConfigMapper.findByModelType(modelType);
            List<ModelDTO> models = entities.stream()
                    .map(ModelConfigConverter::entityToDTO)
                    .collect(Collectors.toList());
                    
            log.debug("获取类型为 {} 的模型列表，共 {} 个模型", modelType, models.size());
            return models;
        } catch (Exception e) {
            log.error("获取类型为 {} 的模型列表失败", modelType, e);
            return new ArrayList<>();
        }
    }

    @Override
    public Boolean isModelAvailable(String provider, String modelName) {
        try {
            ModelConfigEntity entity = modelConfigMapper.findByProviderAndModelName(provider, modelName);
            boolean available = entity != null && Boolean.TRUE.equals(entity.getAvailable());
            
            log.debug("检查模型可用性: {} - {} = {}", provider, modelName, available);
            return available;
        } catch (Exception e) {
            log.error("检查模型可用性失败: {} - {}", provider, modelName, e);
            return false;
        }
    }

    @Override
    @Transactional
    public Boolean addModelConfig(String provider, ModelConfig.ModelConfigItem modelConfigItem) {
        try {
            // 检查是否已存在同名模型
            ModelConfigEntity existing = modelConfigMapper.findByProviderAndModelName(provider, modelConfigItem.getLlmName());
            if (existing != null) {
                log.warn("模型 {} 在提供商 {} 中已存在", modelConfigItem.getLlmName(), provider);
                return false;
            }
            
            ModelConfigEntity entity = ModelConfigConverter.configItemToEntity(provider, modelConfigItem);
            int result = modelConfigMapper.insert(entity);
            
            if (result > 0) {
                log.info("成功添加模型配置: {} - {}", provider, modelConfigItem.getLlmName());
                return true;
            } else {
                log.warn("添加模型配置失败: {} - {}", provider, modelConfigItem.getLlmName());
                return false;
            }
        } catch (Exception e) {
            log.error("添加模型配置失败: {} - {}", provider, modelConfigItem.getLlmName(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public Boolean updateModelConfig(String provider, String modelName, ModelConfig.ModelConfigItem modelConfigItem) {
        try {
            ModelConfigEntity existing = modelConfigMapper.findByProviderAndModelName(provider, modelName);
            if (existing == null) {
                log.warn("模型 {} 在提供商 {} 中不存在", modelName, provider);
                return false;
            }
            
            // 更新实体
            existing.setLlmName(modelConfigItem.getLlmName());
            existing.setModelType(modelConfigItem.getModelType());
            existing.setAvailable(modelConfigItem.getAvailable() != null ? modelConfigItem.getAvailable() : true);
            existing.setMaxTokens(modelConfigItem.getMaxTokens());
            existing.setIsTools(modelConfigItem.getIsTools() != null ? modelConfigItem.getIsTools() : false);
            existing.setStatus(modelConfigItem.getStatus() != null ? modelConfigItem.getStatus() : "1");
            existing.setTags(modelConfigItem.getTags());
            
            int result = modelConfigMapper.updateById(existing);
            
            if (result > 0) {
                log.info("成功更新模型配置: {} - {}", provider, modelName);
                return true;
            } else {
                log.warn("更新模型配置失败: {} - {}", provider, modelName);
                return false;
            }
        } catch (Exception e) {
            log.error("更新模型配置失败: {} - {}", provider, modelName, e);
            return false;
        }
    }

    @Override
    @Transactional
    public Boolean deleteModelConfig(String provider, String modelName) {
        try {
            ModelConfigEntity entity = modelConfigMapper.findByProviderAndModelName(provider, modelName);
            if (entity == null) {
                log.warn("模型 {} 在提供商 {} 中不存在", modelName, provider);
                return false;
            }
            
            int result = modelConfigMapper.deleteById(entity.getId());
            
            if (result > 0) {
                log.info("成功删除模型配置: {} - {}", provider, modelName);
                return true;
            } else {
                log.warn("删除模型配置失败: {} - {}", provider, modelName);
                return false;
            }
        } catch (Exception e) {
            log.error("删除模型配置失败: {} - {}", provider, modelName, e);
            return false;
        }
    }

    @Override
    @Transactional
    public Boolean addProvider(String provider, ModelConfig.ProviderConfig providerConfig) {
        try {
            // 检查提供商是否已存在
            List<ModelConfigEntity> existing = modelConfigMapper.findByProvider(provider);
            if (!existing.isEmpty()) {
                log.warn("提供商 {} 已存在", provider);
                return false;
            }
            
            // 检查提供商配置
            if (providerConfig == null || providerConfig.getModels() == null || providerConfig.getModels().isEmpty()) {
                log.warn("提供商 {} 的配置为空或没有模型", provider);
                return false;
            }
            
            // 批量插入模型
            for (ModelConfig.ModelConfigItem item : providerConfig.getModels()) {
                ModelConfigEntity entity = ModelConfigConverter.configItemToEntity(provider, item);
                modelConfigMapper.insert(entity);
            }
            
            log.info("成功添加提供商: {}，包含 {} 个模型", provider, providerConfig.getModels().size());
            return true;
        } catch (Exception e) {
            log.error("添加提供商失败: {}", provider, e);
            return false;
        }
    }

    @Override
    @Transactional
    public Boolean deleteProvider(String provider) {
        try {
            List<ModelConfigEntity> entities = modelConfigMapper.findByProvider(provider);
            if (entities.isEmpty()) {
                log.warn("提供商 {} 不存在", provider);
                return false;
            }
            
            // 批量删除该提供商的所有模型
            List<Long> ids = entities.stream()
                    .map(ModelConfigEntity::getId)
                    .collect(Collectors.toList());
            
            int deletedCount = modelConfigMapper.deleteBatchIds(ids);
            
            log.info("成功删除提供商: {}，删除了 {} 个模型", provider, deletedCount);
            return true;
        } catch (Exception e) {
            log.error("删除提供商失败: {}", provider, e);
            return false;
        }
    }

    @Override
    @Transactional
    public Boolean reloadConfig() {
        try {
            // 清空数据库中的所有配置
            List<ModelConfigEntity> allEntities = modelConfigMapper.selectList(null);
            if (!allEntities.isEmpty()) {
                List<Long> ids = allEntities.stream()
                        .map(ModelConfigEntity::getId)
                        .collect(Collectors.toList());
                modelConfigMapper.deleteBatchIds(ids);
            }
            
            // 重新从YAML配置文件导入
            initializeModelConfigFromYaml();
            
            log.info("成功重新加载模型配置");
            return true;
        } catch (Exception e) {
            log.error("重新加载配置失败", e);
            return false;
        }
    }

    @Override
    public ModelConfig getModelConfig() {
        try {
            ModelConfig config = new ModelConfig();
            Map<String, ModelConfig.ProviderConfig> providers = new HashMap<>();
            
            List<String> providerNames = modelConfigMapper.findAllProviders();
            for (String provider : providerNames) {
                List<ModelConfigEntity> entities = modelConfigMapper.findByProvider(provider);
                List<ModelConfig.ModelConfigItem> items = entities.stream()
                        .map(ModelConfigConverter::entityToConfigItem)
                        .collect(Collectors.toList());
                
                // 创建ProviderConfig
                ModelConfig.ProviderConfig providerConfig = new ModelConfig.ProviderConfig();
                providerConfig.setEnabled(true); // 从数据库读取的都认为是启用的
                providerConfig.setModels(items);
                
                providers.put(provider, providerConfig);
            }
            
            config.setProviders(providers);
            return config;
        } catch (Exception e) {
            log.error("获取模型配置失败", e);
            return new ModelConfig();
        }
    }
} 