package com.etrx.kb.util;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.etrx.kb.domain.User;
import com.etrx.kb.exception.ApiException;
import com.etrx.kb.mapper.UserMapper;
import com.etrx.kb.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 安全工具类
 */
@Component
public class SecurityUtils {

    private final UserMapper userMapper;
    private final UserService userService;
    private final JwtTokenUtil jwtTokenUtil;

    public SecurityUtils(UserMapper userMapper,
                        @Lazy UserService userService,
                        JwtTokenUtil jwtTokenUtil) {
        this.userMapper = userMapper;
        this.userService = userService;
        this.jwtTokenUtil = jwtTokenUtil;
    }

    /**
     * 获取当前用户ID
     *
     * @return 当前用户ID
     */
    public Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            throw new ApiException("用户未登录");
        }

        String username = authentication.getName();
        if ("anonymousUser".equals(username)) {
            throw new ApiException("用户未登录");
        }

        // 根据用户名查询用户ID
        User user = userMapper.selectOne(new LambdaQueryWrapper<User>()
                .eq(User::getUsername, username));
        if (user == null) {
            throw new ApiException("用户不存在");
        }

        return user.getId();
    }

    /**
     * 获取当前用户名
     *
     * @return 当前用户名
     */
    public String getCurrentUsername() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            throw new ApiException("用户未登录");
        }

        String username = authentication.getName();
        if ("anonymousUser".equals(username)) {
            throw new ApiException("用户未登录");
        }

        return username;
    }

    /**
     * 获取当前用户信息
     *
     * @return 当前用户信息
     */
    public User getCurrentUser() {
        // 1. 检查是否为API密钥认证
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            Boolean isApiKeyAuth = (Boolean) request.getAttribute("API_KEY_AUTH");
            if (isApiKeyAuth != null && isApiKeyAuth) {
                // API密钥认证，从认证用户获取
                String username = getCurrentUsername();
                User user = userMapper.selectOne(new LambdaQueryWrapper<User>()
                        .eq(User::getUsername, username));
                if (user == null) {
                    throw new ApiException("用户不存在");
                }
                // 设置应用名称
                String apiKey = (String) request.getAttribute("API_KEY");
                if (apiKey != null) {
                    // 这里可以通过ApiKeyService获取应用名称，但为了避免循环依赖先简化处理
                    user.setAppName("ThirdPartyApp");
                }
                return user;
            }
        }
        
        // 2. 检查是否为第三方JWT token
        String token = getTokenFromRequest();
        if (token != null && jwtTokenUtil.isThirdPartyToken(token)) {
            // 第三方token，根据工号获取用户
            String employeeNo = jwtTokenUtil.getEmployeeNoFromToken(token);
            User user = userService.getUserByEmployeeNo(employeeNo);
            if (user == null) {
                throw new ApiException("第三方用户不存在");
            }
            // 设置来源应用名称（非数据库字段）
            String appName = jwtTokenUtil.getAppNameFromToken(token);
            user.setAppName(appName);
            return user;
        } else {
            // 3. 本系统token，使用原逻辑
            String username = getCurrentUsername();
            User user = userMapper.selectOne(new LambdaQueryWrapper<User>()
                    .eq(User::getUsername, username));
            if (user == null) {
                throw new ApiException("用户不存在");
            }
            // 本系统用户appName为null
            user.setAppName(null);
            return user;
        }
    }

    /**
     * 从HTTP请求中获取token
     */
    private String getTokenFromRequest() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                String bearerToken = request.getHeader("Authorization");
                if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
                    return bearerToken.substring(7);
                }
            }
        } catch (Exception e) {
            // 忽略异常，返回null
        }
        return null;
    }

    /**
     * 获取当前用户的来源APP名称（仅第三方用户有值）
     *
     * @return APP名称，本系统用户返回null
     */
    public String getCurrentUserAppName() {
        // 1. 检查是否为API密钥认证
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            Boolean isApiKeyAuth = (Boolean) request.getAttribute("API_KEY_AUTH");
            if (isApiKeyAuth != null && isApiKeyAuth) {
                return "ThirdPartyApp"; // 简化处理
            }
        }
        
        // 2. 检查是否为第三方JWT token
        String token = getTokenFromRequest();
        if (token != null && jwtTokenUtil.isThirdPartyToken(token)) {
            return jwtTokenUtil.getAppNameFromToken(token);
        }
        return null;
    }

    /**
     * 判断当前用户是否为第三方用户
     *
     * @return 是否为第三方用户
     */
    public boolean isThirdPartyUser() {
        // 1. 检查是否为API密钥认证
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            Boolean isApiKeyAuth = (Boolean) request.getAttribute("API_KEY_AUTH");
            if (isApiKeyAuth != null && isApiKeyAuth) {
                return true;
            }
        }
        
        // 2. 检查是否为第三方JWT token
        String token = getTokenFromRequest();
        return token != null && jwtTokenUtil.isThirdPartyToken(token);
    }

    /**
     * 检查当前用户是否为管理员
     *
     * @return 是否为管理员
     */
    public boolean isAdmin() {
        try {
            User user = getCurrentUser();
            return user.getRole() >= 2; // 2=管理员, 3=超级管理员
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查当前用户是否为超级管理员
     *
     * @return 是否为超级管理员
     */
    public boolean isSuperAdmin() {
        try {
            User user = getCurrentUser();
            return user.getRole() == 3; // 3=超级管理员
        } catch (Exception e) {
            return false;
        }
    }
} 