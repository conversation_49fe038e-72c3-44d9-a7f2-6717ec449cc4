package com.etrx.kb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.etrx.kb.domain.TaskQueue;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 任务队列Mapper接口
 */
@Mapper
public interface TaskQueueMapper extends BaseMapper<TaskQueue> {
    
    /**
     * 根据主机数量和序列号查询待执行任务
     * @param hostCount 主机数量
     * @param sequenceNumber 序列号
     * @param limit 限制数量
     * @return 任务列表
     */
    List<TaskQueue> selectPendingTasksByModulo(@Param("hostCount") int hostCount, 
                                              @Param("sequenceNumber") long sequenceNumber, 
                                              @Param("limit") int limit);
} 