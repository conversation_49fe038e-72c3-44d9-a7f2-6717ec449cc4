package com.etrx.kb.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 聊天助手映射实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ChatAssistantMapping {

    /**
     * ID
     */
    private Long id;

    /**
     * 平台用户ID
     */
    private Long userId;

    /**
     * RAGflow聊天助手ID
     */
    private String ragflowChatAssistantId;

    /**
     * 聊天助手名称
     */
    private String assistantName;

    /**
     * 助手图标（Base64编码）
     */
    private String icon;

    /**
     * RAGflow知识库ID列表(JSON)
     */
    private String ragflowKnowledgeBaseIds;

    /**
     * 本地知识库ID列表(JSON)
     */
    private String localKnowledgeBaseIds;

    /**
     * 助手类型: 1-RAGflow知识库, 2-本地知识库
     */
    private Integer assistantType;

    /**
     * 状态: 0-禁用, 1-启用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 助手类型枚举
     */
    public enum AssistantType {
        RAGFLOW_KB(1, "RAGflow知识库"),
        LOCAL_KB(2, "本地知识库");

        private final Integer code;
        private final String description;

        AssistantType(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 状态枚举
     */
    public enum Status {
        DISABLED(0, "禁用"),
        ENABLED(1, "启用");

        private final Integer code;
        private final String description;

        Status(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
} 