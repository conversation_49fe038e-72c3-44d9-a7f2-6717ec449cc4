package com.etrx.kb.exception;

/**
 * 第三方访问被拒绝异常
 * 专门用于第三方白名单控制
 */
public class ThirdPartyAccessDeniedException extends RuntimeException {
    
    private final String methodName;
    private final String httpMethod;
    
    public ThirdPartyAccessDeniedException(String methodName, String httpMethod) {
        super("Access denied: Method not in whitelist - " + methodName + " [" + httpMethod + "]");
        this.methodName = methodName;
        this.httpMethod = httpMethod;
    }
    
    public ThirdPartyAccessDeniedException(String methodName) {
        super("Access denied: Method not in whitelist - " + methodName);
        this.methodName = methodName;
        this.httpMethod = null;
    }
    
    public String getMethodName() {
        return methodName;
    }
    
    public String getHttpMethod() {
        return httpMethod;
    }
} 