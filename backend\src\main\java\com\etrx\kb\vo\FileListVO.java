package com.etrx.kb.vo;

import com.etrx.kb.domain.FileNode;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 文件列表VO，包含分页信息和文件节点列表
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FileListVO extends PageVO {
    /**
     * 文件节点列表
     */
    private List<FileNode> records;

    private Boolean isNonKbDirectory;

    /**
     * 构建FileListVO对象
     *
     * @param total 总记录数
     * @param current 当前页码
     * @param size 每页大小
     * @param records 文件节点列表
     * @return FileListVO对象
     */
    public static FileListVO build(Long total, Long current, Long size, List<FileNode> records, Boolean isNonKbDirectory) {
        FileListVO vo = new FileListVO();
        vo.setTotal(total);
        vo.setCurrent(current);
        vo.setSize(size);
        vo.setPages((total + size - 1) / size);
        vo.setRecords(records);
        vo.setIsNonKbDirectory(isNonKbDirectory);
        return vo;
    }
} 