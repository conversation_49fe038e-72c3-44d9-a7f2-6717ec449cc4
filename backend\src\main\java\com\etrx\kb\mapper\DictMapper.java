package com.etrx.kb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.etrx.kb.domain.Dict;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 数据字典Mapper接口
 */
@Mapper
public interface DictMapper extends BaseMapper<Dict> {

    /**
     * 根据字典类型查询启用的配置项
     */
    @Select("SELECT * FROM tb_dict WHERE dict_type = #{dictType} AND status = 1 AND deleted = 0 ORDER BY sort_order ASC, id ASC")
    List<Dict> selectEnabledConfigsByType(@Param("dictType") String dictType);

    /**
     * 根据字典类型查询所有节点
     */
    @Select("SELECT * FROM tb_dict WHERE dict_type = #{dictType} AND deleted = 0 ORDER BY sort_order ASC, id ASC")
    List<Dict> selectAllByType(@Param("dictType") String dictType);
    
    /**
     * 根据字典键查询启用的配置项
     */
    @Select("SELECT * FROM tb_dict WHERE dict_key = #{dictKey} AND status = 1 AND deleted = 0 LIMIT 1")
    Dict selectEnabledByKey(@Param("dictKey") String dictKey);
} 