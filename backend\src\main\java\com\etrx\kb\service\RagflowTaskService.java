package com.etrx.kb.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.etrx.kb.annotation.TaskDefinition;
import com.etrx.kb.common.Constants;
import com.etrx.kb.domain.KbFileRel;
import com.etrx.kb.exception.ApiException;
import com.etrx.kb.mapper.KbFileRelMapper;
import com.etrx.kb.service.FileService;
import com.etrx.kb.service.TaskExecutionService;
import com.etrx.kb.service.model.UploadToRagflowTaskData;
import com.etrx.kb.service.model.DeleteFromRagflowTaskData;
import com.etrx.kb.util.RagflowUtils;
import com.etrx.kb.vo.RagflowUploadVO;
import com.etrx.kb.vo.RagflowDocsListVO;
import erd.cloud.ai.ragflow.RagflowFileClient;
import com.fasterxml.jackson.databind.ObjectMapper;
import erd.cloud.ai.ragflow.RagflowFileClientFactory;
import io.minio.GetObjectArgs;
import io.minio.GetObjectResponse;
import io.minio.MinioClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Ragflow任务服务
 * 包含所有与Ragflow相关的异步任务，只处理ragflow API调用
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RagflowTaskService {

    private final ObjectMapper objectMapper;
    private final RagflowFileClient ragClient = RagflowFileClientFactory.getClient();
    private final MinioClient minioClient;
    private final FileService fileService;
    private final KbFileRelMapper kbFileRelMapper;
    private final TaskExecutionService taskExecutionService;

    /**
     * 上传文件到Ragflow
     * @param businessData 业务数据JSON
     * @return 处理结果
     */
    @TaskDefinition(
            taskName = "uploadToRagflow",
            taskType = "RAGFLOW",
            threadCount = 3,
            maxExecutionCount = 1000,
            maxRetryCount = 3,
            description = "将文件上传到Ragflow服务"
    )
    public String uploadToRagflow(String businessData) {
        File tempDir = null;
        File tempFile = null;
        try {
            log.info("开始执行文件上传到Ragflow任务，业务数据: {}", businessData);
            
            // 解析业务数据
            UploadToRagflowTaskData taskData = objectMapper.readValue(businessData, UploadToRagflowTaskData.class);
            
            // 创建临时目录
            String tempDirPrefix = "upload_" + UUID.randomUUID().toString().replace("-", "");
            tempDir = new File(System.getProperty("java.io.tmpdir"), tempDirPrefix);
            if (!tempDir.mkdir()) {
                throw new RuntimeException("创建临时目录失败");
            }

            // 从MinIO下载文件
            tempFile = new File(tempDir, taskData.getFileName());

            // 确保父目录存在（处理文件夹上传的情况）
            File parentDir = tempFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                if (!parentDir.mkdirs()) {
                    throw new RuntimeException("创建临时文件目录失败: " + parentDir.getAbsolutePath());
                }
                log.info("创建临时文件目录: {}", parentDir.getAbsolutePath());
            }

            long minioDownloadStartTime = System.nanoTime();
            log.info("开始从MinIO下载文件，存储路径: {}, 临时文件路径: {}",
                taskData.getStoragePath(), tempFile.getAbsolutePath());
            
            GetObjectResponse response = minioClient.getObject(
                GetObjectArgs.builder()
                    .bucket(taskData.getBucketName())
                    .object(taskData.getStoragePath())
                    .build()
            );
            
            Files.copy(response, tempFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
            
            long minioDownloadEndTime = System.nanoTime();
            long minioDownloadDuration = (minioDownloadEndTime - minioDownloadStartTime) / 1_000_000;
            log.info("从MinIO下载文件完成，文件大小: {}字节，耗时: {}ms", 
                tempFile.length(), minioDownloadDuration);

            // 上传到Ragflow
            String res = ragClient.uploadDocuments(taskData.getDatasetId(), List.of(tempFile));

            log.info("文件上传到Ragflow完成，知识库ID: {}，文件ID: {}，结果: {}",
                taskData.getKbId(), taskData.getFileNodeId(), res);

            try {
                RagflowUtils.handleResponse(res, "上传文档失败");
            } catch (ApiException e) {
                // 将ApiException转换为RuntimeException，确保任务执行框架能正确捕获和记录
                log.error("Ragflow返回错误，知识库ID: {}，文件ID: {}，错误: {}",
                        taskData.getKbId(), taskData.getFileNodeId(), e.getMessage());
                throw new RuntimeException("上传文档到Ragflow失败: " + e.getMessage(), e);
            }

            ObjectMapper mapper = new ObjectMapper();
            RagflowUploadVO uploadResult = mapper.readValue(res, RagflowUploadVO.class);
            RagflowUploadVO.UploadFileVO fileInfo = uploadResult.getData().get(0);
            // 更新关联表中的文档信息
            LambdaQueryWrapper<KbFileRel> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(KbFileRel::getId, taskData.getRelId());
            KbFileRel rel = kbFileRelMapper.selectOne(wrapper);
            rel.setDocumentId(fileInfo.getId());
            rel.setDocumentStatus(Constants.DocumentStatus.READY);
            // 保存整个data数组信息
            String dataJson = mapper.writeValueAsString(uploadResult.getData());
            rel.setDocumentInfo(dataJson);
            kbFileRelMapper.updateById(rel);

            // 上传成功后，创建解析任务
            processAutoParseFile(taskData, fileInfo, mapper);

            return res;
            
        } catch (Exception e) {
            log.error("上传文件到Ragflow失败，业务数据: {}", businessData, e);

            // 尝试解析业务数据以获取更多上下文信息并更新状态
            try {
                UploadToRagflowTaskData taskData = objectMapper.readValue(businessData, UploadToRagflowTaskData.class);
                log.error("上传失败详情 - 知识库ID: {}, 文件ID: {}, 文件名: {}, 存储路径: {}",
                        taskData.getKbId(), taskData.getFileNodeId(), taskData.getFileName(), taskData.getStoragePath());

                // 更新关联表中的文档状态为上传失败
                updateDocumentStatusOnUploadFailure(taskData, e);

            } catch (Exception parseEx) {
                log.error("解析业务数据失败", parseEx);
            }

            throw new RuntimeException("上传文件到Ragflow失败: " + e.getMessage(), e);
        } finally {
            // 清理临时文件
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
            if (tempDir != null && tempDir.exists()) {
                tempDir.delete();
            }
        }
    }

    private void processAutoParseFile(UploadToRagflowTaskData taskData, RagflowUploadVO.UploadFileVO fileInfo, ObjectMapper mapper) {
        try {
            ParseFileTaskData parseTaskData = new ParseFileTaskData();
            parseTaskData.setKbId(taskData.getKbId());
            parseTaskData.setFileIds(List.of(taskData.getFileNodeId()));
            parseTaskData.setDatasetId(taskData.getDatasetId());
            parseTaskData.setDocumentIds(List.of(fileInfo.getId()));
            parseTaskData.setUserId(taskData.getUserId());
            parseTaskData.setKbName(String.valueOf(taskData.getKbId())); // 使用知识库ID作为名称

            String parseBusinessData = mapper.writeValueAsString(parseTaskData);

            log.info("文件上传成功，准备创建解析任务，知识库ID: {}, 文件ID: {}, 文档ID: {}",
                    taskData.getKbId(), taskData.getFileNodeId(), fileInfo.getId());

            // 提交解析任务
            taskExecutionService.executeTaskByName("parseFileInRagflow", parseBusinessData, taskData.getUserId());

        } catch (Exception e) {
            log.warn("创建解析任务失败，知识库ID: {}, 文件ID: {}, 文档ID: {}",
                    taskData.getKbId(), taskData.getFileNodeId(), fileInfo.getId(), e);
        }
    }

    /**
     * 更新文档状态为上传失败
     * @param taskData 任务数据
     * @param exception 异常信息
     */
    private void updateDocumentStatusOnUploadFailure(UploadToRagflowTaskData taskData, Exception exception) {
        try {
            LambdaQueryWrapper<KbFileRel> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(KbFileRel::getId, taskData.getRelId());
            KbFileRel rel = kbFileRelMapper.selectOne(wrapper);

            if (rel != null) {
                // 设置状态为上传失败
                rel.setDocumentStatus(Constants.DocumentStatus.UPLOAD_FAILED);

                // 构建错误信息JSON
                Map<String, Object> errorInfo = new HashMap<>();
                errorInfo.put("error", true);
                errorInfo.put("errorType", "UPLOAD_FAILED");
                errorInfo.put("errorMessage", exception.getMessage());
                errorInfo.put("timestamp", System.currentTimeMillis());

                // 如果是ApiException，提取更详细的错误信息
                if (exception.getCause() instanceof ApiException) {
                    ApiException apiEx = (ApiException) exception.getCause();
                    errorInfo.put("ragflowError", apiEx.getMessage());
                }

                String errorJson = objectMapper.writeValueAsString(errorInfo);
                rel.setDocumentInfo(errorJson);

                kbFileRelMapper.updateById(rel);

                log.info("已更新文档状态为上传失败，关联ID: {}, 文件ID: {}, 知识库ID: {}",
                        taskData.getRelId(), taskData.getFileNodeId(), taskData.getKbId());
            } else {
                log.warn("未找到关联记录，无法更新状态，关联ID: {}", taskData.getRelId());
            }
        } catch (Exception e) {
            log.error("更新文档上传失败状态时发生异常，关联ID: {}", taskData.getRelId(), e);
        }
    }

    /**
     * 从Ragflow删除文档
     * @param businessData 业务数据JSON
     * @return 处理结果
     */
    @TaskDefinition(
            taskName = "deleteFromRagflow",
            taskType = "RAGFLOW",
            threadCount = 3,
            maxExecutionCount = 1000,
            maxRetryCount = 3,
            description = "从Ragflow服务删除文档"
    )
    public String deleteFromRagflow(String businessData) {
        try {
            log.info("开始执行从Ragflow删除文档任务，业务数据: {}", businessData);

            // 解析业务数据
            DeleteFromRagflowTaskData taskData = objectMapper.readValue(businessData, DeleteFromRagflowTaskData.class);

            // 先检查文档是否存在于ragflow中
            if (checkDocumentExistsInRagflow(taskData.getDatasetId(), taskData.getDocumentId())) {
                // 从Ragflow删除文档
                String result = ragClient.deleteDocuments(taskData.getDatasetId(), List.of(taskData.getDocumentId()));

                log.info("成功从Ragflow删除文档，知识库ID: {}，文件ID: {}，文档ID: {}，结果: {}",
                    taskData.getKbId(), taskData.getFileNodeId(), taskData.getDocumentId(), result);

                RagflowUtils.handleResponse(result, "删除文档失败:");

                return result;
            } else {
                log.warn("文档在Ragflow中不存在，跳过删除，知识库ID: {}，文件ID: {}，文档ID: {}",
                        taskData.getKbId(), taskData.getFileNodeId(), taskData.getDocumentId());
                return "文档不存在，跳过删除";
            }

        } catch (Exception e) {
            log.error("从Ragflow删除文档失败", e);
            throw new RuntimeException("从Ragflow删除文档失败: " + e.getMessage());
        }
    }

    /**
     * 检查文档是否存在于Ragflow中
     * @param datasetId 数据集ID
     * @param documentId 文档ID
     * @return true如果文档存在，false如果不存在
     */
    private boolean checkDocumentExistsInRagflow(String datasetId, String documentId) {
        try {
            // 通过查询文档列表，查找特定ID的文档
            Map<String, String> params = new HashMap<>();
            params.put("id", documentId);

            String result = ragClient.listDocuments(datasetId, params);

            ObjectMapper mapper = new ObjectMapper();
            RagflowDocsListVO response = mapper.readValue(result, RagflowDocsListVO.class);

            if (response.getCode() == 0 && response.getData() != null) {
                List<RagflowDocsListVO.UploadFileVO> docs = response.getData().getDocs();
                return docs != null && docs.stream().anyMatch(doc -> documentId.equals(doc.getId()));
            }

            return false;
        } catch (Exception e) {
            log.warn("检查文档是否存在于Ragflow失败, datasetId: {}, documentId: {}", datasetId, documentId, e);
            // 发生异常时为了安全起见，假设文档存在，尝试删除
            return true;
        }
    }

    /**
     * 在Ragflow中解析文件
     * @param businessData 业务数据JSON
     * @return 处理结果
     */
    @TaskDefinition(
            taskName = "parseFileInRagflow",
            taskType = "RAGFLOW",
            threadCount = 3,
            maxExecutionCount = 1000,
            maxRetryCount = 3,
            description = "在Ragflow中解析文件"
    )
    public String parseFileInRagflow(String businessData) {
        try {
            log.info("开始执行解析文件任务，业务数据: {}", businessData);
            
            // 解析业务数据
            ParseFileTaskData taskData = objectMapper.readValue(businessData, ParseFileTaskData.class);
            
            // 调用Ragflow解析文件
            String result = ragClient.parseDocuments(taskData.getDatasetId(), taskData.getDocumentIds());
            
            // 处理返回结果
            RagflowUtils.handleResponse(result, "解析文件失败");
            
            // 调用完成处理
            fileService.completeParseFiles(taskData.getKbId(), taskData.getFileIds(), taskData.getUserId(), true);
            
            log.info("解析文件任务完成，知识库: {}, 文件数量: {}", 
                    taskData.getKbName(), taskData.getFileIds().size());
            
            return "解析成功";
            
        } catch (Exception e) {
            log.error("解析文件任务失败", e);
            
            try {
                // 尝试解析业务数据进行失败处理
                ParseFileTaskData taskData = objectMapper.readValue(businessData, ParseFileTaskData.class);
                fileService.completeParseFiles(taskData.getKbId(), taskData.getFileIds(), taskData.getUserId(), false);
            } catch (Exception ex) {
                log.error("解析文件任务失败处理时出错", ex);
            }
            
            throw new RuntimeException("解析文件任务失败: " + e.getMessage());
        }
    }

    /**
     * 在Ragflow中停止解析文件
     * @param businessData 业务数据JSON
     * @return 处理结果
     */
    @TaskDefinition(
            taskName = "stopParsingFileInRagflow",
            taskType = "RAGFLOW",
            threadCount = 3,
            maxExecutionCount = 1000,
            maxRetryCount = 3,
            description = "在Ragflow中停止解析文件"
    )
    public String stopParsingFileInRagflow(String businessData) {
        try {
            log.info("开始执行停止解析文件任务，业务数据: {}", businessData);
            
            // 解析业务数据
            StopParsingFileTaskData taskData = objectMapper.readValue(businessData, StopParsingFileTaskData.class);
            
            // 调用Ragflow停止解析
            String result = ragClient.stopParsingDocuments(taskData.getDatasetId(), taskData.getDocumentIds());
            
            // 处理返回结果
            RagflowUtils.handleResponse(result, "终止文件解析失败");
            
            // 调用完成处理
            fileService.completeStopParsingFiles(taskData.getKbId(), taskData.getFileIds(), taskData.getUserId(), true);
            
            log.info("停止解析文件任务完成，知识库: {}, 文件数量: {}", 
                    taskData.getKbName(), taskData.getFileIds().size());
            
            return "停止解析成功";
            
        } catch (Exception e) {
            log.error("停止解析文件任务失败", e);
            
            try {
                StopParsingFileTaskData taskData = objectMapper.readValue(businessData, StopParsingFileTaskData.class);
                fileService.completeStopParsingFiles(taskData.getKbId(), taskData.getFileIds(), taskData.getUserId(), false);
            } catch (Exception ex) {
                log.error("停止解析文件任务失败处理时出错", ex);
            }
            
            throw new RuntimeException("停止解析文件任务失败: " + e.getMessage());
        }
    }

    /**
     * 在Ragflow中更新文档
     * @param businessData 业务数据JSON
     * @return 处理结果
     */
    @TaskDefinition(
            taskName = "updateDocumentInRagflow",
            taskType = "RAGFLOW",
            threadCount = 3,
            maxExecutionCount = 1000,
            maxRetryCount = 3,
            description = "在Ragflow中更新文档"
    )
    public String updateDocumentInRagflow(String businessData) {
        try {
            log.info("开始执行更新文档任务，业务数据: {}", businessData);
            
            // 解析业务数据
            UpdateDocumentTaskData taskData = objectMapper.readValue(businessData, UpdateDocumentTaskData.class);
            
            // 调用Ragflow API更新文档
            String response = ragClient.updateDocument(
                taskData.getDatasetId(),
                taskData.getDocumentId(),
                taskData.getUpdateFields()
            );

            // 处理Ragflow返回结果
            RagflowUtils.handleResponse(response, "更新文档元数据失败");

            // 查询更新后的文档信息
            Map<String, String> params = new HashMap<>();
            params.put("id", taskData.getDocumentId());
            String res = ragClient.listDocuments(taskData.getDatasetId(), params);
            log.info("更新文档元数据后，查询文档信息: {}", res);
            
            // 调用完成处理
            fileService.completeUpdateDocument(taskData.getKbId(), taskData.getFileId(), taskData.getUpdateVO(), taskData.getUserId(), true);
            
            log.info("更新文档任务完成，文件: {}", taskData.getFileName());
            
            return "更新成功";
            
        } catch (Exception e) {
            log.error("更新文档任务失败", e);
            
            try {
                UpdateDocumentTaskData taskData = objectMapper.readValue(businessData, UpdateDocumentTaskData.class);
                fileService.completeUpdateDocument(taskData.getKbId(), taskData.getFileId(), taskData.getUpdateVO(), taskData.getUserId(), false);
            } catch (Exception ex) {
                log.error("更新文档任务失败处理时出错", ex);
            }
            
            throw new RuntimeException("更新文档任务失败: " + e.getMessage());
        }
    }

    /**
     * 在Ragflow中批量更新文档状态
     * @param businessData 业务数据JSON
     * @return 处理结果
     */
    @TaskDefinition(
            taskName = "batchUpdateDocumentStatusInRagflow",
            taskType = "RAGFLOW",
            threadCount = 2,
            maxExecutionCount = 1000,
            maxRetryCount = 3,
            description = "在Ragflow中批量更新文档状态"
    )
    public String batchUpdateDocumentStatusInRagflow(String businessData) {
        try {
            log.info("开始执行批量更新文档状态任务，业务数据: {}", businessData);
            
            // 解析业务数据
            BatchUpdateStatusTaskData taskData = objectMapper.readValue(businessData, BatchUpdateStatusTaskData.class);
            
            // 批量更新状态
            int successCount = 0;
            int failCount = 0;
            
            for (String documentId : taskData.getDocumentIds()) {
                try {
                    // 构建更新字段，只更新status
                    Map<String, Object> updateFields = new HashMap<>();
                    updateFields.put("status", taskData.getStatus());

                    // 调用Ragflow API更新文档状态
                    String response = ragClient.updateDocument(
                        taskData.getDatasetId(),
                        documentId,
                        updateFields
                    );

                    // 处理Ragflow返回结果
                    RagflowUtils.handleResponse(response, "更新文档状态失败");

                    // 查询更新后的文档信息
                    Map<String, String> params = new HashMap<>();
                    params.put("id", documentId);
                    String res = ragClient.listDocuments(taskData.getDatasetId(), params);
                    log.info("批量更新文档状态后，查询文档信息: {}", res);
                    
                    successCount++;
                    
                } catch (Exception e) {
                    failCount++;
                    log.error("批量更新文档状态失败，documentId: {}, error: {}", documentId, e.getMessage());
                }
            }
            
            // 调用完成处理
            boolean success = failCount == 0;
            fileService.completeBatchUpdateDocumentStatus(taskData.getKbId(), taskData.getFileIds(), taskData.getStatus(), taskData.getUserId(), success);
            
            log.info("批量更新文档状态任务完成，知识库: {}, 成功: {}, 失败: {}", 
                    taskData.getKbName(), successCount, failCount);
            
            if (failCount > 0) {
                throw new RuntimeException("部分文档状态更新失败，失败数量: " + failCount);
            }
            
            return "批量更新成功";
            
        } catch (Exception e) {
            log.error("批量更新文档状态任务失败", e);
            
            try {
                BatchUpdateStatusTaskData taskData = objectMapper.readValue(businessData, BatchUpdateStatusTaskData.class);
                fileService.completeBatchUpdateDocumentStatus(taskData.getKbId(), taskData.getFileIds(), taskData.getStatus(), taskData.getUserId(), false);
            } catch (Exception ex) {
                log.error("批量更新文档状态任务失败处理时出错", ex);
            }
            
            throw new RuntimeException("批量更新文档状态任务失败: " + e.getMessage());
        }
    }

    // 任务数据类定义
    
    public static class ParseFileTaskData {
        private Long kbId;
        private List<Long> fileIds;
        private String datasetId;
        private List<String> documentIds;
        private String kbName;
        private Long userId;
        
        // getters and setters
        public Long getKbId() { return kbId; }
        public void setKbId(Long kbId) { this.kbId = kbId; }
        public List<Long> getFileIds() { return fileIds; }
        public void setFileIds(List<Long> fileIds) { this.fileIds = fileIds; }
        public String getDatasetId() { return datasetId; }
        public void setDatasetId(String datasetId) { this.datasetId = datasetId; }
        public List<String> getDocumentIds() { return documentIds; }
        public void setDocumentIds(List<String> documentIds) { this.documentIds = documentIds; }
        public String getKbName() { return kbName; }
        public void setKbName(String kbName) { this.kbName = kbName; }
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
    }
    
    public static class StopParsingFileTaskData {
        private Long kbId;
        private List<Long> fileIds;
        private String datasetId;
        private List<String> documentIds;
        private String kbName;
        private Long userId;
        
        // getters and setters
        public Long getKbId() { return kbId; }
        public void setKbId(Long kbId) { this.kbId = kbId; }
        public List<Long> getFileIds() { return fileIds; }
        public void setFileIds(List<Long> fileIds) { this.fileIds = fileIds; }
        public String getDatasetId() { return datasetId; }
        public void setDatasetId(String datasetId) { this.datasetId = datasetId; }
        public List<String> getDocumentIds() { return documentIds; }
        public void setDocumentIds(List<String> documentIds) { this.documentIds = documentIds; }
        public String getKbName() { return kbName; }
        public void setKbName(String kbName) { this.kbName = kbName; }
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
    }
    
    public static class UpdateDocumentTaskData {
        private Long kbId;
        private Long fileId;
        private String datasetId;
        private String documentId;
        private String fileName;
        private Map<String, Object> updateFields;
        private com.etrx.kb.vo.RagflowUploadVO.UploadFileVO updateVO;
        private Long userId;
        
        // getters and setters
        public Long getKbId() { return kbId; }
        public void setKbId(Long kbId) { this.kbId = kbId; }
        public Long getFileId() { return fileId; }
        public void setFileId(Long fileId) { this.fileId = fileId; }
        public String getDatasetId() { return datasetId; }
        public void setDatasetId(String datasetId) { this.datasetId = datasetId; }
        public String getDocumentId() { return documentId; }
        public void setDocumentId(String documentId) { this.documentId = documentId; }
        public String getFileName() { return fileName; }
        public void setFileName(String fileName) { this.fileName = fileName; }
        public Map<String, Object> getUpdateFields() { return updateFields; }
        public void setUpdateFields(Map<String, Object> updateFields) { this.updateFields = updateFields; }
        public com.etrx.kb.vo.RagflowUploadVO.UploadFileVO getUpdateVO() { return updateVO; }
        public void setUpdateVO(com.etrx.kb.vo.RagflowUploadVO.UploadFileVO updateVO) { this.updateVO = updateVO; }
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
    }
    
    public static class BatchUpdateStatusTaskData {
        private Long kbId;
        private List<Long> fileIds;
        private String datasetId;
        private List<String> documentIds;
        private String status;
        private String kbName;
        private Long userId;
        
        // getters and setters
        public Long getKbId() { return kbId; }
        public void setKbId(Long kbId) { this.kbId = kbId; }
        public List<Long> getFileIds() { return fileIds; }
        public void setFileIds(List<Long> fileIds) { this.fileIds = fileIds; }
        public String getDatasetId() { return datasetId; }
        public void setDatasetId(String datasetId) { this.datasetId = datasetId; }
        public List<String> getDocumentIds() { return documentIds; }
        public void setDocumentIds(List<String> documentIds) { this.documentIds = documentIds; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public String getKbName() { return kbName; }
        public void setKbName(String kbName) { this.kbName = kbName; }
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
    }
}