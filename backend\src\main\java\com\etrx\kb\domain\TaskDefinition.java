package com.etrx.kb.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 任务定义实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("tb_task_definition")
public class TaskDefinition implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务类型
     */
    private String taskType;

    /**
     * 执行类路径
     */
    private String classPath;

    /**
     * 方法名
     */
    private String methodName;

    /**
     * 执行线程数
     */
    private Integer threadCount;

    /**
     * 最大执行个数
     */
    private Integer maxExecutionCount;

    /**
     * 最大重试次数
     */
    private Integer maxRetryCount;

    /**
     * 任务描述
     */
    private String description;

    /**
     * 状态(0:禁用,1:启用)
     */
    private Integer status;

    /**
     * 逻辑删除(0:未删除,1:已删除)
     */
    @TableLogic
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 状态枚举
     */
    public static class Status {
        public static final int DISABLED = 0; // 禁用
        public static final int ENABLED = 1; // 启用
    }
} 