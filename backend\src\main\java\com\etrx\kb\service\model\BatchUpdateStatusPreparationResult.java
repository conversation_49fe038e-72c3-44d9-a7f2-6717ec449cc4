package com.etrx.kb.service.model;

import java.util.List;

public class BatchUpdateStatusPreparationResult {
    private String datasetId;
    private List<String> documentIds;
    private String status;
    private String kbName;

    public BatchUpdateStatusPreparationResult(String datasetId, List<String> documentIds, String status, String kbName) {
        this.datasetId = datasetId;
        this.documentIds = documentIds;
        this.status = status;
        this.kbName = kbName;
    }

    public String getDatasetId() { return datasetId; }
    public List<String> getDocumentIds() { return documentIds; }
    public String getStatus() { return status; }
    public String getKbName() { return kbName; }
}