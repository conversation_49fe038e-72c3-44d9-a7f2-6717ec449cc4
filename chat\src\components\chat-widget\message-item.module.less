.messageItem {
  padding: 24px 0;
  
  .messageItemSection {
    display: inline-block;
  }
  
  .messageItemSectionLeft {
    width: 80%;
  }
  
  .messageItemContent {
    display: inline-flex;
    gap: 20px;
  }
  
  .messageItemContentReverse {
    flex-direction: row-reverse;
  }

  .messageTextBase() {
    padding: 6px 10px;
    border-radius: 8px;
    & > p {
      margin: 0;
    }
  }
  
  .messageText {
    .messageTextBase();
    background-color: #e6f4ff;
    word-break: break-word;
  }

  .messageUserText {
    .messageTextBase();
    background-color: rgba(255, 255, 255, 0.3);
    word-break: break-word;
    text-align: justify;
  }
}

.messageItemLeft {
  text-align: left;
}

.messageItemRight {
  text-align: right;
} 