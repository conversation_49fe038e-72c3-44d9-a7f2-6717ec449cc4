package com.etrx.kb.service.impl;

import com.etrx.kb.domain.Dict;
import com.etrx.kb.service.DictService;
import com.etrx.kb.service.ThirdPartyWhitelistService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import jakarta.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 第三方API白名单服务实现（支持方法级别精确控制）
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ThirdPartyWhitelistServiceImpl implements ThirdPartyWhitelistService {

    private final DictService dictService;

    /**
     * 精确匹配方法缓存（类名.方法名）
     */
    private final Set<String> whitelistExactMethods = ConcurrentHashMap.newKeySet();

    /**
     * 类通配符匹配方法缓存（类名.*）
     */
    private final Set<String> whitelistClassWildcards = ConcurrentHashMap.newKeySet();

    /**
     * 包通配符匹配方法缓存（包名.*）
     */
    private final Set<String> whitelistPackageWildcards = ConcurrentHashMap.newKeySet();

    /**
     * 配置树缓存
     */
    private volatile List<Dict> configTreeCache;

    /**
     * 缓存更新时间
     */
    private volatile LocalDateTime cacheUpdateTime;

    private static final String DICT_TYPE = "third_party_whitelist";

    @PostConstruct
    public void initCache() {
        refreshCache();
        log.info("第三方API白名单缓存初始化完成，精确匹配方法数量: {}, 类通配符数量: {}, 包通配符数量: {}", 
                whitelistExactMethods.size(), whitelistClassWildcards.size(), whitelistPackageWildcards.size());
    }

    @Override
    public boolean isUrlInWhitelist(String url) {
        // 基于方法的控制，URL检查作为辅助验证
        return true; // 默认放行，主要依赖方法级别控制
    }

    @Override
    public boolean isMethodInWhitelist(String fullMethodName, String httpMethod) {
        if (!StringUtils.hasText(fullMethodName)) {
            return false;
        }

        // 1. 精确匹配（完整类名.方法名）
        if (whitelistExactMethods.contains(fullMethodName)) {
            log.debug("方法精确匹配成功: {}", fullMethodName);
            return true;
        }

        // 2. 类通配符匹配（类名.*）
        for (String classPattern : whitelistClassWildcards) {
            if (matchesClassPattern(fullMethodName, classPattern)) {
                log.debug("方法类通配符匹配成功: {} -> {}", fullMethodName, classPattern);
                return true;
            }
        }

        // 3. 包通配符匹配（包名.*）
        for (String packagePattern : whitelistPackageWildcards) {
            if (matchesPackagePattern(fullMethodName, packagePattern)) {
                log.debug("方法包通配符匹配成功: {} -> {}", fullMethodName, packagePattern);
                return true;
            }
        }

        log.debug("方法不在白名单中: {}", fullMethodName);
        return false;
    }

    @Override
    public List<String> getWhitelistUrls() {
        return Arrays.asList("基于方法级别控制，URL列表已废弃");
    }

    @Override
    public List<String> getWhitelistMethods() {
        List<String> allMethods = new ArrayList<>();
        allMethods.addAll(whitelistExactMethods.stream()
                .map(method -> method + " (精确)")
                .collect(Collectors.toList()));
        allMethods.addAll(whitelistClassWildcards.stream()
                .map(pattern -> pattern + " (类通配符)")
                .collect(Collectors.toList()));
        allMethods.addAll(whitelistPackageWildcards.stream()
                .map(pattern -> pattern + " (包通配符)")
                .collect(Collectors.toList()));
        return allMethods.stream().sorted().collect(Collectors.toList());
    }

    @Override
    public List<Dict> getWhitelistTree() {
        if (configTreeCache == null) {
            refreshCache();
        }
        return configTreeCache;
    }

    @Override
    public void refreshCache() {
        try {
            // 清空所有缓存
            whitelistExactMethods.clear();
            whitelistClassWildcards.clear();
            whitelistPackageWildcards.clear();

            // 获取启用的配置项
            List<Dict> configs = dictService.getEnabledDictEntities(DICT_TYPE);
            
            for (Dict config : configs) {
                String value = config.getDictValue();
                Integer matchType = config.getMatchType();
                
                if (StringUtils.hasText(value) && matchType != null && value.contains(".")) {
                    // 处理方法配置（包含类路径）
                    processMethodConfig(value, matchType);
                }
            }

            // 构建配置树
            buildConfigTree();

            cacheUpdateTime = LocalDateTime.now();
            log.info("第三方API白名单缓存刷新完成，方法配置 - 精确: {}, 类通配符: {}, 包通配符: {}", 
                    whitelistExactMethods.size(), whitelistClassWildcards.size(), whitelistPackageWildcards.size());

        } catch (Exception e) {
            log.error("刷新第三方API白名单缓存失败", e);
            throw new RuntimeException("刷新白名单缓存失败", e);
        }
    }

    @Override
    public String getCacheInfo() {
        return String.format("缓存更新时间: %s, 精确匹配方法数量: %d, 类通配符数量: %d, 包通配符数量: %d", 
                cacheUpdateTime, whitelistExactMethods.size(), whitelistClassWildcards.size(), whitelistPackageWildcards.size());
    }

    /**
     * 处理方法配置
     */
    private void processMethodConfig(String methodPattern, Integer matchType) {
        switch (matchType) {
            case Dict.MatchType.EXACT:
                whitelistExactMethods.add(methodPattern);
                log.debug("添加精确匹配方法: {}", methodPattern);
                break;
            case Dict.MatchType.CLASS_WILDCARD:
                whitelistClassWildcards.add(methodPattern);
                log.debug("添加类通配符匹配方法: {}", methodPattern);
                break;
            case Dict.MatchType.PACKAGE_WILDCARD:
                whitelistPackageWildcards.add(methodPattern);
                log.debug("添加包通配符匹配方法: {}", methodPattern);
                break;
            default:
                whitelistExactMethods.add(methodPattern);
                log.debug("添加默认精确匹配方法: {}", methodPattern);
                break;
        }
    }

    /**
     * 类通配符模式匹配
     */
    private boolean matchesClassPattern(String fullMethodName, String pattern) {
        if (pattern.contains("*")) {
            String prefix = pattern.substring(0, pattern.indexOf("*"));
            return fullMethodName.startsWith(prefix);
        }
        return false;
    }

    /**
     * 包通配符模式匹配
     */
    private boolean matchesPackagePattern(String fullMethodName, String pattern) {
        if (pattern.endsWith(".*")) {
            String packagePrefix = pattern.substring(0, pattern.length() - 1);
            return fullMethodName.startsWith(packagePrefix);
        }
        return false;
    }

    /**
     * 构建配置树
     */
    private void buildConfigTree() {
        try {
            List<Dict> allNodes = dictService.getAllDictEntities(DICT_TYPE);
            configTreeCache = buildTree(allNodes);
        } catch (Exception e) {
            log.error("构建配置树失败", e);
            configTreeCache = new ArrayList<>();
        }
    }

    /**
     * 递归构建树形结构
     */
    private List<Dict> buildTree(List<Dict> allNodes) {
        Map<Long, Dict> nodeMap = allNodes.stream()
                .collect(Collectors.toMap(Dict::getId, node -> node));

        List<Dict> rootNodes = new ArrayList<>();

        for (Dict node : allNodes) {
            if (node.getParentId() == null) {
                rootNodes.add(node);
            } else {
                Dict parent = nodeMap.get(node.getParentId());
                if (parent != null) {
                    if (parent.getChildren() == null) {
                        parent.setChildren(new ArrayList<>());
                    }
                    parent.getChildren().add(node);
                }
            }
        }

        sortTree(rootNodes);
        return rootNodes;
    }

    /**
     * 对树进行排序
     */
    private void sortTree(List<Dict> nodes) {
        if (nodes == null || nodes.isEmpty()) {
            return;
        }

        nodes.sort((a, b) -> {
            int sortCompare = Integer.compare(
                    a.getSortOrder() != null ? a.getSortOrder() : 0,
                    b.getSortOrder() != null ? b.getSortOrder() : 0
            );
            return sortCompare != 0 ? sortCompare : Long.compare(a.getId(), b.getId());
        });

        for (Dict node : nodes) {
            if (node.getChildren() != null) {
                sortTree(node.getChildren());
            }
        }
    }
}