package com.etrx.kb.dto;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * Chunk操作请求DTO
 */
@Data
public class ChunkRequestDTO {

    /**
     * 添加Chunk请求
     */
    @Data
    public static class AddChunkRequest {
        /**
         * 数据集ID
         */
        @NotBlank(message = "数据集ID不能为空")
        private String datasetId;

        /**
         * 文档ID
         */
        @NotBlank(message = "文档ID不能为空")
        private String documentId;

        /**
         * Chunk内容
         */
        @NotBlank(message = "Chunk内容不能为空")
        private String content;

        /**
         * 重要关键词列表
         */
        private List<String> importantKeywords;

        /**
         * 相关问题列表
         */
        private List<String> questions;
    }

    /**
     * 查询Chunk请求
     */
    @Data
    public static class ListChunkRequest {
        /**
         * 数据集ID
         */
        @NotBlank(message = "数据集ID不能为空")
        private String datasetId;

        /**
         * 文档ID
         */
        @NotBlank(message = "文档ID不能为空")
        private String documentId;

        /**
         * 关键词过滤
         */
        private String keywords;

        /**
         * 页码
         */
        private Integer page;

        /**
         * 每页大小
         */
        private Integer pageSize;

        /**
         * Chunk ID
         */
        private String id;
    }

    /**
     * 删除Chunk请求
     */
    @Data
    public static class DeleteChunkRequest {
        /**
         * 数据集ID
         */
        @NotBlank(message = "数据集ID不能为空")
        private String datasetId;

        /**
         * 文档ID
         */
        @NotBlank(message = "文档ID不能为空")
        private String documentId;

        /**
         * 要删除的Chunk ID列表
         */
        @NotNull(message = "Chunk ID列表不能为空")
        private List<String> chunkIds;
    }

    /**
     * 更新Chunk请求
     */
    @Data
    public static class UpdateChunkRequest {
        /**
         * 数据集ID
         */
        @NotBlank(message = "数据集ID不能为空")
        private String datasetId;

        /**
         * 文档ID
         */
        @NotBlank(message = "文档ID不能为空")
        private String documentId;

        /**
         * Chunk ID
         */
        @NotBlank(message = "Chunk ID不能为空")
        private String chunkId;

        /**
         * 更新字段
         */
        @NotNull(message = "更新字段不能为空")
        private Map<String, Object> updateFields;
    }

    /**
     * 批量更新Chunk请求
     */
    @Data
    public static class BatchUpdateChunkRequest {
        /**
         * 数据集ID
         */
        @NotBlank(message = "数据集ID不能为空")
        private String datasetId;

        /**
         * 文档ID
         */
        @NotBlank(message = "文档ID不能为空")
        private String documentId;

        /**
         * 要更新的Chunk ID列表
         */
        @NotNull(message = "Chunk ID列表不能为空")
        private List<String> chunkIds;

        /**
         * 更新字段
         */
        @NotNull(message = "更新字段不能为空")
        private Map<String, Object> updateFields;
    }

    /**
     * 检索Chunk请求
     */
    @Data
    public static class RetrieveChunkRequest {
        /**
         * 查询问题
         */
        @NotBlank(message = "查询问题不能为空")
        private String question;

        /**
         * 数据集ID列表
         */
        private List<String> datasetIds;

        /**
         * 文档ID列表
         */
        private List<String> documentIds;

        /**
         * 页码
         */
        private Integer page;

        /**
         * 每页大小
         */
        private Integer pageSize;

        /**
         * 相似度阈值
         */
        private Float similarityThreshold;

        /**
         * 向量相似度权重
         */
        private Float vectorSimilarityWeight;

        /**
         * Top K数量
         */
        private Integer topK;

        /**
         * 重排序模型ID
         */
        private String rerankId;

        /**
         * 是否启用关键词匹配
         */
        private Boolean keyword;

        /**
         * 是否启用高亮
         */
        private Boolean highlight;
    }
} 