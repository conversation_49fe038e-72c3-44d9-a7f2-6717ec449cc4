package com.etrx.kb.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.etrx.kb.vo.base.RagflowBaseVO;

import java.util.List;
import java.util.Map;

/**
 * Ragflow文档列表返回结果
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class RagflowDocsListVO extends RagflowBaseVO {
    private DataVO data;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DataVO {
        private List<UploadFileVO> docs;
        private Integer total;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class UploadFileVO extends BaseUploadFileVO {
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ParserConfigVO extends BaseParserConfigVO {
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class RaptorVO extends BaseRaptorVO {
    }
} 