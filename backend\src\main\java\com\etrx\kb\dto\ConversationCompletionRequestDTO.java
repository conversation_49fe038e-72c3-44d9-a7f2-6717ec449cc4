package com.etrx.kb.dto;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;

import java.util.List;

/**
 * RAGflow原生格式的Conversation Completion请求DTO
 */
@Data
public class ConversationCompletionRequestDTO {

    /**
     * 会话ID
     */
    @NotBlank(message = "conversation_id不能为空")
    private String conversationId;

    /**
     * 消息历史列表
     */
    @NotEmpty(message = "messages不能为空")
    private List<ConversationMessage> messages;

    /**
     * 是否启用流式响应，默认为true
     */
    private Boolean stream = true;

    /**
     * 对话消息DTO
     */
    @Data
    public static class ConversationMessage {
        
        /**
         * 消息ID
         */
        private String id;
        
        /**
         * 消息内容
         */
        @NotBlank(message = "消息内容不能为空")
        private String content;
        
        /**
         * 角色：user 或 assistant
         */
        @NotBlank(message = "角色不能为空")
        private String role;
        
        /**
         * 文档ID列表（用户消息可能包含）
         */
        private List<String> docIds;
        
        /**
         * 创建时间戳（助手消息可能包含）
         */
        private Double createdAt;
    }
} 