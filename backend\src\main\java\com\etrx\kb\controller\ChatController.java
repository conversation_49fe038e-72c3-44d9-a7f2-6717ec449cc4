package com.etrx.kb.controller;

import com.etrx.kb.common.PageResult;
import com.etrx.kb.common.Result;
import com.etrx.kb.dto.AgentDTO;
import com.etrx.kb.dto.ChatAssistantMappingDTO;
import com.etrx.kb.dto.ChatRequestDTO;
import com.etrx.kb.dto.ChatResponseDTO;
import com.etrx.kb.dto.ChatSessionDTO;
import com.etrx.kb.dto.CreateChatAssistantRequestDTO;
import com.etrx.kb.dto.ChatHistoryResponseDTO;
import com.etrx.kb.service.AgentService;
import com.etrx.kb.service.ChatAssistantMappingService;
import com.etrx.kb.service.ChatService;
import com.etrx.kb.util.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 聊天控制器
 */
@Tag(name = "聊天管理", description = "提供聊天对话、流式聊天、助手管理、会话管理、Agent提示词模板等功能")
@Slf4j
@RestController
@RequestMapping("/aikb/chat")
@RequiredArgsConstructor
public class ChatController {

    private final ChatService chatService;
    private final ChatAssistantMappingService chatAssistantMappingService;
    private final AgentService agentService;
    private final SecurityUtils securityUtils;

    /**
     * 发送聊天消息（流式响应）
     */
    @PostMapping(value = "/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter streamMessage(@Valid @RequestBody ChatRequestDTO request) {
        Long userId = securityUtils.getCurrentUserId();
        SseEmitter emitter = new SseEmitter(-0L);
        // 设置基本回调
        emitter.onTimeout(() -> {
            log.warn("SSE连接超时: userId={}", userId);
            emitter.complete();
        });
        emitter.onError(throwable -> {
            log.error("SSE连接发生错误: userId={}", userId, throwable);
            emitter.complete();
        });
        // 异步处理
        CompletableFuture.runAsync(() -> {
            try {
                chatService.streamChatWithCallback(request, userId, response -> {
                    try {
                        emitter.send(SseEmitter.event().name("message").data(response));
                        if (Boolean.TRUE.equals(response.getFinished())) {
                            emitter.complete();
                        }
                    } catch (Exception e) {
                        log.error("发送SSE消息失败", e);
                        emitter.completeWithError(e);
                    }
                });
            } catch (Exception e) {
                log.error("流式聊天处理异常", e);
                try {
                    ChatResponseDTO errorResponse = new ChatResponseDTO();
                    errorResponse.setError("聊天处理失败: " + e.getMessage());
                    errorResponse.setFinished(true);
                    emitter.send(SseEmitter.event().name("error").data(errorResponse));
                    emitter.complete();
                } catch (Exception ex) {
                    emitter.completeWithError(ex);
                }
            }
        });
        return emitter;
    }

    /**
     * RAGflow原生格式的Conversation Completion（非流式响应）
     */
    @PostMapping("/v1/conversation/completion")
    public Result<List<ChatResponseDTO>> conversationCompletion(@Valid @RequestBody com.etrx.kb.dto.ConversationCompletionRequestDTO request) {
        Long userId = securityUtils.getCurrentUserId();
        // 设置为非流式响应
        request.setStream(false);
        List<ChatResponseDTO> responses = chatService.conversationCompletion(request, userId);
        return Result.success(responses);
    }

    /**
     * 新增/修改聊天助手（统一格式）
     * 支持RAGflow前端格式和原生格式
     * 根据dialog_id是否存在来判断是新增还是修改操作
     */
    @PostMapping("/assistant")
    public Result<String> createOrUpdateChatAssistant(@Valid @RequestBody CreateChatAssistantRequestDTO request) {
        Long userId = securityUtils.getCurrentUserId();
        String chatAssistantId = chatService.createOrUpdateChatAssistant(request, userId);
        return Result.success(chatAssistantId);
    }

    /**
     * 创建聊天会话
     */
    @PostMapping("/session")
    public Result<String> createChatSession(
            @RequestParam String chatAssistantId,
            @RequestParam(required = false) String sessionName) {
        Long userId = securityUtils.getCurrentUserId();
        String sessionId = chatService.createChatSession(chatAssistantId, sessionName, userId);
        return Result.success(sessionId);
    }

    /**
     * 更新聊天会话
     */
    @PutMapping("/session/{sessionId}")
    public Result<String> updateChatSession(
            @PathVariable String sessionId,
            @RequestParam String sessionName) {
        Long userId = securityUtils.getCurrentUserId();
        String result = chatService.updateChatSession(sessionId, sessionName, userId);
        return Result.success(result);
    }

    /**
     * 删除聊天会话
     */
    @DeleteMapping("/session/{sessionId}")
    public Result<?> deleteChatSession(@PathVariable String sessionId) {
        Long userId = securityUtils.getCurrentUserId();
        chatService.deleteChatSession(sessionId, userId);
        return Result.success();
    }

    // ==================== 聊天助手映射管理 ====================
    /**
     * 获取用户的聊天助手列表
     */
    @GetMapping("/assistants")
    public Result<List<ChatAssistantMappingDTO>> getUserChatAssistants() {
        Long userId = securityUtils.getCurrentUserId();
        List<ChatAssistantMappingDTO> assistants = chatService.getUserChatAssistants(userId);
        return Result.success(assistants);
    }

    /**
     * 分页获取用户的聊天助手列表
     */
    @GetMapping("/assistants/page")
    public Result<PageResult<ChatAssistantMappingDTO>> getUserChatAssistantsPage(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        Long userId = securityUtils.getCurrentUserId();
        PageResult<ChatAssistantMappingDTO> pageResult = chatService.getUserChatAssistantsPage(userId, pageNum, pageSize);
        return Result.success(pageResult);
    }

    /**
     * 获取当前用户的默认聊天助手
     * 如果没有默认助手，则自动创建一个
     */
    @GetMapping("/assistants/default")
    @Operation(summary = "获取默认聊天助手", description = "获取当前用户的默认聊天助手，如果没有则自动创建")
    public Result<ChatAssistantMappingDTO> getOrCreateDefaultChatAssistant() {
        Long userId = securityUtils.getCurrentUserId();
        ChatAssistantMappingDTO defaultAssistant = chatService.getOrCreateDefaultChatAssistant(userId);
        return Result.success(defaultAssistant);
    }

    /**
     * 根据助手ID获取映射信息
     */
    @GetMapping("/assistants/{assistantId}")
    public Result<ChatAssistantMappingDTO> getChatAssistantMapping(@PathVariable String assistantId) {
        Long userId = securityUtils.getCurrentUserId();
        ChatAssistantMappingDTO mapping = chatAssistantMappingService.getMappingByUserAndAssistant(userId, assistantId);
        if (mapping == null) {
            return Result.failed("聊天助手不存在");
        }
        return Result.success(mapping);
    }

    /**
     * 获取助手详情
     */
    @GetMapping("/assistants/{assistantId}/details")
    public ResponseEntity<String> getChatAssistantDetails(@PathVariable String assistantId) {
        Long userId = securityUtils.getCurrentUserId();
        String details = chatService.getChatAssistantDetails(assistantId, userId);
        return ResponseEntity.ok()
                .contentType(org.springframework.http.MediaType.APPLICATION_JSON)
                .body(details);
    }

    /**
     * 禁用聊天助手
     */
    @PutMapping("/assistants/{id}/disable")
    public Result<?> disableChatAssistant(@PathVariable Long id) {
        Long userId = securityUtils.getCurrentUserId();
        chatAssistantMappingService.disableChatAssistant(id, userId);
        return Result.success();
    }

    /**
     * 启用聊天助手
     */
    @PutMapping("/assistants/{id}/enable")
    public Result<?> enableChatAssistant(@PathVariable Long id) {
        Long userId = securityUtils.getCurrentUserId();
        chatAssistantMappingService.enableChatAssistant(id, userId);
        return Result.success();
    }

    /**
     * 删除聊天助手
     * 包括删除RAGflow中的助手、本地映射记录以及相关的会话映射
     */
    @DeleteMapping("/assistants/{assistantId}")
    public Result<?> deleteChatAssistant(@PathVariable String assistantId) {
        Long userId = securityUtils.getCurrentUserId();
        chatService.deleteChatAssistant(assistantId, userId);
        return Result.success();
    }

    /**
     * 根据聊天助手ID获取会话列表
     */
    @GetMapping("/assistants/{assistantId}/sessions")
    @PreAuthorize("hasRole('USER')")
    public Result<List<ChatSessionDTO>> getChatSessionsByAssistantId(@PathVariable String assistantId) {
        Long userId = securityUtils.getCurrentUserId();
        List<ChatSessionDTO> sessions = chatService.getChatSessionsByAssistantId(assistantId, userId);
        return Result.success(sessions);
    }

    /**
     * 获取聊天历史记录
     */
    @GetMapping("/history/{sessionId}")
    public ChatHistoryResponseDTO getChatSessionHistory(@PathVariable String sessionId) {
        Long userId = securityUtils.getCurrentUserId();
        return chatService.getChatHistoryStandard(sessionId, userId);
    }

    // ====================  Agent相关接口  ====================

    /**
     * 获取所有Agent列表
     */
    @Operation(summary = "获取所有Agent列表", description = "获取系统中所有可用的Agent提示词模板")
    @GetMapping("/agents")
    public Result<List<AgentDTO>> getAllAgents() {
        List<AgentDTO> agents = agentService.getAllAgents();
        return Result.success(agents);
    }

    /**
     * 根据ID获取Agent详情
     */
    @Operation(summary = "根据ID获取Agent详情", description = "通过Agent ID获取具体的Agent信息")
    @GetMapping("/agents/{id}")
    public Result<AgentDTO> getAgentById(
            @Parameter(description = "Agent ID", required = true) @PathVariable String id) {
        AgentDTO agent = agentService.getAgentById(id);
        if (agent == null) {
            return Result.failed("Agent不存在");
        }
        return Result.success(agent);
    }

    /**
     * 根据分组获取Agent列表
     */
    @Operation(summary = "根据分组获取Agent列表", description = "根据分组名称获取对应的Agent列表")
    @GetMapping("/agents/group/{group}")
    public Result<List<AgentDTO>> getAgentsByGroup(
            @Parameter(description = "分组名称", required = true) @PathVariable String group) {
        List<AgentDTO> agents = agentService.getAgentsByGroup(group);
        return Result.success(agents);
    }

    /**
     * 获取所有Agent分组
     */
    @Operation(summary = "获取所有Agent分组", description = "获取系统中所有Agent的分组列表")
    @GetMapping("/agents/groups")
    public Result<List<String>> getAllGroups() {
        List<String> groups = agentService.getAllGroups();
        return Result.success(groups);
    }

    /**
     * 搜索Agent
     */
    @Operation(summary = "搜索Agent", description = "根据关键词搜索Agent，支持按名称、描述、分组搜索")
    @GetMapping("/agents/search")
    public Result<List<AgentDTO>> searchAgents(
            @Parameter(description = "搜索关键词，可为空") @RequestParam(required = false) String keyword) {
        List<AgentDTO> agents = agentService.searchAgents(keyword);
        return Result.success(agents);
    }
}
