package com.etrx.kb.service;

import com.etrx.kb.domain.Dict;

import java.util.List;

/**
 * 第三方API白名单服务接口（支持方法级别精确控制）
 */
public interface ThirdPartyWhitelistService {

    /**
     * 检查URL是否在白名单中（支持前缀匹配）
     *
     * @param url 请求URL
     * @return 是否允许访问
     */
    boolean isUrlInWhitelist(String url);

    /**
     * 检查方法是否在白名单中（方法级别精确控制）
     *
     * @param fullMethodName 完整方法名（类名.方法名）
     * @param httpMethod HTTP方法（可选）
     * @return 是否允许访问
     */
    boolean isMethodInWhitelist(String fullMethodName, String httpMethod);

    /**
     * 获取白名单URL列表
     *
     * @return URL列表
     */
    List<String> getWhitelistUrls();

    /**
     * 获取白名单方法列表
     *
     * @return 方法列表
     */
    List<String> getWhitelistMethods();

    /**
     * 获取白名单配置树
     *
     * @return 配置树
     */
    List<Dict> getWhitelistTree();

    /**
     * 刷新缓存
     */
    void refreshCache();

    /**
     * 获取缓存状态信息
     *
     * @return 缓存信息
     */
    String getCacheInfo();
} 