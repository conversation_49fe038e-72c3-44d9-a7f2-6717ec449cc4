package com.etrx.kb.service.impl;

import com.etrx.kb.domain.TaskQueue;
import com.etrx.kb.enums.TaskExecutionStatus;
import com.etrx.kb.mapper.TaskQueueMapper;
import com.etrx.kb.mapper.TaskSequenceMapper;
import com.etrx.kb.service.TaskExecutionService;
import com.etrx.kb.service.TaskSchedulerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务调度服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskSchedulerServiceImpl implements TaskSchedulerService {

    private final TaskQueueMapper taskQueueMapper;
    private final TaskSequenceMapper taskSequenceMapper;
    private final TaskExecutionService taskExecutionService;

    /**
     * 主机数量，默认为5，可通过配置文件修改
     */
    @Value("${task.scheduler.host-count:5}")
    private int hostCount;

    /**
     * 每次扫描的任务数量限制
     */
    @Value("${task.scheduler.batch-size:100}")
    private int batchSize;

    @Override
    @Transactional
    public void scanAndExecuteTasks() {
        Long sequenceNumber = null;
        try {
            // 获取当前主机的序列号
            sequenceNumber = getNextSequenceNumber();
            log.debug("当前主机序列号: {}", sequenceNumber);
            
            // 查询属于当前主机的待执行任务
            List<TaskQueue> pendingTasks = taskQueueMapper.selectPendingTasksByModulo(
                    hostCount, sequenceNumber, batchSize);
            
            if (pendingTasks.isEmpty()) {
                log.debug("没有待执行的任务");
                return;
            }
            
            log.info("扫描到 {} 个待执行任务", pendingTasks.size());
            
            // 逐个处理任务
            for (TaskQueue taskQueue : pendingTasks) {
                processTask(taskQueue);
            }
            
        } catch (Exception e) {
            log.error("扫描任务失败 [sequence={}]", sequenceNumber, e);
            throw new RuntimeException("扫描任务失败", e);
        }
    }
    
    /**
     * 处理单个任务
     */
    @Transactional
    protected void processTask(TaskQueue taskQueue) {
        if (shouldSkipTask(taskQueue)) {
            log.debug("跳过任务 [taskId={}]", taskQueue.getId());
            return;
        }

        try {
            // 尝试锁定任务
            int locked = lockTask(taskQueue);
            if (locked != 1) {
                log.debug("任务已被其他进程处理 [taskId={}]", taskQueue.getId());
                return;
            }

            // 执行任务
            taskExecutionService.executeTask(taskQueue);
            
            // 更新任务状态为成功
            updateTaskStatus(taskQueue, TaskExecutionStatus.COMPLETED, null);
            
        } catch (Exception e) {
            log.error("执行任务失败 [taskId={}]", taskQueue.getId(), e);
            // 更新任务状态为失败
            updateTaskStatus(taskQueue, TaskExecutionStatus.FAILED, e.getMessage());
            throw new RuntimeException("执行任务失败", e);
        }
    }
    
    /**
     * 锁定任务
     */
    private int lockTask(TaskQueue taskQueue) {
        taskQueue.setExecutionStatus(TaskExecutionStatus.RUNNING);
        taskQueue.setUpdateTime(LocalDateTime.now());
        return taskQueueMapper.updateById(taskQueue);
    }
    
    /**
     * 更新任务状态
     */
    private void updateTaskStatus(TaskQueue taskQueue, TaskExecutionStatus status, String errorMessage) {
        taskQueue.setExecutionStatus(status);
        taskQueue.setErrorMessage(errorMessage);
        taskQueue.setUpdateTime(LocalDateTime.now());
        taskQueueMapper.updateById(taskQueue);
    }

    @Override
    @Transactional
    public Long getNextSequenceNumber() {
        try {
            // 先获取当前值
            Long currentValue = taskSequenceMapper.getNextSequence("TASK_SCANNER_SEQUENCE");
            if (currentValue == null) {
                log.error("获取序列号失败：序列不存在");
                throw new RuntimeException("序列不存在");
            }

            // 增加乐观锁机制
            // 更新序列值
            int updated = taskSequenceMapper.updateSequence("TASK_SCANNER_SEQUENCE", currentValue);
            if (updated != 1) {
                log.warn("更新序列号失败, 序列号已被其他线程执行, 本次调度跳过, 等待下次调度");
                throw new RuntimeException("更新序列号失败");
            }
            
            return currentValue;
        } catch (Exception e) {
            log.error("获取下一个序列号时发生错误", e);
            throw new RuntimeException("获取序列号失败", e);
        }
    }

    /**
     * 判断是否应该跳过任务
     */
    private boolean shouldSkipTask(TaskQueue taskQueue) {
        // 检查是否有下次执行时间且时间未到
        if (taskQueue.getNextExecutionTime() != null && 
            taskQueue.getNextExecutionTime().isAfter(LocalDateTime.now())) {
            log.debug("任务 {} 还未到执行时间", taskQueue.getId());
            return true;
        }
        
        // 检查任务状态
        if (taskQueue.getExecutionStatus() == null || 
            taskQueue.getExecutionStatus() != TaskExecutionStatus.PENDING) {
            log.debug("任务 {} 状态不是待执行: {}", taskQueue.getId(), taskQueue.getExecutionStatus());
            return true;
        }
        
        return false;
    }
} 