package com.etrx.kb.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.etrx.kb.domain.FileNode;
import com.etrx.kb.domain.KbFileRel;
import com.etrx.kb.domain.KnowledgeBase;
import com.etrx.kb.mapper.KbFileRelMapper;
import com.etrx.kb.mapper.KnowledgeBaseMapper;
import com.etrx.kb.service.FileService;
import com.etrx.kb.common.Constants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class DocumentStatusSyncTask {

    private final FileService fileService;
    private final KbFileRelMapper kbFileRelMapper;
    private final KnowledgeBaseMapper knowledgeBaseMapper;
    
    private final AtomicBoolean enabled = new AtomicBoolean(true);

    @Scheduled(fixedRate = 60000) // 每分钟执行一次
    public void syncDocumentStatus() {
        if (!enabled.get()) {
            log.debug("文档状态同步任务已暂停");
            return;
        }

        log.info("开始执行文档状态同步定时任务");
        try {
            // 1. 查询所有状态为PROCESSING的文档关联关系
            LambdaQueryWrapper<KbFileRel> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(KbFileRel::getDocumentStatus, Constants.DocumentStatus.PROCESSING)
                  .isNotNull(KbFileRel::getDocumentId);
            List<KbFileRel> processingRels = kbFileRelMapper.selectList(wrapper);

            if (processingRels.isEmpty()) {
                log.info("没有需要同步状态的文档");
                return;
            }

            // 2. 获取这些文档关联的文件节点和知识库信息
            List<Long> fileIds = processingRels.stream()
                    .map(KbFileRel::getFileNodeId)
                    .collect(Collectors.toList());

            // 查询文件节点信息
            List<FileNode> fileNodes = fileService.listByIds(fileIds);
            Map<Long, FileNode> fileNodeMap = fileNodes.stream()
                    .collect(Collectors.toMap(
                            FileNode::getId,
                            node -> node
                    ));

            // 获取所有相关的知识库信息
            List<Long> kbIds = processingRels.stream()
                    .map(KbFileRel::getKbId)
                    .collect(Collectors.toList());
            List<KnowledgeBase> knowledgeBases = knowledgeBaseMapper.selectBatchIds(kbIds);
            Map<Long, KnowledgeBase> kbMap = knowledgeBases.stream()
                    .collect(Collectors.toMap(
                            KnowledgeBase::getId,
                            kb -> kb
                    ));

            // 3. 按知识库分组处理文档
            Map<Long, List<FileNode>> kbDocsMap = processingRels.stream()
                    .filter(rel -> fileNodeMap.containsKey(rel.getFileNodeId()))
                    .map(rel -> fileNodeMap.get(rel.getFileNodeId()))
                    .collect(Collectors.groupingBy(
                            node -> processingRels.stream()
                                    .filter(rel -> rel.getFileNodeId().equals(node.getId()))
                                    .findFirst()
                                    .map(KbFileRel::getKbId)
                                    .orElse(null)
                    ));

            // 4. 对每个知识库的文档进行状态同步
            for (Map.Entry<Long, List<FileNode>> entry : kbDocsMap.entrySet()) {
                Long kbId = entry.getKey();
                List<FileNode> docs = entry.getValue();
                KnowledgeBase kb = kbMap.get(kbId);
                
                if (kb != null) {
                    fileService.processDocsStatus(kbId, docs, kb);
                } else {
                    log.warn("找不到知识库信息，kbId: {}", kbId);
                }
            }

            log.info("文档状态同步定时任务执行完成，共处理{}个文档", processingRels.size());
        } catch (Exception e) {
            log.error("文档状态同步定时任务执行失败", e);
        }
    }

    /**
     * 暂停任务
     */
    public void pause() {
        if (enabled.compareAndSet(true, false)) {
            log.info("文档状态同步任务已暂停");
        }
    }

    /**
     * 恢复任务
     */
    public void resume() {
        if (enabled.compareAndSet(false, true)) {
            log.info("文档状态同步任务已恢复");
        }
    }

    /**
     * 获取任务状态
     */
    public boolean isEnabled() {
        return enabled.get();
    }
} 