package com.etrx.kb.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 模型配置实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("model_config")
public class ModelConfigEntity {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 提供商名称
     */
    private String provider;

    /**
     * 模型名称
     */
    private String llmName;

    /**
     * 模型类型：chat, embedding, tts, speech2text
     */
    private String modelType;

    /**
     * 是否可用
     */
    private Boolean available;

    /**
     * 最大令牌数
     */
    private Integer maxTokens;

    /**
     * 是否支持工具
     */
    private Boolean isTools;

    /**
     * 状态
     */
    private String status;

    /**
     * 标签，用逗号分隔
     */
    private String tags;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    /**
     * 逻辑删除标识：0-未删除，1-已删除
     */
    @TableLogic
    private Integer deleted;
} 