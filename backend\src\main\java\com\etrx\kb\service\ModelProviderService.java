package com.etrx.kb.service;

import org.springframework.ai.chat.model.ChatModel;

/**
 * 模型提供商服务接口
 * 用于统一管理不同的AI模型提供商
 */
public interface ModelProviderService {
    
    /**
     * 根据提供商名称获取ChatModel
     * 
     * @param providerName 提供商名称 (Ollama, OpenAI, AzureOpenAI)
     * @return ChatModel实例
     */
    ChatModel getChatModel(String providerName);
    
    /**
     * 根据提供商名称和模型名称获取ChatModel
     * 
     * @param providerName 提供商名称
     * @param modelName 模型名称
     * @return ChatModel实例
     */
    ChatModel getChatModel(String providerName, String modelName);
    
    /**
     * 获取默认的ChatModel
     * 
     * @return 默认ChatModel实例
     */
    ChatModel getDefaultChatModel();
    
    /**
     * 检查指定提供商是否可用
     * 
     * @param providerName 提供商名称
     * @return 是否可用
     */
    boolean isProviderAvailable(String providerName);
    
    /**
     * 获取所有可用的提供商名称
     * 
     * @return 可用提供商名称列表
     */
    String[] getAvailableProviders();
} 