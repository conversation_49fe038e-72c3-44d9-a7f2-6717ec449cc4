package com.etrx.kb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.etrx.kb.domain.TaskDefinition;
import com.etrx.kb.mapper.TaskDefinitionMapper;
import com.etrx.kb.service.TaskDefinitionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 任务定义服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskDefinitionServiceImpl implements TaskDefinitionService {
    
    private final TaskDefinitionMapper taskDefinitionMapper;
    
    @Override
    @Transactional
    public void saveOrUpdateTaskDefinition(TaskDefinition taskDefinition) {
        // 查找是否已存在相同名称的任务定义
        QueryWrapper<TaskDefinition> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("task_name", taskDefinition.getTaskName());
        
        TaskDefinition existingTask = taskDefinitionMapper.selectOne(queryWrapper);
        
        if (existingTask != null) {
            // 更新现有任务定义
            existingTask.setTaskType(taskDefinition.getTaskType());
            existingTask.setClassPath(taskDefinition.getClassPath());
            existingTask.setMethodName(taskDefinition.getMethodName());
            existingTask.setThreadCount(taskDefinition.getThreadCount());
            existingTask.setMaxExecutionCount(taskDefinition.getMaxExecutionCount());
            existingTask.setMaxRetryCount(taskDefinition.getMaxRetryCount());
            existingTask.setDescription(taskDefinition.getDescription());
            existingTask.setUpdateTime(LocalDateTime.now());
            
            taskDefinitionMapper.updateById(existingTask);
            log.info("更新任务定义: {}", taskDefinition.getTaskName());
        } else {
            // 创建新任务定义
            taskDefinitionMapper.insert(taskDefinition);
            log.info("创建新任务定义: {}", taskDefinition.getTaskName());
        }
    }
    
    @Override
    public TaskDefinition getByTaskName(String taskName) {
        QueryWrapper<TaskDefinition> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("task_name", taskName);
        return taskDefinitionMapper.selectOne(queryWrapper);
    }
    
    @Override
    public TaskDefinition getById(Long id) {
        return taskDefinitionMapper.selectById(id);
    }
} 