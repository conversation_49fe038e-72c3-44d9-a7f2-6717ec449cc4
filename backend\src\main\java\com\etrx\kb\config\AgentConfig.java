package com.etrx.kb.config;

import com.etrx.kb.dto.AgentDTO;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Agent配置类，负责加载agents.json到内存
 */
@Slf4j
@Configuration
public class AgentConfig {

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 将agents.json加载到内存中的Map，key为agentId，value为AgentDTO
     */
    @Bean
    public Map<String, AgentDTO> agentMap() {
        try {
            ClassPathResource resource = new ClassPathResource("agents.json");
            InputStream inputStream = resource.getInputStream();
            
            List<AgentDTO> agents = objectMapper.readValue(
                inputStream, 
                new TypeReference<List<AgentDTO>>() {}
            );
            
            Map<String, AgentDTO> agentMap = agents.stream()
                .collect(Collectors.toConcurrentMap(
                    AgentDTO::getId,
                    Function.identity(),
                    (existing, replacement) -> existing,
                    ConcurrentHashMap::new
                ));
            
            log.info("成功加载{}个Agent到内存中", agentMap.size());
            return agentMap;
            
        } catch (IOException e) {
            log.error("加载agents.json失败", e);
            return new ConcurrentHashMap<>();
        }
    }
} 