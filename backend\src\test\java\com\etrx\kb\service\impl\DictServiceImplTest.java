package com.etrx.kb.service.impl;

import com.etrx.kb.domain.Dict;
import com.etrx.kb.mapper.DictMapper;
import com.etrx.kb.vo.DictVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * DictServiceImpl单元测试
 * 
 * <AUTHOR>
 * @date 2024-01-10
 */
@ExtendWith(MockitoExtension.class)
class DictServiceImplTest {
    
    @Mock
    private DictMapper dictMapper;
    
    @InjectMocks
    private DictServiceImpl dictService;
    
    private Dict mockDict;
    private List<Dict> mockDictList;
    
    @BeforeEach
    void setUp() {
        // 创建测试数据
        mockDict = new Dict();
        mockDict.setId(1L);
        mockDict.setDictType("third_party_method_api");
        mockDict.setDictKey("user_controller.get_user");
        mockDict.setDictValue("com.etrx.kb.controller.UserController.getCurrentUserInfo");
        mockDict.setDictLabel("获取当前用户信息");
        mockDict.setParentId(null);
        mockDict.setNodeType(Dict.NodeType.CONFIG_ITEM);
        mockDict.setPathLevel(1);
        mockDict.setDescription("获取当前登录用户的详细信息");
        mockDict.setSortOrder(1);
        mockDict.setStatus(Dict.Status.ENABLED);
        mockDict.setDeleted(0);
        mockDict.setCreateTime(LocalDateTime.now());
        mockDict.setUpdateTime(LocalDateTime.now());
        
        Dict parentDict = new Dict();
        parentDict.setId(10L);
        parentDict.setDictType("third_party_method_api");
        parentDict.setDictKey("user_module");
        parentDict.setDictLabel("用户模块");
        parentDict.setParentId(null);
        parentDict.setNodeType(Dict.NodeType.DIRECTORY);
        parentDict.setPathLevel(1);
        parentDict.setSortOrder(1);
        parentDict.setStatus(Dict.Status.ENABLED);
        parentDict.setDeleted(0);
        
        Dict childDict = new Dict();
        childDict.setId(2L);
        childDict.setDictType("third_party_method_api");
        childDict.setDictKey("user_controller.update_user");
        childDict.setDictValue("com.etrx.kb.controller.UserController.updateUserInfo");
        childDict.setDictLabel("更新用户信息");
        childDict.setParentId(10L);
        childDict.setNodeType(Dict.NodeType.CONFIG_ITEM);
        childDict.setPathLevel(2);
        childDict.setSortOrder(2);
        childDict.setStatus(Dict.Status.ENABLED);
        childDict.setDeleted(0);
        
        mockDictList = Arrays.asList(parentDict, mockDict, childDict);
    }
    
    @Test
    void testGetEnabledDictList_Success() {
        // Given
        when(dictMapper.selectEnabledConfigsByType("third_party_method_api"))
            .thenReturn(mockDictList);
        
        // When
        List<DictVO> result = dictService.getEnabledDictList("third_party_method_api");
        
        // Then
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("user_module", result.get(0).getDictKey());
        verify(dictMapper, times(1)).selectEnabledConfigsByType("third_party_method_api");
    }
    
    @Test
    void testGetEnabledDictList_EmptyType() {
        // When
        List<DictVO> result = dictService.getEnabledDictList("");
        
        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(dictMapper, never()).selectEnabledConfigsByType(anyString());
    }
    
    @Test
    void testGetAllDictList_Success() {
        // Given
        when(dictMapper.selectAllByType("third_party_method_api"))
            .thenReturn(mockDictList);
        
        // When
        List<DictVO> result = dictService.getAllDictList("third_party_method_api");
        
        // Then
        assertNotNull(result);
        assertEquals(3, result.size());
        verify(dictMapper, times(1)).selectAllByType("third_party_method_api");
    }
    
    @Test
    void testGetDictTree_Success() {
        // Given
        when(dictMapper.selectEnabledConfigsByType("third_party_method_api"))
            .thenReturn(mockDictList);
        
        // When
        List<DictVO> result = dictService.getDictTree("third_party_method_api", true);
        
        // Then
        assertNotNull(result);
        assertEquals(2, result.size()); // 只有两个根节点
        
        // 验证树形结构
        DictVO parentNode = result.stream()
            .filter(node -> node.getDictKey().equals("user_module"))
            .findFirst()
            .orElse(null);
        
        assertNotNull(parentNode);
        assertNotNull(parentNode.getChildren());
        assertEquals(1, parentNode.getChildren().size());
        assertEquals("user_controller.update_user", parentNode.getChildren().get(0).getDictKey());
    }
    
    @Test
    void testGetDictTree_EmptyList() {
        // Given
        when(dictMapper.selectAllByType("unknown_type"))
            .thenReturn(Collections.emptyList());
        
        // When
        List<DictVO> result = dictService.getDictTree("unknown_type", false);
        
        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
    
    @Test
    void testGetDictByTypeAndKey_Success() {
        // Given
        when(dictMapper.selectOne(any()))
            .thenReturn(mockDict);
        
        // When
        DictVO result = dictService.getDictByTypeAndKey("third_party_method_api", "user_controller.get_user");
        
        // Then
        assertNotNull(result);
        assertEquals("user_controller.get_user", result.getDictKey());
        assertEquals("获取当前用户信息", result.getDictLabel());
        verify(dictMapper, times(1)).selectOne(any());
    }
    
    @Test
    void testGetDictByTypeAndKey_NotFound() {
        // Given
        when(dictMapper.selectOne(any()))
            .thenReturn(null);
        
        // When
        DictVO result = dictService.getDictByTypeAndKey("third_party_method_api", "non_existent_key");
        
        // Then
        assertNull(result);
        verify(dictMapper, times(1)).selectOne(any());
    }
    
    @Test
    void testGetDictByTypeAndKey_InvalidParams() {
        // When - 空类型
        DictVO result1 = dictService.getDictByTypeAndKey("", "key");
        
        // Then
        assertNull(result1);
        verify(dictMapper, never()).selectOne(any());
        
        // When - 空键
        DictVO result2 = dictService.getDictByTypeAndKey("type", "");
        
        // Then
        assertNull(result2);
        verify(dictMapper, never()).selectOne(any());
    }
    
    @Test
    void testBuildTree_WithMultipleLevels() {
        // Given - 构建三层结构
        Dict level1 = new Dict();
        level1.setId(1L);
        level1.setDictKey("level1");
        level1.setParentId(null);
        level1.setSortOrder(1);
        
        Dict level2 = new Dict();
        level2.setId(2L);
        level2.setDictKey("level2");
        level2.setParentId(1L);
        level2.setSortOrder(2);
        
        Dict level3 = new Dict();
        level3.setId(3L);
        level3.setDictKey("level3");
        level3.setParentId(2L);
        level3.setSortOrder(3);
        
        List<Dict> multiLevelList = Arrays.asList(level3, level1, level2); // 故意打乱顺序
        
        when(dictMapper.selectAllByType("multi_level"))
            .thenReturn(multiLevelList);
        
        // When
        List<DictVO> result = dictService.getDictTree("multi_level", false);
        
        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        DictVO root = result.get(0);
        assertEquals("level1", root.getDictKey());
        assertNotNull(root.getChildren());
        assertEquals(1, root.getChildren().size());
        
        DictVO child = root.getChildren().get(0);
        assertEquals("level2", child.getDictKey());
        assertNotNull(child.getChildren());
        assertEquals(1, child.getChildren().size());
        
        DictVO grandchild = child.getChildren().get(0);
        assertEquals("level3", grandchild.getDictKey());
    }
    
    @Test
    void testGetDictByKey_Success() {
        // Given
        when(dictMapper.selectEnabledByKey("AI_EMBEDDING_MODEL"))
            .thenReturn(mockDict);
        
        // When
        DictVO result = dictService.getDictByKey("AI_EMBEDDING_MODEL");
        
        // Then
        assertNotNull(result);
        assertEquals("user_controller.get_user", result.getDictKey());
        assertEquals("获取当前用户信息", result.getDictLabel());
        verify(dictMapper, times(1)).selectEnabledByKey("AI_EMBEDDING_MODEL");
    }
    
    @Test
    void testGetDictByKey_NotFound() {
        // Given
        when(dictMapper.selectEnabledByKey("non_existent_key"))
            .thenReturn(null);
        
        // When
        DictVO result = dictService.getDictByKey("non_existent_key");
        
        // Then
        assertNull(result);
        verify(dictMapper, times(1)).selectEnabledByKey("non_existent_key");
    }
    
    @Test
    void testGetDictByKey_EmptyKey() {
        // When
        DictVO result = dictService.getDictByKey("");
        
        // Then
        assertNull(result);
        verify(dictMapper, never()).selectEnabledByKey(anyString());
        
        // When - null key
        DictVO result2 = dictService.getDictByKey(null);
        
        // Then
        assertNull(result2);
        verify(dictMapper, never()).selectEnabledByKey(anyString());
    }
} 