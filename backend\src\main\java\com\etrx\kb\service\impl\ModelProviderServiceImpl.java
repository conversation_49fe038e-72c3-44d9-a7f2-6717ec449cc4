package com.etrx.kb.service.impl;

import com.etrx.kb.config.ModelConfig;
import com.etrx.kb.config.OllamaMCPChatModel;
import com.etrx.kb.service.ModelProviderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 模型提供商服务实现类
 */
@Slf4j
@Service
public class ModelProviderServiceImpl implements ModelProviderService {

    private final Map<String, ChatModel> chatModelMap = new HashMap<>();
    private final Set<String> availableProviders = new HashSet<>();

    @Autowired
    private ModelConfig modelConfig;

    // 注入Ollama ChatModel
    @Autowired(required = false)
    private OllamaMCPChatModel ollamaMcpChatModel;

    // 其他模型可以通过ChatModel接口注入
    @Autowired(required = false)
    private List<ChatModel> allChatModels;

    /**
     * 初始化方法，在Bean创建后执行
     */
    @jakarta.annotation.PostConstruct
    public void initializeProviders() {
        log.info("初始化AI模型提供商...");

        if (modelConfig.getProviders() == null) {
            log.warn("未找到模型提供商配置");
            return;
        }

        // 初始化Ollama
        ModelConfig.ProviderConfig ollamaConfig = modelConfig.getProviders().get("Ollama");
        if (ollamaConfig != null && ollamaConfig.getEnabled() && ollamaMcpChatModel != null) {
            chatModelMap.put("Ollama", ollamaMcpChatModel);
            availableProviders.add("Ollama");
            log.info("Ollama模型提供商已启用");
        }

        // 初始化其他模型（如果有Spring AI自动配置的ChatModel）
        if (allChatModels != null) {
            for (ChatModel chatModel : allChatModels) {
                String modelClassName = chatModel.getClass().getSimpleName();
                
                // 尝试识别OpenAI模型
                ModelConfig.ProviderConfig openAiConfig = modelConfig.getProviders().get("OpenAI");
                if (openAiConfig != null && openAiConfig.getEnabled() && 
                    modelClassName.contains("OpenAi") && !modelClassName.contains("Azure")) {
                    chatModelMap.put("OpenAI", chatModel);
                    availableProviders.add("OpenAI");
                    log.info("OpenAI模型提供商已启用: {}", modelClassName);
                }
                
                // 尝试识别Azure OpenAI模型
                ModelConfig.ProviderConfig azureConfig = modelConfig.getProviders().get("AzureOpenAI");
                if (azureConfig != null && azureConfig.getEnabled() && modelClassName.contains("Azure")) {
                    chatModelMap.put("AzureOpenAI", chatModel);
                    availableProviders.add("AzureOpenAI");
                    log.info("Azure OpenAI模型提供商已启用: {}", modelClassName);
                }
            }
        }

        log.info("已初始化的模型提供商: {}", availableProviders);
        log.info("默认模型提供商: {}", getDefaultProvider());
    }

    @Override
    public ChatModel getChatModel(String providerName) {
        if (!isProviderAvailable(providerName)) {
            log.warn("请求的模型提供商不可用: {}, 回退到默认提供商: {}", providerName, getDefaultProvider());
            return getDefaultChatModel();
        }

        ChatModel chatModel = chatModelMap.get(providerName);
        if (chatModel == null) {
            log.warn("找不到模型提供商: {}, 回退到默认提供商: {}", providerName, getDefaultProvider());
            return getDefaultChatModel();
        }

        log.debug("获取模型提供商: {}", providerName);
        return chatModel;
    }

    @Override
    public ChatModel getChatModel(String providerName, String modelName) {
        // 注意：Spring AI的ChatModel通常在创建时就绑定了特定模型
        // 这里先获取提供商的ChatModel，后续可以考虑扩展为支持动态模型切换
        log.debug("获取模型提供商: {}, 模型: {}", providerName, modelName);
        return getChatModel(providerName);
    }

    @Override
    public ChatModel getDefaultChatModel() {
        String defaultProvider = getDefaultProvider();
        
        // 首先尝试获取配置的默认提供商
        if (isProviderAvailable(defaultProvider)) {
            ChatModel defaultChatModel = chatModelMap.get(defaultProvider);
            if (defaultChatModel != null) {
                log.debug("使用默认模型提供商: {}", defaultProvider);
                return defaultChatModel;
            }
        }

        // 如果默认提供商不可用，按优先级顺序选择
        String[] priorityProviders = {"Ollama", "OpenAI", "AzureOpenAI"};
        for (String provider : priorityProviders) {
            if (isProviderAvailable(provider)) {
                ChatModel chatModel = chatModelMap.get(provider);
                if (chatModel != null) {
                    log.warn("默认提供商不可用，使用备选提供商: {}", provider);
                    return chatModel;
                }
            }
        }

        // 如果所有提供商都不可用，抛出异常
        throw new IllegalStateException("没有可用的AI模型提供商。请检查配置并确保至少启用一个提供商。");
    }

    @Override
    public boolean isProviderAvailable(String providerName) {
        return availableProviders.contains(providerName);
    }

    @Override
    public String[] getAvailableProviders() {
        return availableProviders.toArray(new String[0]);
    }

    /**
     * 获取默认提供商名称
     */
    private String getDefaultProvider() {
        return modelConfig.getDefaultProvider() != null ? 
               modelConfig.getDefaultProvider() : "Ollama";
    }

    /**
     * 获取提供商信息（用于调试）
     */
    public Map<String, Object> getProviderInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("defaultProvider", getDefaultProvider());
        info.put("defaultModel", modelConfig.getDefaultModel());
        info.put("availableProviders", availableProviders);
        
        Map<String, Boolean> enabledProviders = new HashMap<>();
        if (modelConfig.getProviders() != null) {
            modelConfig.getProviders().forEach((name, config) -> 
                enabledProviders.put(name.toLowerCase(), config.getEnabled()));
        }
        info.put("enabledProviders", enabledProviders);
        
        return info;
    }
} 