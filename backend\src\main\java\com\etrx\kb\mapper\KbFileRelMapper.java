package com.etrx.kb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.etrx.kb.domain.KbFileRel;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface KbFileRelMapper extends BaseMapper<KbFileRel> {
    
    /**
     * 查询文件和知识库的关联关系（包括已删除的记录）
     * @param fileNodeId 文件节点ID
     * @param kbIds 知识库ID列表
     * @return 关联关系列表
     */
    @Select("""
            <script>
            SELECT * FROM tb_kb_file_rel 
            WHERE file_node_id = #{fileNodeId}
            AND kb_id IN 
            <foreach collection='kbIds' item='item' open='(' separator=',' close=')'>
                #{item}
            </foreach>
            </script>
            """)
    List<KbFileRel> selectByFileNodeIdAndKbIds(@Param("fileNodeId") Long fileNodeId, @Param("kbIds") List<Long> kbIds);
    
    /**
     * 硬删除关联关系记录
     * @param id 关联关系ID
     * @return 删除的记录数
     */
    @Delete("DELETE FROM tb_kb_file_rel WHERE id = #{id}")
    int hardDeleteById(@Param("id") Long id);
} 