package com.etrx.kb.controller;

import com.etrx.kb.common.PageResult;
import com.etrx.kb.common.Result;
import com.etrx.kb.dto.UserInfoDTO;
import com.etrx.kb.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 用户控制器
 */
@RestController
@RequestMapping("/aikb/users")
@RequiredArgsConstructor
public class UserController {

    private final UserService userService;

    /**
     * 获取当前用户信息
     */
    @GetMapping("/me")
    public Result<UserInfoDTO> getCurrentUserInfo() {
        return Result.success(userService.getCurrentUserInfo());
    }

    /**
     * 更新当前用户信息
     */
    @PutMapping("/me")
    public Result<UserInfoDTO> updateCurrentUserInfo(@RequestBody UserInfoDTO userDTO) {
        return Result.success(userService.updateCurrentUserInfo(userDTO));
    }

    /**
     * 获取指定用户信息
     */
    @GetMapping("/{id}")
    @PreAuthorize("@userPermission.canViewUser(#id)")
    public Result<UserInfoDTO> getUserInfo(@PathVariable Long id) {
        return Result.success(userService.getUserInfo(id));
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/{id}")
    @PreAuthorize("@userPermission.canUpdateUser(#id)")
    public Result<UserInfoDTO> updateUserInfo(@PathVariable Long id, @RequestBody UserInfoDTO userDTO) {
        return Result.success(userService.updateUserInfo(id, userDTO));
    }

    /**
     * 修改密码
     */
    @PutMapping("/{id}/password")
    @PreAuthorize("@userPermission.canChangePassword(#id)")
    public Result<?> changePassword(@PathVariable Long id, 
                                    @RequestParam String oldPassword, 
                                    @RequestParam String newPassword) {
        userService.changePassword(id, oldPassword, newPassword);
        return Result.success();
    }

    /**
     * 分页查询用户列表
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    public Result<PageResult<UserInfoDTO>> listUsers(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String keyword) {
        return Result.success(userService.listUsers(pageNum, pageSize, keyword));
    }

    /**
     * 更新用户状态
     */
    @PutMapping("/{id}/status")
    @PreAuthorize("@userPermission.canUpdateStatus(#id)")
    public Result<?> updateStatus(@PathVariable Long id, @RequestParam Integer status) {
        userService.updateStatus(id, status);
        return Result.success();
    }

    /**
     * 更新用户角色
     */
    @PutMapping("/{id}/role")
    @PreAuthorize("@userPermission.canUpdateRole(#id, #role)")
    public Result<?> updateRole(@PathVariable Long id, @RequestParam Integer role) {
        userService.updateRole(id, role);
        return Result.success();
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("@userPermission.canDeleteUser(#id)")
    public Result<?> deleteUser(@PathVariable Long id) {
        userService.deleteUser(id);
        return Result.success();
    }
}