import {
  LlmModelType,
  ModelVariableType,
  settledModelVariableMap,
} from '@/constants/knowledge';
import { Flex, Form, InputNumber, Select, Slider, Switch, Tooltip } from 'antd';
import camelCase from 'lodash/camelCase';

import { useTranslate } from '@/hooks/common-hooks';
import { useComposeLlmOptionsByModelTypes } from '@/hooks/llm-hooks';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { useCallback, useMemo, useEffect } from 'react';
import styles from './index.less';

interface IProps {
  prefix?: string;
  formItemLayout?: any;
  handleParametersChange?(value: ModelVariableType): void;
  onChange?(value: string, option: any): void;
}

const LlmSettingItems = ({ prefix, formItemLayout = {}, onChange }: IProps) => {
  const form = Form.useFormInstance();
  const { t } = useTranslate('chat');
  const parameterOptions = Object.values(ModelVariableType).map((x) => ({
    label: t(camelCase(x)),
    value: x,
  }));

  const handleParametersChange = useCallback(
    (value: ModelVariableType) => {
      const variable = settledModelVariableMap[value];
      let nextVariable: Record<string, any> = variable;
      if (prefix) {
        nextVariable = { [prefix]: variable };
      }
      form.setFieldsValue(nextVariable);
    },
    [form, prefix],
  );

  const memorizedPrefix = useMemo(() => (prefix ? [prefix] : []), [prefix]);

  const modelOptions = useComposeLlmOptionsByModelTypes([
    LlmModelType.Chat,
    LlmModelType.Image2text,
  ]);
  // console.log(1111, modelOptions, onChange)
  // debugger;
  useEffect(() => {
    if(modelOptions && modelOptions.length && !form.getFieldValue('llm_id')) {
      form.setFieldValue('llm_id', modelOptions[0].options[0].value as string);
    }
  }, [modelOptions, form])

  return (
    <>
      <Form.Item
        label={t('model')}
        name="llm_id"
        tooltip={t('modelTip')}
        {...formItemLayout}
        rules={[{ required: true, message: t('modelMessage') }]}
      >
        <Select
          options={modelOptions}
          showSearch
          popupMatchSelectWidth={false}
          onChange={onChange}
        />
      </Form.Item>
      <div className="tw-border tw-rounded-md">
        <div className="tw-flex tw-justify-between tw-bg-slate-100 tw-p-2 tw-mb-2">
          <div className="tw-space-x-1 tw-items-center">
            <span className="tw-text-lg tw-font-semibold">{t('freedom')}</span>
            <Tooltip title={t('freedomTip')}>
              <QuestionCircleOutlined></QuestionCircleOutlined>
            </Tooltip>
          </div>
          <div className="tw-w-1/4 tw-min-w-32">
            <Form.Item
              label={t('freedom')}
              name="parameter"
              tooltip={t('freedomTip')}
              initialValue={ModelVariableType.Precise}
              labelCol={{ span: 0 }}
              wrapperCol={{ span: 24 }}
              className="tw-m-0"
            >
              <Select<ModelVariableType>
                options={parameterOptions}
                onChange={handleParametersChange}
              />
            </Form.Item>
          </div>
        </div>

        <div className="tw-pr-2">
          <Form.Item
            label={t('temperature')}
            tooltip={t('temperatureTip')}
            {...formItemLayout}
          >
            <Flex gap={20} align="center">
              <Form.Item
                name={'temperatureEnabled'}
                valuePropName="checked"
                noStyle
              >
                <Switch size="small" />
              </Form.Item>
              <Form.Item noStyle dependencies={['temperatureEnabled']}>
                {({ getFieldValue }) => {
                  const disabled = !getFieldValue('temperatureEnabled');
                  return (
                    <>
                      <Flex flex={1}>
                        <Form.Item
                          name={[...memorizedPrefix, 'temperature']}
                          noStyle
                        >
                          <Slider
                            className={styles.variableSlider}
                            max={1}
                            step={0.01}
                            disabled={disabled}
                          />
                        </Form.Item>
                      </Flex>
                      <Form.Item
                        name={[...memorizedPrefix, 'temperature']}
                        noStyle
                      >
                        <InputNumber
                          className={styles.sliderInputNumber}
                          max={1}
                          min={0}
                          step={0.01}
                          disabled={disabled}
                        />
                      </Form.Item>
                    </>
                  );
                }}
              </Form.Item>
            </Flex>
          </Form.Item>
          <Form.Item
            label={t('topP')}
            tooltip={t('topPTip')}
            {...formItemLayout}
          >
            <Flex gap={20} align="center">
              <Form.Item name={'topPEnabled'} valuePropName="checked" noStyle>
                <Switch size="small" />
              </Form.Item>
              <Form.Item noStyle dependencies={['topPEnabled']}>
                {({ getFieldValue }) => {
                  const disabled = !getFieldValue('topPEnabled');
                  return (
                    <>
                      <Flex flex={1}>
                        <Form.Item name={[...memorizedPrefix, 'top_p']} noStyle>
                          <Slider
                            className={styles.variableSlider}
                            max={1}
                            step={0.01}
                            disabled={disabled}
                          />
                        </Form.Item>
                      </Flex>
                      <Form.Item name={[...memorizedPrefix, 'top_p']} noStyle>
                        <InputNumber
                          className={styles.sliderInputNumber}
                          max={1}
                          min={0}
                          step={0.01}
                          disabled={disabled}
                        />
                      </Form.Item>
                    </>
                  );
                }}
              </Form.Item>
            </Flex>
          </Form.Item>
          <Form.Item
            label={t('presencePenalty')}
            tooltip={t('presencePenaltyTip')}
            {...formItemLayout}
          >
            <Flex gap={20} align="center">
              <Form.Item
                name={'presencePenaltyEnabled'}
                valuePropName="checked"
                noStyle
              >
                <Switch size="small" />
              </Form.Item>
              <Form.Item noStyle dependencies={['presencePenaltyEnabled']}>
                {({ getFieldValue }) => {
                  const disabled = !getFieldValue('presencePenaltyEnabled');
                  return (
                    <>
                      <Flex flex={1}>
                        <Form.Item
                          name={[...memorizedPrefix, 'presence_penalty']}
                          noStyle
                        >
                          <Slider
                            className={styles.variableSlider}
                            max={1}
                            step={0.01}
                            disabled={disabled}
                          />
                        </Form.Item>
                      </Flex>
                      <Form.Item
                        name={[...memorizedPrefix, 'presence_penalty']}
                        noStyle
                      >
                        <InputNumber
                          className={styles.sliderInputNumber}
                          max={1}
                          min={0}
                          step={0.01}
                          disabled={disabled}
                        />
                      </Form.Item>
                    </>
                  );
                }}
              </Form.Item>
            </Flex>
          </Form.Item>
          <Form.Item
            label={t('frequencyPenalty')}
            tooltip={t('frequencyPenaltyTip')}
            {...formItemLayout}
          >
            <Flex gap={20} align="center">
              <Form.Item
                name={'frequencyPenaltyEnabled'}
                valuePropName="checked"
                noStyle
              >
                <Switch size="small" />
              </Form.Item>
              <Form.Item noStyle dependencies={['frequencyPenaltyEnabled']}>
                {({ getFieldValue }) => {
                  const disabled = !getFieldValue('frequencyPenaltyEnabled');
                  return (
                    <>
                      <Flex flex={1}>
                        <Form.Item
                          name={[...memorizedPrefix, 'frequency_penalty']}
                          noStyle
                        >
                          <Slider
                            className={styles.variableSlider}
                            max={1}
                            step={0.01}
                            disabled={disabled}
                          />
                        </Form.Item>
                      </Flex>
                      <Form.Item
                        name={[...memorizedPrefix, 'frequency_penalty']}
                        noStyle
                      >
                        <InputNumber
                          className={styles.sliderInputNumber}
                          max={1}
                          min={0}
                          step={0.01}
                          disabled={disabled}
                        />
                      </Form.Item>
                    </>
                  );
                }}
              </Form.Item>
            </Flex>
          </Form.Item>
        </div>
      </div>
    </>
  );
};

export default LlmSettingItems;
