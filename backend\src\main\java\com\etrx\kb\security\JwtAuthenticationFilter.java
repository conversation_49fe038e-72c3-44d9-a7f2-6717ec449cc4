package com.etrx.kb.security;

import com.etrx.kb.util.JwtTokenUtil;
import com.etrx.kb.service.UserService;
import com.etrx.kb.service.ApiKeyService;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.MalformedJwtException;
import io.jsonwebtoken.UnsupportedJwtException;
import io.jsonwebtoken.security.SignatureException;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

/**
 * JWT认证过滤器
 */
@Slf4j
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private final JwtTokenUtil jwtTokenUtil;
    private final CustomUserDetailsService userDetailsService;
    private final UserService userService;
    private final ApiKeyService apiKeyService;

    public JwtAuthenticationFilter(JwtTokenUtil jwtTokenUtil,
                                   CustomUserDetailsService userDetailsService,
                                   @Lazy UserService userService,
                                   @Lazy ApiKeyService apiKeyService) {
        this.jwtTokenUtil = jwtTokenUtil;
        this.userDetailsService = userDetailsService;
        this.userService = userService;
        this.apiKeyService = apiKeyService;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        
        // 检查是否有X-API-Key请求头（直接API密钥认证）
        String apiKey = request.getHeader("X-API-Key");
        if (StringUtils.hasText(apiKey)) {
            if (handleApiKeyAuthentication(request, apiKey)) {
                filterChain.doFilter(request, response);
                return;
            }
        }
        
        // 从请求头中获取Authorization（JWT认证）
        String authHeader = request.getHeader("Authorization");
        String username = null;
        String jwtToken = null;

        // 检查Authorization头是否存在且格式正确
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            jwtToken = authHeader.substring(7);
            try {
                // 判断是否为第三方token
                if (jwtTokenUtil.isThirdPartyToken(jwtToken)) {
                    handleThirdPartyToken(jwtToken, request);
                } else {
                    // 处理本系统token
                    handleSystemToken(jwtToken, request);
                }
            } catch (ExpiredJwtException e) {
                log.error("JWT令牌已过期", e);
            } catch (SignatureException e) {
                log.error("无效的JWT签名", e);
            } catch (MalformedJwtException e) {
                log.error("无效的JWT令牌", e);
            } catch (UnsupportedJwtException e) {
                log.error("不支持的JWT令牌", e);
            } catch (IllegalArgumentException e) {
                log.error("JWT令牌为空", e);
            }
        }

        filterChain.doFilter(request, response);
    }

    /**
     * 处理API密钥认证（直接认证方式）
     */
    private boolean handleApiKeyAuthentication(HttpServletRequest request, String apiKey) {
        try {
            // 验证API密钥
            if (!apiKeyService.validateApiKey(apiKey)) {
                log.warn("无效的API密钥: {}", apiKey.substring(0, Math.min(8, apiKey.length())) + "...");
                return false;
            }

            // 获取用户信息
            String username = request.getHeader("X-User-Name");
            String employeeNo = request.getHeader("X-Employee-No");
            String userUuid = request.getHeader("X-User-UUID");

            // 只校验 username 和 employeeNo 为必填，userUuid 改为可选
            if (!StringUtils.hasText(username) || !StringUtils.hasText(employeeNo)) {
                log.warn("API密钥认证缺少必要的用户信息头: username={}, employeeNo={}", 
                        username, employeeNo);
                return false;
            }

            // 查找或创建用户
            var user = userService.getUserByEmployeeNo(employeeNo);
            if (user == null) {
                // 自动创建用户
                var userInfo = new com.etrx.kb.dto.ThirdPartyLoginDTO.UserInfo();
                userInfo.setUsername(username);
                userInfo.setEmployeeNo(employeeNo);
                // userUuid 可为空，只有在有值时才设置
                if (StringUtils.hasText(userUuid)) {
                    userInfo.setUserUuid(userUuid);
                }
                userInfo.setNickname(request.getHeader("X-User-Nickname"));
                userInfo.setEmail(request.getHeader("X-User-Email"));
                userInfo.setPhone(request.getHeader("X-User-Phone"));
                userInfo.setAvatar(request.getHeader("X-User-Avatar"));

                userService.thirdPartyLogin(apiKey, userInfo);
                user = userService.getUserByEmployeeNo(employeeNo);
                log.info("API密钥认证：自动创建用户 - 工号: {}", employeeNo);
            }

            // 设置认证信息
            if (SecurityContextHolder.getContext().getAuthentication() == null) {
                UserDetails userDetails = userDetailsService.loadUserByUsername(user.getUsername());
                UsernamePasswordAuthenticationToken authenticationToken =
                        new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
                authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                SecurityContextHolder.getContext().setAuthentication(authenticationToken);

                // 标记为API密钥认证，用于白名单识别
                request.setAttribute("API_KEY_AUTH", true);
                request.setAttribute("API_KEY", apiKey);
                
                log.debug("API密钥认证成功: 用户={}, 工号={}", username, employeeNo);
            }

            return true;
        } catch (Exception e) {
            log.error("API密钥认证处理失败", e);
            return false;
        }
    }

    /**
     * 处理第三方token
     */
    private void handleThirdPartyToken(String jwtToken, HttpServletRequest request) {
        try {
            // 获取工号和API密钥
            String employeeNo = jwtTokenUtil.getEmployeeNoFromToken(jwtToken);
            String apiKey = jwtTokenUtil.getApiKeyFromToken(jwtToken);

            // 验证token是否过期
            if (jwtTokenUtil.isTokenExpired(jwtToken)) {
                log.error("第三方JWT令牌已过期");
                return;
            }

            // 验证API密钥是否有效
            if (!apiKeyService.validateApiKey(apiKey)) {
                log.error("第三方JWT令牌中的API密钥无效");
                return;
            }

            // 根据工号查询用户
            var user = userService.getUserByEmployeeNo(employeeNo);
            if (user == null) {
                log.error("第三方用户不存在：{}", employeeNo);
                return;
            }

            // 如果SecurityContext中没有认证信息，设置认证
            if (SecurityContextHolder.getContext().getAuthentication() == null) {
                // 为第三方用户创建UserDetails
                UserDetails userDetails = userDetailsService.loadUserByUsername(user.getUsername());

                // 创建认证令牌
                UsernamePasswordAuthenticationToken authenticationToken =
                        new UsernamePasswordAuthenticationToken(
                                userDetails,
                                null,
                                userDetails.getAuthorities()
                        );
                authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

                // 设置认证信息到SecurityContext
                SecurityContextHolder.getContext().setAuthentication(authenticationToken);
            }
        } catch (Exception e) {
            log.error("处理第三方token失败", e);
        }
    }

    /**
     * 处理本系统token
     */
    private void handleSystemToken(String jwtToken, HttpServletRequest request) {
        try {
            String username = jwtTokenUtil.getUsernameFromToken(jwtToken);

            // 如果用户名不为空且SecurityContext中没有认证信息
            if (username != null && SecurityContextHolder.getContext().getAuthentication() == null) {
                // 加载用户详情
                UserDetails userDetails = userDetailsService.loadUserByUsername(username);

                // 验证令牌
                if (jwtTokenUtil.validateToken(jwtToken, userDetails)) {
                    // 创建认证令牌
                    UsernamePasswordAuthenticationToken authenticationToken =
                            new UsernamePasswordAuthenticationToken(
                                    userDetails,
                                    null,
                                    userDetails.getAuthorities()
                            );
                    authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

                    // 设置认证信息到SecurityContext
                    SecurityContextHolder.getContext().setAuthentication(authenticationToken);
                }
            }
        } catch (Exception e) {
            log.error("处理系统token失败", e);
        }
    }
}