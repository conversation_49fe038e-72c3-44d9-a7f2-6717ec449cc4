CREATE TABLE `operation_log` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `user_id` BIGINT NOT NULL COMMENT '操作人ID',
  `username` VARCHAR(64) NOT NULL COMMENT '操作人用户名',
  `action_type` VARCHAR(32) NOT NULL COMMENT '操作类型',
  `object_type` VARCHAR(32) NOT NULL COMMENT '对象类型',
  `object_id` BIGINT DEFAULT NULL COMMENT '对象ID',
  `object_name` VARCHAR(128) DEFAULT NULL COMMENT '对象名称',
  `description` VARCHAR(255) DEFAULT NULL COMMENT '操作描述',
  `ip` VARCHAR(64) DEFAULT NULL COMMENT '操作IP',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4; 