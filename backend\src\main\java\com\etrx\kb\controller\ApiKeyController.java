package com.etrx.kb.controller;

import com.etrx.kb.common.PageResult;
import com.etrx.kb.common.Result;
import com.etrx.kb.dto.ApiKeyDTO;
import com.etrx.kb.service.ApiKeyService;
import com.etrx.kb.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * API密钥控制器
 */
@RestController
@RequestMapping("/aikb/api-keys")
@RequiredArgsConstructor
public class ApiKeyController {

    private final ApiKeyService apiKeyService;
    private final SecurityUtils securityUtils;

    /**
     * 创建API密钥
     */
    @PostMapping
    @PreAuthorize("hasRole('USER')")
    public Result<ApiKeyDTO> createApiKey(@RequestBody ApiKeyDTO apiKeyDTO) {
        return Result.success(apiKeyService.createApiKey(apiKeyDTO.getAppName(), securityUtils.getCurrentUserId()));
    }

    /**
     * 获取API密钥信息
     */
    @GetMapping("/{id}")
    @PreAuthorize("@apiKeyPermission.check(#id, T(com.etrx.kb.common.Constants$UserRole).ADMIN)")
    public Result<ApiKeyDTO> getApiKey(@PathVariable Long id) {
        return Result.success(apiKeyService.getApiKey(id));
    }

    /**
     * 删除API密钥
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("@apiKeyPermission.check(#id, T(com.etrx.kb.common.Constants$UserRole).ADMIN)")
    public Result<?> deleteApiKey(@PathVariable Long id) {
        apiKeyService.deleteApiKey(id, securityUtils.getCurrentUserId());
        return Result.success();
    }

    /**
     * 更新API密钥状态
     */
    @PutMapping("/{id}/status")
    @PreAuthorize("@apiKeyPermission.check(#id, T(com.etrx.kb.common.Constants$UserRole).ADMIN)")
    public Result<?> updateStatus(@PathVariable Long id, @RequestParam Integer status) {
        apiKeyService.updateStatus(id, status, securityUtils.getCurrentUserId());
        return Result.success();
    }

    /**
     * 分页查询API密钥列表
     */
    @GetMapping
    public Result<PageResult<ApiKeyDTO>> listApiKeys(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String keyword) {
        return Result.success(apiKeyService.listApiKeys(pageNum, pageSize, keyword, securityUtils.getCurrentUserId()));
    }

    /**
     * 获取用户的所有API密钥
     */
    @GetMapping("/my")
    @PreAuthorize("@apiKeyPermission.check(#id, T(com.etrx.kb.common.Constants$UserRole).ADMIN)")
    public Result<List<ApiKeyDTO>> getUserApiKeys() {
        return Result.success(apiKeyService.getUserApiKeys(securityUtils.getCurrentUserId()));
    }
}