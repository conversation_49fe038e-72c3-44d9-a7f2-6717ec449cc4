package com.etrx.kb.controller;

import com.etrx.kb.common.Constants;
import com.etrx.kb.common.PageResult;
import com.etrx.kb.common.Result;
import com.etrx.kb.dto.KnowledgeBaseDTO;
import com.etrx.kb.dto.KnowledgeBaseMemberDTO;
import com.etrx.kb.service.KnowledgeBaseService;
import com.etrx.kb.util.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

/**
 * 知识库控制器
 */
@RestController
@RequestMapping("/aikb/knowledge-bases")
@RequiredArgsConstructor
@Tag(name = "知识库管理", description = "知识库及其成员的管理接口")
public class KnowledgeBaseController {

    private final KnowledgeBaseService knowledgeBaseService;
    private final SecurityUtils securityUtils;

    /**
     * 创建知识库
     */
    @Operation(summary = "创建知识库", description = "创建一个新的知识库")
    @PostMapping
    @PreAuthorize("@userPermission.checkRole(T(com.etrx.kb.common.Constants$UserRole).ADMIN)")
    public Result<KnowledgeBaseDTO> createKnowledgeBase(
            @Parameter(description = "知识库信息") @RequestBody KnowledgeBaseDTO dto) {
        return Result.success(knowledgeBaseService.createKnowledgeBase(securityUtils.getCurrentUserId(), dto));
    }

    /**
     * 更新知识库
     */
    @Operation(summary = "更新知识库", description = "更新指定知识库的信息")
    @PutMapping("/{id}")
    @PreAuthorize("@knowledgeBasePermission.check(#id, T(com.etrx.kb.common.Constants$KnowledgeBaseMemberRole).ADMIN)")
    public Result<KnowledgeBaseDTO> updateKnowledgeBase(
            @Parameter(description = "知识库ID") @PathVariable Long id,
            @Parameter(description = "更新的知识库信息") @RequestBody KnowledgeBaseDTO dto) {
        return Result.success(knowledgeBaseService.updateKnowledgeBase(id, dto));
    }

    /**
     * 获取知识库信息
     */
    @Operation(summary = "获取知识库信息", description = "获取指定知识库的详细信息")
    @GetMapping("/{id}")
    public Result<KnowledgeBaseDTO> getKnowledgeBase(
            @Parameter(description = "知识库ID") @PathVariable Long id) {
        return Result.success(knowledgeBaseService.getKnowledgeBase(id));
    }

    /**
     * 删除知识库
     */
    @Operation(summary = "删除知识库", description = "删除指定的知识库")
    @DeleteMapping("/{id}")
    @PreAuthorize("@knowledgeBasePermission.check(#id, T(com.etrx.kb.common.Constants$KnowledgeBaseMemberRole).ADMIN)")
    public Result<?> deleteKnowledgeBase(
            @Parameter(description = "知识库ID") @PathVariable Long id) {
        knowledgeBaseService.deleteKnowledgeBase(id, securityUtils.getCurrentUserId());
        return Result.success();
    }

    /**
     * 分页查询知识库列表
     */
    @Operation(summary = "分页查询知识库列表", description = "根据条件分页查询知识库列表")
    @GetMapping
    public Result<PageResult<KnowledgeBaseDTO>> listKnowledgeBases(
            @Parameter(description = "页码，默认1") @RequestParam(defaultValue = "1") Integer pageNum,
            @Parameter(description = "每页大小，默认10") @RequestParam(defaultValue = "10") Integer pageSize,
            @Parameter(description = "搜索关键字") @RequestParam(required = false) String keyword,
            @Parameter(description = "知识库类型") @RequestParam(required = false) String type) {
        return Result.success(knowledgeBaseService.listKnowledgeBases(pageNum, pageSize, keyword, type, securityUtils.getCurrentUserId()));
    }

    /**
     * 获取用户有权限的知识库列表
     */
    @Operation(summary = "获取我的知识库列表", description = "获取当前用户有权限访问的所有知识库")
    @GetMapping("/my")
    public Result<List<KnowledgeBaseDTO>> getUserKnowledgeBases() {
        return Result.success(knowledgeBaseService.getUserKnowledgeBases(securityUtils.getCurrentUserId()));
    }

    /**
     * 添加知识库成员
     */
    @Operation(summary = "添加知识库成员", description = "向知识库添加新成员（需要管理员权限）")
    @PostMapping("/{kbId}/members")
    @PreAuthorize("@knowledgeBasePermission.check(#kbId, T(com.etrx.kb.common.Constants$KnowledgeBaseMemberRole).ADMIN)")
    public Result<?> addMember(
            @Parameter(description = "知识库ID") @PathVariable Long kbId,
            @Parameter(description = "成员信息") @RequestBody KnowledgeBaseMemberDTO dto) {
        dto.setKbId(kbId);
        knowledgeBaseService.addMember(securityUtils.getCurrentUserId(), dto);
        return Result.success();
    }

    /**
     * 更新知识库成员角色
     */
    @Operation(summary = "更新成员角色", description = "更新知识库成员的角色（需要管理员权限）")
    @PutMapping("/{kbId}/members/{userId}")
    @PreAuthorize("@knowledgeBasePermission.check(#kbId, T(com.etrx.kb.common.Constants$KnowledgeBaseMemberRole).ADMIN)")
    public Result<?> updateMemberRole(
            @Parameter(description = "知识库ID") @PathVariable Long kbId,
            @Parameter(description = "用户ID") @PathVariable Long userId,
            @Parameter(description = "更新的角色信息") @RequestBody KnowledgeBaseMemberDTO dto) {
        dto.setKbId(kbId);
        knowledgeBaseService.updateMemberRole(securityUtils.getCurrentUserId(), dto);
        return Result.success();
    }

    /**
     * 移除知识库成员
     */
    @Operation(summary = "移除知识库成员", description = "从知识库中移除指定成员（需要管理员权限）")
    @DeleteMapping("/{kbId}/members/{userId}")
    @PreAuthorize("@knowledgeBasePermission.check(#kbId, T(com.etrx.kb.common.Constants$KnowledgeBaseMemberRole).ADMIN)")
    public Result<?> removeMember(
            @Parameter(description = "知识库ID") @PathVariable Long kbId,
            @Parameter(description = "要移除的用户ID") @PathVariable Long userId) {
        knowledgeBaseService.removeMember(securityUtils.getCurrentUserId(), kbId, userId);
        return Result.success();
    }

    /**
     * 获取知识库成员列表
     */
    @Operation(summary = "获取成员列表", description = "获取知识库的所有成员列表")
    @GetMapping("/{kbId}/members")
    public Result<List<KnowledgeBaseMemberDTO>> listMembers(
            @Parameter(description = "知识库ID") @PathVariable Long kbId) {
        return Result.success(knowledgeBaseService.listMembers(kbId));
    }

    /**
     * 转移知识库所有权
     */
    @Operation(summary = "转移所有权", description = "转移知识库的所有权给其他用户（需要所有者权限）")
    @PutMapping("/{kbId}/transfer")
    @PreAuthorize("@knowledgeBasePermission.check(#kbId, T(com.etrx.kb.common.Constants$KnowledgeBaseMemberRole).OWNER)")
    public Result<?> transferOwnership(
            @Parameter(description = "知识库ID") @PathVariable Long kbId,
            @Parameter(description = "新所有者的用户ID") @RequestParam Long newOwnerId) {
        knowledgeBaseService.transferOwnership(kbId, securityUtils.getCurrentUserId(), newOwnerId);
        return Result.success();
    }

    /**
     * 更新知识库状态
     */
    @Operation(summary = "更新知识库状态", description = "更新知识库的状态（需要管理员权限）")
    @PutMapping("/{id}/status")
    @PreAuthorize("@knowledgeBasePermission.check(#id, T(com.etrx.kb.common.Constants$KnowledgeBaseMemberRole).ADMIN)")
    public Result<?> updateStatus(
            @Parameter(description = "知识库ID") @PathVariable Long id,
            @Parameter(description = "新状态值") @RequestParam Integer status) {
        knowledgeBaseService.updateStatus(id, status);
        return Result.success();
    }

    /**
     * 获取知识库ID和名称列表
     */
    @Operation(summary = "获取知识库ID和名称列表", description = "获取当前用户可访问的所有知识库的ID和名称")
    @GetMapping("/simple-list")
    public Result<List<KnowledgeBaseDTO>> listKnowledgeBaseNames(
            @Parameter(description = "搜索关键字") @RequestParam(required = false) String keyword) {
        return Result.success(knowledgeBaseService.listKnowledgeBaseNames(keyword, securityUtils.getCurrentUserId()));
    }
}