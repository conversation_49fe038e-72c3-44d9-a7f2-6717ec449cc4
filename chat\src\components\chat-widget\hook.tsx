import { Authorization } from '@/constants/authorization';
import { MessageType } from '@/constants/chat';
import { useTranslate } from '@/hooks/common-hooks';
import { ResponseType } from '@/interfaces/database/base';
import { IAnswer, IConversation, IDialog, Message } from '@/interfaces/database/chat';
import { IClientConversation, IMessage } from '@/pages/chat/interface';
import chatService, {
  getConversation,
  getDialog,
  listConversation,
  removeConversation,
  setConversation,
  updateConversation,
} from '@/services/chat-service';
import api from '@/utils/api';
import { getAuthorization } from '@/utils/authorization-util';
import {
  buildMessageListWithUuid,
  buildMessageUuid,
  isConversationIdExist,
} from '@/utils/chat';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { EventSourceParserStream } from 'eventsource-parser/stream';
import { omit } from 'lodash';
import trim from 'lodash/trim';
import {
  ChangeEventHandler,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
  useContext,
} from 'react';
import { v4 as uuid } from 'uuid';
import { useEmployeeContext } from '@/contexts/EmployeeContext';

interface DefaultDialog {
  id: string;
  ragflowChatAssistantId: string;
}

// export interface Message {
//   id: string;
//   role: 'user' | 'assistant';
//   content: string;
//   doc_ids?: string[];
// }

const useFetchDefaultDialog = () => {
  const {
    data,
    isFetching: loading,
    refetch,
  } = useQuery<DefaultDialog>({
    queryKey: ['fetchDialog'],
    gcTime: 0,
    initialData: {} as DefaultDialog,
    refetchOnWindowFocus: false,
    queryFn: async () => {
      const { data } = await chatService.defaultUserDialog();
      return data?.data ?? ({} as DefaultDialog);
    },
  });

  return { data, loading, refetch };
};

export const useHandleDialog = () => {
  const [dialogId, setDialogId] = useState<string>('');
  const { data, loading } = useFetchDefaultDialog();
  useEffect(() => {
    if (data?.ragflowChatAssistantId) {
      setDialogId(data?.ragflowChatAssistantId);
    }
  }, [data]);



  return {
    data,
    dialogId,
    setDialogId,
    loading,
  };
};

export const useHandleMessageInputChange = () => {
  const [value, setValue] = useState('');

  const handleInputChange: ChangeEventHandler<HTMLTextAreaElement> = (e) => {
    const value = e.target.value;
    // const nextValue = value.replaceAll('\\n', '\n').replaceAll('\\t', '\t');
    setValue(value);
  };

  return {
    handleInputChange,
    value,
    setValue,
  };
};

export const useSendButtonDisabled = (value: string) => {
  return trim(value) === '';
};

export const useScrollToBottom = (
  drawerVisible: boolean,
  messages?: unknown
) => {
  const ref = useRef<HTMLDivElement>(null);

  const scrollToBottom = useCallback(() => {
    if (messages) {
      ref.current?.scrollIntoView({ behavior: 'instant' });
    }
  }, [messages]); // If the message changes, scroll to the bottom

  useEffect(() => {
    scrollToBottom();
  }, [scrollToBottom, drawerVisible]);

  return ref;
};

export const useSendMessageWithSse = (
  url: string = api.completeConversation,
) => {
  const [answer, setAnswer] = useState<IAnswer>({} as IAnswer);
  const [done, setDone] = useState(true);
  const timer = useRef<any>();
  const sseRef = useRef<AbortController>();
  const employeeContext = useEmployeeContext();

  const initializeSseRef = useCallback(() => {
    sseRef.current = new AbortController();
  }, []);

  const resetAnswer = useCallback(() => {
    if (timer.current) {
      clearTimeout(timer.current);
    }
    timer.current = setTimeout(() => {
      setAnswer({} as IAnswer);
      clearTimeout(timer.current);
    }, 1000);
  }, []);

  const send = useCallback(
    async (
      body: any,
      controller?: AbortController,
    ): Promise<{ response: Response; data: ResponseType } | undefined> => {
      initializeSseRef();
      try {
        setDone(false);
        const headers: Record<string, string> = {
          [Authorization]: getAuthorization(),
          'Content-Type': 'application/json',
        };
        let requestUrl = url; // 使用临时变量保存请求地址
        if (employeeContext) {
          headers['X-User-Name'] = employeeContext.employeeName;
          headers['X-Employee-No'] = employeeContext.employeeNumber;
          if(employeeContext.apiKey) {
            headers['X-API-Key'] = employeeContext.apiKey;
          }
          if(employeeContext.apiHost) {
            requestUrl = `${employeeContext.apiHost}${requestUrl}`; // 修改临时变量
          }
        }

        const response = await fetch(requestUrl, {
          method: 'POST',
          headers,
          body: JSON.stringify(body),
          signal: controller?.signal || sseRef.current?.signal,
        });

        const res = response.clone().json();

        const reader = response?.body
          ?.pipeThrough(new TextDecoderStream())
          .pipeThrough(new EventSourceParserStream())
          .getReader();

        while (true) {
          const x = await reader?.read();
          if (x) {
            const { value } = x;
            const val = JSON.parse(value?.data || '');
            const done = val.finished;
            if (done) {
              console.info('done');
              if (val.error) {
                setAnswer({
                  ...val,
                  answer: val.error,
                  conversationId: body?.conversation_id,
                });
              }
              resetAnswer();
              break;
            }
            try {
              // const val = JSON.parse(value?.data || '');
              // const d = val?.data;
              // if (typeof d !== 'boolean') {
              setAnswer({
                ...val,
                conversationId: body?.conversation_id,
              });
              // }
            } catch (e) {
              console.warn(e);
            }
          }
        }
        console.info('done?');
        setDone(true);
        resetAnswer();
        return { data: await res, response };
      } catch (e) {
        setDone(true);
        resetAnswer();

        console.warn(e);
      }
    },
    [initializeSseRef, url, resetAnswer, employeeContext],
  );

  const stopOutputMessage = useCallback(() => {
    sseRef.current?.abort();
  }, []);

  return { send, answer, done, setDone, resetAnswer, stopOutputMessage };
};

export const useSelectDerivedMessages = (drawerVisible: boolean) => {
  const [derivedMessages, setDerivedMessages] = useState<IMessage[]>([]);

  const ref = useScrollToBottom(drawerVisible, derivedMessages);

  const addNewestQuestion = useCallback(
    (message: Message, answer: string = '') => {
      setDerivedMessages((pre) => {
        return [
          ...pre,
          {
            ...message,
            id: buildMessageUuid(message), // The message id is generated on the front end,
            // and the message id returned by the back end is the same as the question id,
            //  so that the pair of messages can be deleted together when deleting the message
          },
          {
            role: MessageType.Assistant,
            content: answer,
            id: buildMessageUuid({ ...message, role: MessageType.Assistant }),
          },
        ];
      });
    },
    [],
  );

  // Add the streaming message to the last item in the message list
  const addNewestAnswer = useCallback((answer: IAnswer) => {
    setDerivedMessages((pre) => {
      return [
        ...(pre?.slice(0, -1) ?? []),
        {
          role: MessageType.Assistant,
          content: answer.answer,
          reference: answer.reference,
          id: buildMessageUuid({
            id: answer.id,
            role: MessageType.Assistant,
          }),
          prompt: answer.prompt,
          audio_binary: answer.audio_binary,
          ...omit(answer, 'reference'),
        },
      ];
    });
  }, []);

  const removeLatestMessage = useCallback(() => {
    setDerivedMessages((pre) => {
      const nextMessages = pre?.slice(0, -2) ?? [];
      return nextMessages;
    });
  }, []);

  const removeMessageById = useCallback(
    (messageId: string) => {
      setDerivedMessages((pre) => {
        const nextMessages = pre?.filter((x) => x.id !== messageId) ?? [];
        return nextMessages;
      });
    },
    [setDerivedMessages],
  );

  const removeMessagesAfterCurrentMessage = useCallback(
    (messageId: string) => {
      setDerivedMessages((pre) => {
        const index = pre.findIndex((x) => x.id === messageId);
        if (index !== -1) {
          let nextMessages = pre.slice(0, index + 2) ?? [];
          const latestMessage = nextMessages.at(-1);
          nextMessages = latestMessage
            ? [
                ...nextMessages.slice(0, -1),
                {
                  ...latestMessage,
                  content: '',
                  reference: undefined,
                  prompt: undefined,
                },
              ]
            : nextMessages;
          return nextMessages;
        }
        return pre;
      });
    },
    [setDerivedMessages],
  );



  return {
    ref,
    derivedMessages,
    setDerivedMessages,
    addNewestQuestion,
    addNewestAnswer,
    removeLatestMessage,
    removeMessageById,
    removeMessagesAfterCurrentMessage,
  };
};

export const useFetchNextDialog = () => {
  const { dialogId } = useHandleDialog();
  const {
    data,
    isFetching: loading,
    refetch,
  } = useQuery<IDialog>({
    queryKey: ['fetchDialog', dialogId],
    gcTime: 0,
    initialData: {} as IDialog,
    enabled: !!dialogId,
    refetchOnWindowFocus: false,
    queryFn: async () => {
      const { data } = await getDialog(dialogId);
      data.data.prompt_config = data.data.prompt;
      data.data.prompt_config.system = data.data.prompt.prompt;
      data.data.prompt_config.emptyResponse = data.data.prompt.empty_response;
      data.data.prompt_config.prologue = data.data.prompt.opener;
      data.data.datasetIds = data.data.datasets?.map(
        (item: { id: string }) => item.id,
      );
      data.data.llm_setting = data.data.llm;
      data.data.llm_setting.frequencyPenalty = data.data.llm.frequency_penalty;
      data.data.llm_setting.topP = data.data.llm.top_p;
      data.data.llm_setting.presencePenalty = data.data.llm.presence_penalty;
      data.data.similarity_threshold = data.data.prompt.similarity_threshold;
      data.data.top_n = data.data.prompt.top_n;
      data.data.llm_id = data.data.llm?.model_name;

      return data?.data ?? ({} as IDialog);
    },
  });

  return { data, loading, refetch };
};
export const useFetchNextConversation = (conversationId: string) => {
  const {
    data,
    isFetching: loading,
    refetch,
  } = useQuery<IClientConversation>({
    queryKey: ['fetchConversation', conversationId],
    initialData: {} as IClientConversation,
    // enabled: isConversationIdExist(conversationId),
    gcTime: 0,
    refetchOnWindowFocus: false,
    queryFn: async () => {
      if (isConversationIdExist(conversationId)) {
        const { data } = await getConversation(conversationId);

        const conversation = data?.data ?? {};

        const messageList = buildMessageListWithUuid(conversation?.message);

        return { ...conversation, message: messageList };
      }
      return { message: [] };
    },
  });

  return { data, loading, refetch };
};
export const useUpdateNextConversation = () => {
  const queryClient = useQueryClient();
  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationKey: ['updateConversation'],
    mutationFn: async (params: Record<string, any>) => {
      const { data } = params.conversation_id
        ? await updateConversation(params.conversation_id, {
            chatAssistantId: params.dialog_id,
            sessionId: params.conversation_id,
            sessionName: params.name,
          })
        : await setConversation({
            chatAssistantId: params.dialog_id,
            sessionName: params.name,
          });
      if (data?.code === 0 || data?.code === 200) {
        queryClient.invalidateQueries({ queryKey: ['fetchConversationList'] });
      }
      return data;
    },
  });

  return { data, loading, updateConversation: mutateAsync };
};
export const useSetConversation = () => {
  const { updateConversation } = useUpdateNextConversation();

  const setConversation = useCallback(
    async (
      message: string,
      isNew: boolean = false,
      conversationId?: string,
      dialogId?: string,
    ) => {
      if (dialogId) {
        return await updateConversation({
          dialog_id: dialogId,
          name: message,
          is_new: isNew,
          conversation_id: conversationId,
          message: [
            {
              role: MessageType.Assistant,
              content: message,
            },
          ],
        });
      }
    },
    [],
  );

  return { setConversation };
};

export const useFindPrologueFromDialogList = () => {
  const { dialogId } = useHandleDialog();
  const { data: dialogDetail } = useFetchNextDialog();
  const prologue = useMemo(() => {
    return dialogDetail?.prompt_config?.prologue;
  }, [dialogId, dialogDetail]);

  return prologue || '您好！我是您的AI助手，有什么可以帮助您的吗？';
};

export const useFetchNextConversationList = () => {
  const { dialogId } = useHandleDialog();
  const { t } = useTranslate('chat');
  const { setConversation } = useSetConversation();
  const {
    data,
    isFetching: loading,
    refetch,
  } = useQuery<IConversation[]>({
    queryKey: ['fetchConversationList', dialogId],
    initialData: [],
    gcTime: 0,
    refetchOnWindowFocus: false,
    enabled: !!dialogId,
    queryFn: async () => {
      let resp = await listConversation(dialogId);
      if (resp.data.data?.length === 0) {
        await setConversation(t('newConversation'), false, '', dialogId);
        resp = await listConversation(dialogId);
      }
      return (resp.data.data || []).map(
        (item: {
          chatAssistantId: string;
          sessionId: string;
          sessionName: string;
          createTime: string;
        }) => {
          return {
            dialog_id: item.chatAssistantId,
            id: item.sessionId,
            createTime: item.createTime,
            // avatar: string;
            // message: Message[];
            // reference: IReference[];
            name: item.sessionName,
          };
        },
      );
    },
  });

  return { data, loading, refetch };
};

export const useSelectDerivedConversationList = () => {
  const { t } = useTranslate('chat');
  const { dialogId } = useHandleDialog();
  const [conversationId, setConversationId] = useState<string>('');
  const [list, setList] = useState<Array<IConversation>>([]);
  const { data: conversationList, loading } = useFetchNextConversationList();
  const prologue = useFindPrologueFromDialogList();
  const { setConversation } = useSetConversation();

  const addConversation = useCallback(async () => {
    return await setConversation(t('newConversation'), false, '', dialogId);
  }, [conversationList, dialogId, prologue, t]);

  // When you first enter the page, select the top conversation card

  useEffect(() => {
    setList([...conversationList]);
    if(conversationList && conversationList.length > 0) {
      if(!conversationId || (conversationId && conversationList.every(i => i.id  !== conversationId))) {
        setConversationId(conversationList[0].id);
      }
    }
  }, [conversationList]);

  // useEffect(() => {
  //   // debugger;
  //   if (conversationId) {
  //     if (conversation.message?.length > 0) {
  //       setDerivedMessages(conversation.message || []);
  //     } else {
  //       addPrologue();
  //     }
  //   }
  //
  //   if (!conversationId) {
  //     setDerivedMessages([]);
  //   }
  // }, [conversation.message, conversationId, setDerivedMessages]);

  return { list, addConversation, setConversationId, conversationId, loading };
};

export const useRemoveNextConversation = () => {
  const queryClient = useQueryClient();
  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationKey: ['removeConversation'],
    mutationFn: async (conversationIds: string) => {
      const { data } = await removeConversation(conversationIds);
      if (data.code === 0 || data.code === 200) {
        queryClient.invalidateQueries({ queryKey: ['fetchConversationList'] });
      }
      return data.code;
    },
  });

  return { data, loading, removeConversation: mutateAsync };
};

export const useRegenerateMessage = ({
  removeMessagesAfterCurrentMessage,
  sendMessage,
  messages,
  conversationId
}: {
  removeMessagesAfterCurrentMessage(messageId: string): void;
  sendMessage({
    message,
    currentConversationId,
    messages
  }: {
    message: Message;
    messages?: Message[];
    currentConversationId: string;
  }): void | Promise<any>;
  messages: Message[];
  conversationId: string
}) => {
  const regenerateMessage = useCallback(
    async (message: Message) => {
      // debugger;
      if (message.id) {
        removeMessagesAfterCurrentMessage(message.id);
        const index = messages.findIndex((x) => x.id === message.id);
        let nextMessages;
        if (index !== -1) {
          nextMessages = messages.slice(0, index);
        }
        sendMessage({
          message: { ...message, id: uuid() },
          messages: nextMessages,
          currentConversationId: conversationId
        });
      }
    },
    [removeMessagesAfterCurrentMessage, sendMessage, messages],
  );

  return { regenerateMessage };
};
