package com.etrx.kb.controller;

import com.etrx.kb.common.Result;
import com.etrx.kb.service.DictService;
import com.etrx.kb.vo.DictVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 数据字典控制器
 * 
 * <AUTHOR>
 * @date 2024-01-10
 */
@Slf4j
@RestController
@RequestMapping("/aikb/v1/dict")
@RequiredArgsConstructor
@Tag(name = "数据字典管理", description = "数据字典查询相关接口")
public class DictController {
    
    private final DictService dictService;
    
    /**
     * 查询启用的数据字典列表
     */
    @GetMapping("/list/enabled")
    @Operation(summary = "查询启用的数据字典列表", description = "根据字典类型查询启用状态的数据字典列表")
    public Result<List<DictVO>> getEnabledDictList(
            @Parameter(description = "字典类型", required = true) 
            @RequestParam String dictType) {
        log.info("查询启用的数据字典列表: dictType={}", dictType);
        List<DictVO> list = dictService.getEnabledDictList(dictType);
        return Result.success(list);
    }
    
    /**
     * 查询所有数据字典列表
     */
    @GetMapping("/list/all")
    @Operation(summary = "查询所有数据字典列表", description = "根据字典类型查询所有数据字典列表（包括禁用的）")
    public Result<List<DictVO>> getAllDictList(
            @Parameter(description = "字典类型", required = true)
            @RequestParam String dictType) {
        log.info("查询所有数据字典列表: dictType={}", dictType);
        List<DictVO> list = dictService.getAllDictList(dictType);
        return Result.success(list);
    }
    
    /**
     * 查询数据字典树形结构
     */
    @GetMapping("/tree")
    @Operation(summary = "查询数据字典树形结构", description = "根据字典类型查询树形结构的数据字典")
    public Result<List<DictVO>> getDictTree(
            @Parameter(description = "字典类型", required = true)
            @RequestParam String dictType,
            @Parameter(description = "是否只查询启用的")
            @RequestParam(defaultValue = "true") boolean onlyEnabled) {
        log.info("查询数据字典树形结构: dictType={}, onlyEnabled={}", dictType, onlyEnabled);
        List<DictVO> tree = dictService.getDictTree(dictType, onlyEnabled);
        return Result.success(tree);
    }
    
    /**
     * 根据类型和键查询数据字典
     */
    @GetMapping("/detail")
    @Operation(summary = "根据类型和键查询数据字典", description = "根据字典类型和字典键精确查询单个数据字典")
    public Result<DictVO> getDictByTypeAndKey(
            @Parameter(description = "字典类型", required = true)
            @RequestParam String dictType,
            @Parameter(description = "字典键", required = true)
            @RequestParam String dictKey) {
        log.info("根据类型和键查询数据字典: dictType={}, dictKey={}", dictType, dictKey);
        DictVO dict = dictService.getDictByTypeAndKey(dictType, dictKey);
        if (dict == null) {
            return Result.failed("数据字典不存在");
        }
        return Result.success(dict);
    }
    
    /**
     * 根据字典键查询数据字典
     */
    @GetMapping("/key")
    @Operation(summary = "根据字典键查询数据字典", description = "根据字典键精确查询单个数据字典，不需要指定类型")
    public Result<DictVO> getDictByKey(
            @Parameter(description = "字典键", required = true)
            @RequestParam String dictKey) {
        log.info("根据键查询数据字典: dictKey={}", dictKey);
        DictVO dict = dictService.getDictByKey(dictKey);
        if (dict == null) {
            return Result.failed("数据字典不存在");
        }
        return Result.success(dict);
    }
    
    /**
     * 批量查询数据字典（支持多个类型）
     */
    @PostMapping("/batch/types")
    @Operation(summary = "批量查询数据字典", description = "批量查询多个类型的数据字典")
    public Result<List<DictTypeVO>> getDictByTypes(
            @Parameter(description = "字典类型列表", required = true)
            @RequestBody List<String> dictTypes) {
        log.info("批量查询数据字典: dictTypes={}", dictTypes);
        
        List<DictTypeVO> result = new ArrayList<>();
        for (String dictType : dictTypes) {
            DictTypeVO typeVO = new DictTypeVO();
            typeVO.setDictType(dictType);
            typeVO.setDictList(dictService.getEnabledDictList(dictType));
            result.add(typeVO);
        }
        
        return Result.success(result);
    }
    
    /**
     * 数据字典类型VO
     */
    @Data
    public static class DictTypeVO {
        private String dictType;
        private List<DictVO> dictList;
    }
} 