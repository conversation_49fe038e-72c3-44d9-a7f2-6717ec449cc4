-- 创建聊天会话映射表
CREATE TABLE IF NOT EXISTS chat_session_mapping (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
    user_id BIGINT NOT NULL COMMENT '平台用户ID',
    session_id VARCHAR(255) NOT NULL COMMENT 'RAGflow会话ID',
    chat_assistant_id VARCHAR(255) NOT NULL COMMENT 'RAGflow聊天助手ID',
    session_name VARCHAR(255) COMMENT '会话名称',
    message LONGTEXT COMMENT '会话消息记录(JSON)',
    reference LONGTEXT COMMENT '会话引用记录(JSON)',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-禁用, 1-启用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_session_id (session_id),
    INDEX idx_chat_assistant_id (chat_assistant_id),
    INDEX idx_status (status),
    UNIQUE KEY uk_session_id (session_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='聊天会话映射表'; 