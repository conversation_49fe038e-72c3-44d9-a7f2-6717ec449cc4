package com.etrx.kb.service;

import com.etrx.kb.domain.TaskQueue;

/**
 * 任务执行服务接口
 */
public interface TaskExecutionService {
    
    /**
     * 提交任务到队列
     * @param taskDefinitionId 任务定义ID
     * @param businessData 业务数据
     * @param createdBy 创建者ID
     * @return 任务队列ID
     */
    Long submitTask(Long taskDefinitionId, String businessData, Long createdBy);
    
    /**
     * 直接执行任务（业务直接触发）
     * @param taskDefinitionId 任务定义ID
     * @param businessData 业务数据
     * @param createdBy 创建者ID
     */
    void executeTaskDirectly(Long taskDefinitionId, String businessData, Long createdBy);
    
    /**
     * 执行任务队列中的任务
     * @param taskQueue 任务队列
     */
    void executeTask(TaskQueue taskQueue);
    
    /**
     * 根据任务名称直接执行任务
     * @param taskName 任务名称
     * @param businessData 业务数据
     * @param createdBy 创建者ID
     */
    void executeTaskByName(String taskName, String businessData, Long createdBy);
    
    /**
     * 根据任务名称提交任务到队列
     * @param taskName 任务名称
     * @param businessData 业务数据
     * @param createdBy 创建者ID
     * @return 任务队列ID
     */
    Long submitTaskByName(String taskName, String businessData, Long createdBy);
} 