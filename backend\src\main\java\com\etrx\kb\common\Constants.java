package com.etrx.kb.common;

/**
 * 系统常量
 */
public class Constants {

    /**
     * 用户角色
     */
    public static class UserRole {
        /**
         * 普通用户
         */
        public static final int USER = 1;
        
        /**
         * 管理员
         */
        public static final int ADMIN = 2;
        
        /**
         * 超级管理员
         */
        public static final int SUPER_ADMIN = 3;
    }

    /**
     * 用户状态
     */
    public static class UserStatus {
        /**
         * 禁用
         */
        public static final int DISABLED = 0;
        
        /**
         * 启用
         */
        public static final int ENABLED = 1;
    }

    /**
     * 用户注册来源
     */
    public static class UserRegisterSource {
        /**
         * 系统注册
         */
        public static final String SYSTEM = "SYSTEM";
        
        /**
         * 第三方接入
         */
        public static final String THIRD_PARTY = "THIRD_PARTY";
        
        /**
         * 数据集成
         */
        public static final String DATA_INTEGRATION = "DATA_INTEGRATION";
    }

    /**
     * 知识库类型
     */
    public static class KnowledgeBaseType {
        /**
         * 开放类型
         */
        public static final String OPEN = "OPEN";
        
        /**
         * 应用类型
         */
        public static final String APP = "APP";
    }

    /**
     * 知识库成员角色
     */
    public static class KnowledgeBaseMemberRole {
        /**
         * 成员
         */
        public static final int MEMBER = 10;
        
        /**
         * 管理员
         */
        public static final int ADMIN = 20;
        
        /**
         * 所有者
         */
        public static final int OWNER = 30;
    }

    /**
     * 文档状态
     */
    public static class DocumentStatus {
        /**
         * 等待上传 - 文件已上传到本地，等待异步上传到Ragflow
         */
        public static final int PENDING_UPLOAD = -1;

        /**
         * 未解析
         */
        public static final int READY = 0;

        /**
         * 处理中
         */
        public static final int PROCESSING = 1;

        /**
         * 取消
         */
        public static final int CANCELED = 2;

        /**
         * 已完成
         */
        public static final int COMPLETED = 3;

        /**
         * 解析失败
         */
        public static final int FAILED = 4;

        /**
         * 上传失败
         */
        public static final int UPLOAD_FAILED = 5;
    }

    /**
     * 文档嵌入状态
     */
    public static class DocumentEmbedStatus {
        /**
         * 未嵌入
         */
        public static final int NOT_EMBEDDED = 0;
        
        /**
         * 嵌入中
         */
        public static final int EMBEDDING = 1;
        
        /**
         * 已嵌入
         */
        public static final int EMBEDDED = 2;
        
        /**
         * 失败
         */
        public static final int FAILED = 3;
    }

    /**
     * API密钥状态
     */
    public static class ApiKeyStatus {
        /**
         * 禁用
         */
        public static final int DISABLED = 0;
        
        /**
         * 启用
         */
        public static final int ENABLED = 1;
    }

    /**
     * Redis键前缀
     */
    public static class RedisPrefix {
        /**
         * 用户Token前缀
         */
        public static final String USER_TOKEN = "user:token:";
        
        /**
         * API密钥前缀
         */
        public static final String API_KEY = "api:key:";
    }
}