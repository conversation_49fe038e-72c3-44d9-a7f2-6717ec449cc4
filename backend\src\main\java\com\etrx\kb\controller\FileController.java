package com.etrx.kb.controller;

import com.etrx.kb.common.Result;
import com.etrx.kb.domain.FileNode;
import com.etrx.kb.dto.BatchCreateFolderRequest;
import com.etrx.kb.service.FileService;
import com.etrx.kb.util.SecurityUtils;
import com.etrx.kb.vo.DocumentUpdateVO;
import com.etrx.kb.vo.DocumentInfoVO;
import com.etrx.kb.vo.FileListVO;
import com.etrx.kb.vo.RagflowUploadVO;
import com.etrx.kb.vo.BatchUpdateStatusVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;

import java.io.IOException;
import java.io.OutputStream;
import java.util.List;
import java.util.HashMap;
import java.util.Map;

/**
 * 文件管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/aikb/files")
@RequiredArgsConstructor
@Tag(name = "文件管理", description = "文件和文件夹的管理接口")
public class FileController {

    private final FileService fileService;
    private final SecurityUtils securityUtils;
    
    @Value("${ragflow.base-url}")
    private String ragflowBaseUrl;
    
    @Autowired
    private RestTemplate restTemplate;

    /**
     * 获取知识库根目录
     */
    @Operation(summary = "获取知识库根目录", description = "获取指定知识库的根目录，如果不存在则创建")
    @GetMapping("/kb/{kbId}/root")
    public Result<FileNode> getKbRootFolder(
            @Parameter(description = "知识库ID") @PathVariable Long kbId) {
        return Result.success(fileService.getOrCreateKbRootFolder(kbId, securityUtils.getCurrentUserId()));
    }

    /**
     * 上传知识库文件
     */
    @Operation(summary = "上传知识库文件", description = "将文件上传到指定的知识库中的指定目录")
    @PostMapping("/kb/{kbId}/upload")
    public Result<FileNode> uploadKbFile(
            @Parameter(description = "知识库ID") @PathVariable Long kbId,
            @Parameter(description = "目标文件夹ID，不传则上传到知识库根目录") @RequestParam(required = false) Long folderId,
            @Parameter(description = "上传的文件") @RequestParam("file") MultipartFile file) {
        return Result.success(fileService.uploadKbFile(kbId, folderId, file, securityUtils.getCurrentUserId()));
    }

    /**
     * 删除知识库文件
     */
    @Operation(summary = "删除知识库文件", description = "从知识库中删除指定的文件")
    @DeleteMapping("/kb/{kbId}/files/{fileId}")
    @PreAuthorize("@knowledgeBasePermission.check(#kbId, T(com.etrx.kb.common.Constants$KnowledgeBaseMemberRole).ADMIN)")
    public Result<Void> deleteKbFile(
            @Parameter(description = "知识库ID") @PathVariable Long kbId,
            @Parameter(description = "文件ID") @PathVariable Long fileId) {
        fileService.deleteKbFile(kbId, fileId, securityUtils.getCurrentUserId());
        return Result.success();
    }

    /**
     * 批量绑定文件到知识库
     */
    @Operation(summary = "批量绑定文件到知识库", description = "将已存在的文件绑定到指定的多个知识库")
    @PostMapping("/files/{fileId}/bind")
    @PreAuthorize("@userPermission.checkRole(T(com.etrx.kb.common.Constants$UserRole).ADMIN)")
    public Result<Void> bindKbFile(
            @Parameter(description = "文件ID") @PathVariable Long fileId,
            @Parameter(description = "知识库ID列表") @RequestBody List<Long> kbIds) {
        fileService.bindKbFile(kbIds, fileId, securityUtils.getCurrentUserId());
        return Result.success();
    }

    /**
     * 批量解绑知识库文件
     */
    @Operation(summary = "批量解绑知识库文件", description = "解除文件与多个知识库的绑定关系")
    @PostMapping("/files/{fileId}/unbind")
    @PreAuthorize("@userPermission.checkRole(T(com.etrx.kb.common.Constants$UserRole).ADMIN)")
    public Result<Void> unbindKbFile(
            @Parameter(description = "文件ID") @PathVariable Long fileId,
            @Parameter(description = "知识库ID列表，为空则解绑所有关联的知识库") @RequestBody(required = false) List<Long> kbIds) {
        fileService.unbindKbFile(kbIds, fileId, securityUtils.getCurrentUserId());
        return Result.success();
    }

    /**
     * 创建文件夹
     */
    @Operation(summary = "创建文件夹", description = "在指定位置创建新的文件夹")
    @PostMapping("/folders")
    @PreAuthorize("@userPermission.checkRole(T(com.etrx.kb.common.Constants$UserRole).ADMIN)")
    public Result<FileNode> createFolder(@RequestBody HashMap<String, Object> request) {
        String name = (String) request.get("name");
        Long parentId = request.get("parentId") != null ? Long.valueOf(request.get("parentId").toString()) : null;
        return Result.success(fileService.createFolder(name, parentId, securityUtils.getCurrentUserId()));
    }

    /**
     * 批量创建文件夹结构
     */
    @Operation(summary = "批量创建文件夹结构", description = "批量创建文件夹，保持目录层级结构")
    @PostMapping("/folders/batch")
    @PreAuthorize("@userPermission.checkRole(T(com.etrx.kb.common.Constants$UserRole).ADMIN)")
    public Result<Map<String, Long>> createFoldersInBatch(@RequestBody BatchCreateFolderRequest request) {
        Map<String, Long> folderMap = fileService.createFoldersInBatch(
            request.getFolders(),
            request.getParentId(),
            securityUtils.getCurrentUserId()
        );
        return Result.success(folderMap);
    }

    /**
     * 删除文件夹
     */
    @Operation(summary = "删除文件夹", description = "删除指定的文件夹，文件夹必须为空")
    @DeleteMapping("/folders/{folderId}")
    @PreAuthorize("@userPermission.checkRole(T(com.etrx.kb.common.Constants$UserRole).ADMIN)")
    public Result<Void> deleteFolder(
            @Parameter(description = "文件夹ID") @PathVariable Long folderId) {
        fileService.deleteFolder(folderId, securityUtils.getCurrentUserId());
        return Result.success();
    }

    /**
     * 获取文件夹内容
     */
    @Operation(summary = "获取文件夹内容", description = "获取指定文件夹下的所有文件和子文件夹")
    @GetMapping("/folders/{folderId}")
    public Result<FileListVO> listFolder(
            @Parameter(description = "文件夹ID") @PathVariable Long folderId,
            @Parameter(description = "知识库ID，不传则获取所有文件") @RequestParam(required = false) Long kbId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @Parameter(description = "每页数量") @RequestParam(defaultValue = "10") Integer pageSize,
            @Parameter(description = "搜索关键字") @RequestParam(required = false) String keyword) {
        return Result.success(fileService.listFolder(folderId, kbId, pageNum, pageSize, keyword));
    }

    /**
     * 搜索文件
     */
    @Operation(summary = "搜索文件", description = "根据关键字搜索文件和文件夹")
    @GetMapping("/search")
    public Result<List<FileNode>> searchFiles(
            @Parameter(description = "搜索关键字") @RequestParam String keyword) {
        return Result.success(fileService.searchFiles(keyword));
    }

    /**
     * 上传独立文件
     */
    @Operation(summary = "上传独立文件", description = "上传文件到指定目录，不关联到知识库")
    @PostMapping("/upload")
    @PreAuthorize("@userPermission.checkRole(T(com.etrx.kb.common.Constants$UserRole).ADMIN)")
    public Result<FileNode> uploadFile(
            @Parameter(description = "父文件夹ID，不传则上传到根目录") @RequestParam(required = false) Long parentId,
            @Parameter(description = "上传的文件") @RequestParam("file") MultipartFile file) {
        return Result.success(fileService.uploadFile(parentId, null, file, securityUtils.getCurrentUserId()));
    }

    /**
     * 删除文件
     */
    @Operation(summary = "删除文件", description = "删除指定的文件")
    @DeleteMapping("/files/{fileId}")
    @PreAuthorize("@userPermission.checkRole(T(com.etrx.kb.common.Constants$UserRole).ADMIN)")
    public Result<Void> deleteFile(
            @Parameter(description = "文件ID") @PathVariable Long fileId) {
        fileService.deleteFile(fileId, securityUtils.getCurrentUserId());
        return Result.success();
    }

    /**
     * 下载文件
     */
    @Operation(summary = "下载文件", description = "下载指定的文件")
    @GetMapping("/kb/{fileId}/download")
    public void downloadFile(
            @Parameter(description = "文件ID") @PathVariable Long fileId,
            HttpServletResponse response) {
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition,Content-Length");
        fileService.downloadFile(fileId, response, securityUtils.getCurrentUserId());
    }

    /**
     * 解析文件
     */
    @Operation(summary = "解析文件", description = "解析指定知识库中的文件列表")
    @PostMapping("/kb/{kbId}/files/parse")
    @PreAuthorize("@knowledgeBasePermission.check(#kbId, T(com.etrx.kb.common.Constants$KnowledgeBaseMemberRole).ADMIN)")
    public Result<Void> parseFiles(
            @Parameter(description = "知识库ID") @PathVariable Long kbId,
            @Parameter(description = "文件ID列表") @RequestBody List<Long> fileIds) {
        fileService.parseFiles(kbId, fileIds, securityUtils.getCurrentUserId());
        return Result.success();
    }

    /**
     * 终止文件解析
     */
    @Operation(summary = "终止文件解析", description = "终止指定知识库中文件列表的解析过程")
    @PostMapping("/kb/{kbId}/files/stopparse")
    @PreAuthorize("@knowledgeBasePermission.check(#kbId, T(com.etrx.kb.common.Constants$KnowledgeBaseMemberRole).ADMIN)")
    public Result<Void> stopParsingFiles(
            @Parameter(description = "知识库ID") @PathVariable Long kbId,
            @Parameter(description = "文件ID列表") @RequestBody List<Long> fileIds) {
        fileService.stopParsingFiles(kbId, fileIds, securityUtils.getCurrentUserId());
        return Result.success();
    }

    /**
     * 更新文档元数据
     */
    @Operation(summary = "更新文档元数据", description = "更新指定文档的元数据信息，包括文档名称、元数据字段、解析方法和解析配置")
    @PutMapping("/kb/{kbId}/files/{fileId}/document")
    @PreAuthorize("@knowledgeBasePermission.check(#kbId, T(com.etrx.kb.common.Constants$KnowledgeBaseMemberRole).ADMIN)")
    public Result<Void> updateDocument(
            @Parameter(description = "知识库ID") @PathVariable Long kbId,
            @Parameter(description = "文件ID") @PathVariable Long fileId,
            @Parameter(description = "更新参数") @RequestBody RagflowUploadVO.UploadFileVO updateVO) {
        fileService.updateDocument(kbId, fileId, updateVO, securityUtils.getCurrentUserId());
        return Result.success();
    }

    /**
     * 批量更新文档状态
     */
    @Operation(summary = "批量更新文档状态", description = "批量更新指定知识库中多个文档的状态，只更新状态字段，不修改其他元数据")
    @PutMapping("/kb/{kbId}/files/batch-status")
    @PreAuthorize("@knowledgeBasePermission.check(#kbId, T(com.etrx.kb.common.Constants$KnowledgeBaseMemberRole).ADMIN)")
    public Result<Void> batchUpdateDocumentStatus(
            @Parameter(description = "知识库ID") @PathVariable Long kbId,
            @Parameter(description = "批量更新状态参数") @RequestBody BatchUpdateStatusVO updateVO) {
        fileService.batchUpdateDocumentStatus(kbId, updateVO.getFileIds(), updateVO.getStatus(), securityUtils.getCurrentUserId());
        return Result.success();
    }

    /**
     * 移动文件或文件夹
     */
    @Operation(summary = "移动文件或文件夹", description = "将文件或文件夹移动到新的目标位置")
    @PostMapping("/move/{nodeId}")
    @PreAuthorize("@userPermission.checkRole(T(com.etrx.kb.common.Constants$UserRole).ADMIN)")
    public Result<Void> moveNode(
            @Parameter(description = "要移动的节点ID") @PathVariable Long nodeId,
            @Parameter(description = "目标文件夹ID，如果为null则移动到根目录") @RequestParam(required = false) Long targetFolderId) {
        fileService.moveNode(nodeId, targetFolderId, securityUtils.getCurrentUserId());
        return Result.success();
    }

    /**
     * 重命名文件或文件夹
     */
    @Operation(summary = "重命名文件或文件夹", description = "修改指定文件或文件夹的名称")
    @PutMapping("/rename/{nodeId}")
    @PreAuthorize("@userPermission.checkRole(T(com.etrx.kb.common.Constants$UserRole).ADMIN)")
    public Result<Void> renameNode(
            @Parameter(description = "要重命名的节点ID") @PathVariable Long nodeId,
            @Parameter(description = "新的名称") @RequestParam String newName) {
        fileService.renameNode(nodeId, newName, securityUtils.getCurrentUserId());
        return Result.success();
    }
    
    /**
     * 根据知识库ID查询所有关联文档信息
     */
    @Operation(summary = "根据知识库ID查询所有关联文档信息", description = "根据知识库ID查询该知识库关联的所有文档信息，返回文档ID、文档名称和文档路径")
    @GetMapping("/kb/{kbId}/documents")
    public Result<List<DocumentInfoVO>> getAllDocumentsByKbId(
            @Parameter(description = "知识库ID") @PathVariable Long kbId) {
        return Result.success(fileService.getAllDocumentsByKbId(kbId, securityUtils.getCurrentUserId()));
    }

    /**
     * 批量删除知识库文件
     */
    @Operation(summary = "批量删除知识库文件", description = "从知识库中批量删除指定的文件")
    @DeleteMapping("/kb/{kbId}/files/batch")
    @PreAuthorize("@knowledgeBasePermission.check(#kbId, T(com.etrx.kb.common.Constants$KnowledgeBaseMemberRole).ADMIN)")
    public Result<Void> batchDeleteKbFiles(
            @Parameter(description = "知识库ID") @PathVariable Long kbId,
            @Parameter(description = "文件ID列表") @RequestBody List<Long> fileIds) {
        fileService.batchDeleteKbFiles(kbId, fileIds, securityUtils.getCurrentUserId());
        return Result.success();
    }
}