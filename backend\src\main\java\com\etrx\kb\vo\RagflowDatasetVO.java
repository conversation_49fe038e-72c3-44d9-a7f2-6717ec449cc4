package com.etrx.kb.vo;

import lombok.Data;
import java.util.List;

/**
 * Ragflow数据集返回结果
 */
@Data
public class RagflowDatasetVO {
    private Integer code;
    private DatasetVO data;

    @Data
    public static class DatasetVO {
        private String avatar;
        private Integer chunk_count;
        // 切片方法,默认值:naive对应ragflow:General
        private String chunk_method;
        private String create_date;
        private Long create_time;
        private String created_by;
        private String description;
        private Integer document_count;
        private String embedding_model;
        private String id;
        private String language;
        // 数据集名称
        private String name;
        // 页面排名
        private Integer pagerank;
        private ParserConfigVO parser_config;
        private String permission;
        private Double similarity_threshold;
        private String status;
        private String tenant_id;
        private Integer token_num;
        private String update_date;
        private Long update_time;
        private Double vector_similarity_weight;
    }

    @Data
    public static class ParserConfigVO {
        // 自动关键词提取
        private int auto_keywords;
        // 自动问题提取
        private int auto_questions;
        // 建议文本块大小
        private Integer chunk_token_num;
        // 文本分段标识符
        private String delimiter;
        // 图谱配置
        private GraphragVO graphrag;
        // 表格转HTML
        private Boolean html4excel;
        // PDF解析器
        private String layout_recognize;
        // Raptor配置
        private RaptorVO raptor;
    }

    @Data
    public static class GraphragVO {
        // 社区报告生成
        private Boolean community;
        // 实体类型列表
        private List<String> entity_types;
        // 方法
        private String method;
        // 实体归一化
        private Boolean resolution;
        // 提取知识图谱
        private Boolean use_graphrag;
    }

    @Data
    public static class RaptorVO {
        // 最大聚类数
        private Integer max_cluster;
        // 最大token数
        private Integer max_token;
        // 提示词
        private String prompt;
        // 随机种子
        private Integer random_seed;
        // 阈值
        private Double threshold;
        // 是否启用Raptor策略
        private Boolean use_raptor;
    }
} 