# 饼状图展示测试组件

这个测试组件用于测试 `markdown-content` 组件中的饼状图展示功能。

## 功能特性

1. **预设示例测试** - 提供多个预设的饼状图示例
2. **自定义数据测试** - 可以输入自定义的饼状图数据进行测试
3. **实时渲染** - 实时预览 Markdown 内容的渲染结果
4. **单独组件测试** - 测试独立的饼状图组件
5. **混合内容测试** - 测试饼状图与其他 Markdown 内容的混合显示

## 饼状图语法

### 基础语法
```markdown
```pie
名称1: 值1
名称2: 值2
名称3: 值3
```
```

### 带颜色语法
```markdown
```pie
名称1: 值1: #颜色代码
名称2: 值2: #颜色代码
名称3: 值3: #颜色代码
```
```

### 示例
```markdown
```pie
苹果: 30
香蕉: 25: #FFC658
橙子: 20: #FF6B6B
葡萄: 15: #4ECDC4
草莓: 10: #45B7D1
```
```

## 使用方法

1. 访问测试页面：`/pie-chart-test`
2. 使用预设示例按钮快速测试不同场景
3. 在自定义测试区域输入饼状图数据进行解析测试
4. 在 Markdown 编辑器中编写包含饼状图的完整内容
5. 查看实时渲染结果

## 测试场景

### 1. 基础饼状图
- 测试基本的饼状图显示
- 验证数据解析和渲染

### 2. 带颜色饼状图
- 测试自定义颜色的饼状图
- 验证颜色解析和应用

### 3. 混合内容
- 测试饼状图与普通文本、代码块、数学公式的混合显示
- 验证 Markdown 解析器的兼容性

### 4. 边界情况
- 测试空数据、无效数据等边界情况
- 验证错误处理和容错机制

## 组件依赖

- `MarkdownContent` - 主要的 Markdown 渲染组件
- `PieChartComponent` - 饼状图显示组件
- `parsePieChart` - 饼状图数据解析工具
- Ant Design 组件库

## 文件结构

```
markdown-content/
├── index.tsx              # 主要的 Markdown 内容组件
├── index.module.less      # 样式文件
├── pie-chart-test.tsx     # 饼状图测试组件
└── README.md              # 说明文档
```

## 注意事项

1. 饼状图数据格式必须严格按照 `名称: 值` 或 `名称: 值: 颜色` 的格式
2. 颜色值必须是有效的 CSS 颜色代码（如 #FF6B6B）
3. 数值必须是有效的数字
4. 空行会被自动忽略
5. 如果解析失败，会显示为普通代码块 

# 饼状图防闪烁优化方案

## 问题描述

在聊天应用中，饼状图在以下情况下会出现闪烁问题：

1. **流式消息更新**：当接收到流式消息时，每次内容更新都会导致饼状图重新渲染
2. **消息列表变化**：当问新问题时，消息列表变化导致组件重新渲染
3. **缺少优化机制**：每次渲染都会重新解析饼状图数据

## 解决方案

### 1. React 性能优化

#### 1.1 使用 React.memo

```typescript
// 优化的饼状图组件
const MemoizedPieChart = memo(({ chartConfig }: { chartConfig: any }) => {
  return (
    <div className={styles.pieChartWrapper}>
      <PieChartComponent {...chartConfig} />
    </div>
  );
});
```

**优势：**
- 只有当 `chartConfig` 真正变化时才重新渲染
- 避免因父组件重新渲染导致的不必要更新

#### 1.2 使用 useMemo 缓存计算结果

```typescript
// 在 PieChartComponent 中
const chartData = useMemo(() => {
  return data.map((item, index) => ({
    ...item,
    color: item.color || COLORS[index % COLORS.length],
  }));
}, [data]);

// 生成稳定的 key
const chartKey = useMemo(() => {
  return data.map(item => `${item.name}-${item.value}`).join('|');
}, [data]);
```

**优势：**
- 只有当 `data` 变化时才重新计算
- 为 React 的 diff 算法提供稳定的 key

#### 1.3 使用 useCallback 优化函数

```typescript
const customCodeBlock = useCallback((props: any) => {
  // 处理饼状图逻辑
}, []);
```

**优势：**
- 避免每次渲染时创建新的函数引用
- 减少子组件的不必要重新渲染

### 2. 稳定的 Key 生成

```typescript
// 为 React 组件生成稳定的 key
const chartKey = useMemo(() => {
  return data.map(item => `${item.name}-${item.value}`).join('|');
}, [data]);

// 为 Cell 组件生成稳定的 key
<Cell key={`cell-${index}-${entry.name}`} fill={entry.color} />
```

**优势：**
- 帮助 React 正确识别组件变化
- 避免不必要的 DOM 操作

## 使用方式

### 基本用法

```typescript
import MarkdownContent from '@/components/chat-widget/markdown-content';

// 在组件中使用
<MarkdownContent 
  content={markdownContent} 
  loading={false}
/>
```

### 饼状图语法

支持两种格式：

1. **标准格式**：
```markdown
```pie
苹果: 30
香蕉: 25
橙子: 20
葡萄: 15
西瓜: 10
```
```

2. **Mermaid 格式**：
```markdown
```mermaid pie
苹果: 30
香蕉: 25
橙子: 20
葡萄: 15
西瓜: 10
```
```

## 测试

使用 `pie-chart-test.tsx` 组件进行测试：

1. **流式消息测试**：模拟逐字符更新，验证防闪烁效果
2. **新问题测试**：模拟消息列表变化，验证 memo 机制
3. **基础测试**：测试标准饼状图语法
4. **Mermaid 测试**：测试 Mermaid 格式的饼状图

## 性能提升

- **减少重新渲染**：使用 memo 和 useMemo 避免不必要的渲染
- **稳定的组件引用**：useCallback 确保函数引用稳定
- **优化的 diff 算法**：稳定的 key 帮助 React 正确识别变化
- **响应速度**：避免重复计算，提升渲染性能

## 工作原理

1. **Memo 机制**：`React.memo` 会浅比较 props，只有当 props 真正变化时才重新渲染
2. **useMemo 缓存**：缓存计算结果，避免重复计算
3. **useCallback 优化**：缓存函数引用，避免子组件不必要的重新渲染
4. **稳定的 Key**：为列表项提供稳定的 key，帮助 React 正确识别变化

## 注意事项

1. 确保传递给 memo 组件的 props 是稳定的
2. 避免在 render 函数中创建新的对象或数组
3. 合理使用 useMemo 和 useCallback，不要过度优化
4. 为列表项提供稳定的 key

## 扩展性

该方案可以轻松扩展到其他图表类型：

1. 在 `chart-parser.ts` 中添加新的解析函数
2. 在 `markdown-content/index.tsx` 中添加新的语言支持
3. 创建对应的图表组件并应用相同的优化策略
4. 使用相同的 memo 模式包装新组件 