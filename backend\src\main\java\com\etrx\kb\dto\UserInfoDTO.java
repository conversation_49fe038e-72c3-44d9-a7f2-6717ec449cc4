package com.etrx.kb.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户信息DTO
 */
@Data
public class UserInfoDTO {

    /**
     * 用户ID
     */
    private Long id;

    /**
     * 用户名
     */
    private String username;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 用户工号(第三方系统使用)
     */
    private String employeeNo;

    /**
     * 用户UUID(第三方系统使用)
     */
    private String userUuid;

    /**
     * 用户注册来源：SYSTEM(系统注册)、THIRD_PARTY_appName(第三方接入)、DATA_INTEGRATION(数据集成)
     */
    private String registerSource;

    /**
     * 来源应用名称(第三方系统使用，运行时字段)
     */
    private String appName;

    /**
     * 角色(1:普通用户,2:管理员,3:超级管理员)
     */
    private Integer role;

    /**
     * 状态(0:禁用,1:启用)
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}