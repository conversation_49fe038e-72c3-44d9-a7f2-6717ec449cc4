package com.etrx.kb.service;

import com.etrx.kb.dto.FolderDTO;
import com.etrx.kb.dto.FolderTreeDTO;

import java.util.List;

/**
 * 文件夹服务接口
 */
public interface FolderService {

    /**
     * 创建文件夹
     *
     * @param kbId      知识库ID
     * @param parentId   父文件夹ID
     * @param name      文件夹名称
     * @param creatorId 创建者ID
     * @return 创建的文件夹信息
     */
    FolderDTO createFolder(Long kbId, Long parentId, String name, Long creatorId);

    /**
     * 获取文件夹信息
     *
     * @param id 文件夹ID
     * @return 文件夹信息
     */
    FolderDTO getFolder(Long id);

    /**
     * 删除文件夹
     *
     * @param id        文件夹ID
     * @param operatorId 操作者ID
     */
    void deleteFolder(Long id, Long operatorId);

    /**
     * 移动文件夹
     *
     * @param id        文件夹ID
     * @param parentId  新的父文件夹ID
     * @param operatorId 操作者ID
     */
    void moveFolder(Long id, Long parentId, Long operatorId);

    /**
     * 重命名文件夹
     *
     * @param id        文件夹ID
     * @param newName   新名称
     * @param operatorId 操作者ID
     */
    void renameFolder(Long id, String newName, Long operatorId);

    /**
     * 获取知识库的文件夹树
     *
     * @param kbId     知识库ID
     * @param userId   用户ID（用于权限检查）
     * @return 文件夹树
     */
    List<FolderTreeDTO> getFolderTree(Long kbId, Long userId);

    /**
     * 获取子文件夹列表
     *
     * @param parentId 父文件夹ID
     * @param userId   用户ID（用于权限检查）
     * @return 子文件夹列表
     */
    List<FolderDTO> getSubFolders(Long parentId, Long userId);
}