package com.etrx.kb.dto;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 知识库成员DTO
 */
@Data
public class KnowledgeBaseMemberDTO {

    /**
     * 知识库ID
     */
    @NotNull(message = "知识库ID不能为空")
    private Long kbId;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 角色(10:member,20:admin,30:owner)
     */
    @NotNull(message = "角色不能为空")
    private Integer role;

    /**
     * 用户名
     */
    private String username;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 头像URL
     */
    private String avatar;
}