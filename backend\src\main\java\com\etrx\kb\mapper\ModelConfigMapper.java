package com.etrx.kb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.etrx.kb.domain.ModelConfigEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 模型配置Mapper
 */
@Mapper
public interface ModelConfigMapper extends BaseMapper<ModelConfigEntity> {

    /**
     * 根据提供商查询模型配置
     */
    @Select("SELECT * FROM model_config WHERE provider = #{provider} AND deleted = 0 ORDER BY created_time ASC")
    List<ModelConfigEntity> findByProvider(String provider);

    /**
     * 根据模型类型查询模型配置
     */
    @Select("SELECT * FROM model_config WHERE model_type = #{modelType} AND deleted = 0 ORDER BY created_time ASC")
    List<ModelConfigEntity> findByModelType(String modelType);

    /**
     * 根据提供商和模型名称查询
     */
    @Select("SELECT * FROM model_config WHERE provider = #{provider} AND llm_name = #{llmName} AND deleted = 0 LIMIT 1")
    ModelConfigEntity findByProviderAndModelName(String provider, String llmName);

    /**
     * 查询所有提供商名称
     */
    @Select("SELECT DISTINCT provider FROM model_config WHERE deleted = 0 ORDER BY provider ASC")
    List<String> findAllProviders();

    /**
     * 统计总记录数
     */
    @Select("SELECT COUNT(*) FROM model_config WHERE deleted = 0")
    long countAll();
} 