package com.etrx.kb.controller;

import com.etrx.kb.common.Result;
import com.etrx.kb.dto.ModelDTO;
import com.etrx.kb.service.ModelService;
import com.etrx.kb.config.ModelConfig;
import com.etrx.kb.service.ModelProviderService;
import com.etrx.kb.service.impl.ModelProviderServiceImpl;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;

/**
 * 模型控制器
 */
@Tag(name = "模型管理", description = "提供AI模型列表查询、可用性检查等功能")
@Slf4j
@RestController
@RequestMapping("/aikb/models")
@RequiredArgsConstructor
public class ModelController {

    private final ModelService modelService;
    private final ModelProviderService modelProviderService;
    private final ModelProviderServiceImpl modelProviderServiceImpl;

    /**
     * 获取所有可用的模型列表
     * 按提供商分组返回
     */
    @Operation(summary = "获取所有模型列表", description = "按提供商分组返回所有可用的模型列表")
    @GetMapping
    public Result<Map<String, List<ModelDTO>>> getAllModels() {
        Map<String, List<ModelDTO>> models = modelService.getAllModels();
        return Result.success(models);
    }

    /**
     * 获取指定提供商的模型列表
     */
    @Operation(summary = "获取指定提供商的模型列表")
    @GetMapping("/provider/{provider}")
    public Result<List<ModelDTO>> getModelsByProvider(
            @Parameter(description = "提供商名称") @PathVariable String provider) {
        List<ModelDTO> models = modelService.getModelsByProvider(provider);
        return Result.success(models);
    }

    /**
     * 获取指定类型的模型列表
     */
    @Operation(summary = "获取指定类型的模型列表")
    @GetMapping("/type/{modelType}")
    public Result<List<ModelDTO>> getModelsByType(
            @Parameter(description = "模型类型（chat, embedding, tts, speech2text）") @PathVariable String modelType) {
        List<ModelDTO> models = modelService.getModelsByType(modelType);
        return Result.success(models);
    }

    /**
     * 检查模型是否可用
     */
    @Operation(summary = "检查模型是否可用")
    @GetMapping("/check/{provider}/{modelName}")
    public Result<Boolean> checkModelAvailability(
            @Parameter(description = "提供商名称") @PathVariable String provider, 
            @Parameter(description = "模型名称") @PathVariable String modelName) {
        Boolean available = modelService.isModelAvailable(provider, modelName);
        return Result.success(available);
    }

    /**
     * 获取支持聊天的模型列表（常用接口）
     */
    @Operation(summary = "获取聊天模型列表")
    @GetMapping("/chat")
    public Result<List<ModelDTO>> getChatModels() {
        List<ModelDTO> models = modelService.getModelsByType("chat");
        return Result.success(models);
    }

    /**
     * 获取支持嵌入的模型列表（常用接口）
     */
    @Operation(summary = "获取嵌入模型列表")
    @GetMapping("/embedding")
    public Result<List<ModelDTO>> getEmbeddingModels() {
        List<ModelDTO> models = modelService.getModelsByType("embedding");
        return Result.success(models);
    }

    // ==================== 配置管理接口 ====================

    /**
     * 获取完整的模型配置
     */
    @Operation(summary = "获取完整的模型配置")
    @GetMapping("/config")
    public Result<ModelConfig> getModelConfig() {
        ModelConfig config = modelService.getModelConfig();
        return Result.success(config);
    }

    /**
     * 添加新的模型配置
     */
    @Operation(summary = "添加新的模型配置")
    @PostMapping("/config/{provider}")
    public Result<Boolean> addModelConfig(
            @Parameter(description = "提供商名称") @PathVariable String provider,
            @RequestBody ModelConfig.ModelConfigItem modelConfigItem) {
        Boolean success = modelService.addModelConfig(provider, modelConfigItem);
        if (success) {
            return Result.success("模型配置添加成功", true);
        } else {
            return Result.failed("模型配置添加失败");
        }
    }

    /**
     * 更新模型配置
     */
    @Operation(summary = "更新模型配置")
    @PutMapping("/config/{provider}/{modelName}")
    public Result<Boolean> updateModelConfig(
            @Parameter(description = "提供商名称") @PathVariable String provider,
            @Parameter(description = "模型名称") @PathVariable String modelName,
            @RequestBody ModelConfig.ModelConfigItem modelConfigItem) {
        Boolean success = modelService.updateModelConfig(provider, modelName, modelConfigItem);
        if (success) {
            return Result.success("模型配置更新成功", true);
        } else {
            return Result.failed("模型配置更新失败");
        }
    }

    /**
     * 删除模型配置
     */
    @Operation(summary = "删除模型配置")
    @DeleteMapping("/config/{provider}/{modelName}")
    public Result<Boolean> deleteModelConfig(
            @Parameter(description = "提供商名称") @PathVariable String provider,
            @Parameter(description = "模型名称") @PathVariable String modelName) {
        Boolean success = modelService.deleteModelConfig(provider, modelName);
        if (success) {
            return Result.success("模型配置删除成功", true);
        } else {
            return Result.failed("模型配置删除失败");
        }
    }

    /**
     * 添加新的提供商
     */
    @Operation(summary = "添加新的提供商")
    @PostMapping("/provider/{provider}")
    public Result<Boolean> addProvider(
            @Parameter(description = "提供商名称") @PathVariable String provider,
            @RequestBody ModelConfig.ProviderConfig providerConfig) {
        Boolean success = modelService.addProvider(provider, providerConfig);
        if (success) {
            return Result.success("提供商添加成功", true);
        } else {
            return Result.failed("提供商添加失败");
        }
    }

    /**
     * 删除提供商及其所有模型
     */
    @Operation(summary = "删除提供商")
    @DeleteMapping("/provider/{provider}")
    public Result<Boolean> deleteProvider(
            @Parameter(description = "提供商名称") @PathVariable String provider) {
        Boolean success = modelService.deleteProvider(provider);
        if (success) {
            return Result.success("提供商删除成功", true);
        } else {
            return Result.failed("提供商删除失败");
        }
    }

    /**
     * 重新加载配置
     */
    @Operation(summary = "重新加载配置", description = "清空运行时配置，重新从配置文件加载")
    @PostMapping("/config/reload")
    public Result<Boolean> reloadConfig() {
        Boolean success = modelService.reloadConfig();
        if (success) {
            return Result.success("配置重新加载成功", true);
        } else {
            return Result.failed("配置重新加载失败");
        }
    }

    /**
     * 获取配置状态信息
     */
    @Operation(summary = "获取配置状态信息", description = "显示当前配置来源和统计信息")
    @GetMapping("/status")
    public Result<Map<String, Object>> getConfigStatus() {
        Map<String, Object> status = new HashMap<>();
        
        // 获取所有模型
        Map<String, List<ModelDTO>> allModels = modelService.getAllModels();
        
        // 统计信息
        int totalProviders = allModels.size();
        int totalModels = allModels.values().stream()
                .mapToInt(List::size)
                .sum();
        
        // 按类型统计
        Map<String, Long> modelsByType = allModels.values().stream()
                .flatMap(List::stream)
                .collect(Collectors.groupingBy(
                    ModelDTO::getModelType,
                    Collectors.counting()
                ));
        
        status.put("totalProviders", totalProviders);
        status.put("totalModels", totalModels);
        status.put("modelsByType", modelsByType);
        status.put("providers", allModels.keySet());
        status.put("configSource", "DATABASE");
        status.put("lastUpdate", System.currentTimeMillis());
        
        return Result.success(status);
    }

    /**
     * 获取所有可用的模型提供商
     */
    @GetMapping("/providers")
    @Operation(summary = "获取可用模型提供商", description = "获取所有已配置和可用的AI模型提供商列表")
    public ResponseEntity<Map<String, Object>> getAvailableProviders() {
        try {
            String[] providers = modelProviderService.getAvailableProviders();
            Map<String, Object> providerInfo = modelProviderServiceImpl.getProviderInfo();
            
            return ResponseEntity.ok(Map.of(
                    "success", true,
                    "providers", providers,
                    "providerInfo", providerInfo
            ));
        } catch (Exception e) {
            log.error("获取模型提供商失败", e);
            return ResponseEntity.internalServerError().body(Map.of(
                    "success", false,
                    "message", "获取模型提供商失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 获取模型配置信息
     */
    @GetMapping("/config/provider-info")
    @Operation(summary = "获取模型提供商配置", description = "获取当前的AI模型提供商配置信息")
    public ResponseEntity<Map<String, Object>> getModelProviderConfig() {
        try {
            Map<String, Object> config = modelProviderServiceImpl.getProviderInfo();
            return ResponseEntity.ok(Map.of(
                    "success", true,
                    "config", config
            ));
        } catch (Exception e) {
            log.error("获取模型配置失败", e);
            return ResponseEntity.internalServerError().body(Map.of(
                    "success", false,
                    "message", "获取配置失败: " + e.getMessage()
            ));
        }
    }
} 