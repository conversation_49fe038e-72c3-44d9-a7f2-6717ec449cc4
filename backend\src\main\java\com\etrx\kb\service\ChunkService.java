package com.etrx.kb.service;

import com.etrx.kb.dto.ChunkRequestDTO;

/**
 * Chunk服务接口
 * 提供Chunk的CRUD操作和检索功能
 */
public interface ChunkService {

    /**
     * 添加Chunk
     *
     * @param request 添加Chunk请求参数
     * @return 添加结果JSON字符串
     */
    String addChunk(ChunkRequestDTO.AddChunkRequest request);

    /**
     * 查询Chunk列表
     *
     * @param request 查询请求参数
     * @return Chunk列表JSON字符串
     */
    String listChunks(ChunkRequestDTO.ListChunkRequest request);

    /**
     * 删除Chunk
     *
     * @param request 删除请求参数
     * @return 删除结果JSON字符串
     */
    String deleteChunks(ChunkRequestDTO.DeleteChunkRequest request);

    /**
     * 更新Chunk
     *
     * @param request 更新请求参数
     * @return 更新结果JSON字符串
     */
    String updateChunk(ChunkRequestDTO.UpdateChunkRequest request);

    /**
     * 批量更新Chunk
     *
     * @param request 批量更新请求参数
     * @return 更新结果JSON字符串
     */
    String batchUpdateChunks(ChunkRequestDTO.BatchUpdateChunkRequest request);

    /**
     * 检索Chunk
     *
     * @param request 检索请求参数
     * @return 检索结果JSON字符串
     */
    String retrieveChunks(ChunkRequestDTO.RetrieveChunkRequest request);
} 