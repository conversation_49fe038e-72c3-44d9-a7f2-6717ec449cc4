package com.etrx.kb.security;

import com.etrx.kb.common.Constants;
import com.etrx.kb.domain.User;
import com.etrx.kb.mapper.UserMapper;
import com.etrx.kb.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.function.BiPredicate;
import java.util.function.Predicate;

/**
 * 用户权限检查类
 */
@Component
@RequiredArgsConstructor
public class UserPermission {

    private final UserMapper userMapper;
    private final SecurityUtils securityUtils;

    /**
     * 权限检查策略枚举
     */
    public enum PermissionStrategy {
        // 允许自己访问自己
        ALLOW_SELF(true, (current, target) -> target.getRole() <= Constants.UserRole.ADMIN),
        // 不允许自己访问自己（如修改角色、状态等）
        DENY_SELF(false, (current, target) -> target.getRole() <= Constants.UserRole.ADMIN),
        // 管理员只能操作普通用户
        ADMIN_TO_USER_ONLY(false, (current, target) -> target.getRole().equals(Constants.UserRole.USER)),
        // 管理员可以操作普通用户和管理员，但不能设置超过自己权限的角色
        ADMIN_WITH_ROLE_LIMIT(false, (current, target) -> target.getRole() <= Constants.UserRole.ADMIN);

        private final boolean allowSelf;
        private final BiPredicate<User, User> adminPermissionCheck;

        PermissionStrategy(boolean allowSelf, BiPredicate<User, User> adminPermissionCheck) {
            this.allowSelf = allowSelf;
            this.adminPermissionCheck = adminPermissionCheck;
        }
    }

    /**
     * 通用权限检查方法
     *
     * @param targetUserId 目标用户ID
     * @param strategy 权限检查策略
     * @param additionalCheck 额外的权限检查（可选）
     * @return 是否有权限
     */
    private boolean checkPermission(Long targetUserId, PermissionStrategy strategy, 
                                  Predicate<User> additionalCheck) {
        User currentUser = securityUtils.getCurrentUser();
        if (currentUser == null) {
            return false;
        }

        // 检查是否是自己访问自己
        boolean isSelf = currentUser.getId().equals(targetUserId);
        if (isSelf) {
            return strategy.allowSelf;
        }

        // 超级管理员拥有所有权限（除了自己操作自己的限制）
        if (currentUser.getRole().equals(Constants.UserRole.SUPER_ADMIN)) {
            return true;
        }

        // 管理员权限检查
        if (currentUser.getRole().equals(Constants.UserRole.ADMIN)) {
            User targetUser = userMapper.selectById(targetUserId);
            if (targetUser != null) {
                boolean hasAdminPermission = strategy.adminPermissionCheck.test(currentUser, targetUser);
                // 如果有额外检查，也要通过
                if (additionalCheck != null) {
                    return hasAdminPermission && additionalCheck.test(targetUser);
                }
                return hasAdminPermission;
            }
        }

        // 普通用户默认没有权限
        return false;
    }

    /**
     * 重载方法，不需要额外检查的情况
     */
    private boolean checkPermission(Long targetUserId, PermissionStrategy strategy) {
        return checkPermission(targetUserId, strategy, null);
    }

    /**
     * 检查当前用户是否可以查看指定用户信息
     * 规则：
     * 1. 普通用户只能查看自己的信息
     * 2. 管理员可以查看普通用户和管理员的信息
     * 3. 超级管理员可以查看所有用户的信息
     *
     * @param targetUserId 目标用户ID
     * @return 是否有权限
     */
    public boolean canViewUser(Long targetUserId) {
        return checkPermission(targetUserId, PermissionStrategy.ALLOW_SELF);
    }

    /**
     * 检查当前用户是否可以修改指定用户信息
     * 规则：
     * 1. 普通用户只能修改自己的信息
     * 2. 管理员可以修改普通用户和管理员的信息
     * 3. 超级管理员可以修改所有用户的信息
     *
     * @param targetUserId 目标用户ID
     * @return 是否有权限
     */
    public boolean canUpdateUser(Long targetUserId) {
        return checkPermission(targetUserId, PermissionStrategy.ALLOW_SELF);
    }

    /**
     * 检查当前用户是否可以修改指定用户密码
     * 规则：
     * 1. 普通用户只能修改自己的密码
     * 2. 管理员可以修改普通用户和管理员的密码
     * 3. 超级管理员可以修改所有用户的密码
     *
     * @param targetUserId 目标用户ID
     * @return 是否有权限
     */
    public boolean canChangePassword(Long targetUserId) {
        return checkPermission(targetUserId, PermissionStrategy.ALLOW_SELF);
    }

    /**
     * 检查当前用户是否可以更新指定用户角色
     * 规则：
     * 1. 普通用户不能修改任何人的角色
     * 2. 管理员可以修改普通用户和管理员的角色，但不能设置为超过自己角色的级别
     * 3. 超级管理员可以修改所有用户的角色为任何级别
     *
     * @param targetUserId 目标用户ID
     * @param newRole 新角色
     * @return 是否有权限
     */
    public boolean canUpdateRole(Long targetUserId, Integer newRole) {
        return checkPermission(targetUserId, PermissionStrategy.ADMIN_WITH_ROLE_LIMIT, 
                             targetUser -> newRole <= Constants.UserRole.ADMIN);
    }

    /**
     * 检查当前用户是否可以更新用户状态
     * 规则：
     * 1. 普通用户不能修改任何人的状态
     * 2. 管理员可以修改普通用户和管理员的状态
     * 3. 超级管理员可以修改所有用户的状态
     *
     * @param targetUserId 目标用户ID
     * @return 是否有权限
     */
    public boolean canUpdateStatus(Long targetUserId) {
        return checkPermission(targetUserId, PermissionStrategy.DENY_SELF);
    }

    /**
     * 检查当前用户是否可以删除指定用户
     * 规则：
     * 1. 普通用户不能删除任何用户
     * 2. 管理员可以删除普通用户，但不能删除管理员或超级管理员
     * 3. 超级管理员可以删除除自己外的所有用户
     *
     * @param targetUserId 目标用户ID
     * @return 是否有权限
     */
    public boolean canDeleteUser(Long targetUserId) {
        return checkPermission(targetUserId, PermissionStrategy.ADMIN_TO_USER_ONLY);
    }

    /**
     * 检查当前用户是否具有指定的系统角色或更高权限
     * 规则：
     * 1. 如果当前用户角色值大于等于指定角色值，则返回true
     * 2. 否则返回false
     *
     * @param requiredRole 需要的角色级别
     * @return 是否有权限
     */
    public boolean checkRole(Integer requiredRole) {
        User currentUser = securityUtils.getCurrentUser();
        if (currentUser == null) {
            return false;
        }
        
        return currentUser.getRole() >= requiredRole;
    }
} 