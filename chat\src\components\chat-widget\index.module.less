.chatWidget {
  position: fixed;
  z-index: 1000;
}

//.chatButton {
//  width: 56px;
//  height: 56px;
//  display: flex;
//  align-items: center;
//  justify-content: center;
//  background: linear-gradient(135deg, #4f8cff 0%, #6fd6ff 100%);
//  box-shadow: 0 4px 16px rgba(80, 120, 255, 0.18), 0 1.5px 6px rgba(0,0,0,0.08);
//  border: none;
//  transition: box-shadow 0.2s, transform 0.2s, background 0.2s;
//  cursor: pointer;
//  position: fixed;
//  z-index: 1100;
//  right: 40px;
//  bottom: 60px;
//
//  svg {
//    width: 28px;
//    height: 28px;
//    color: #fff;
//    transition: color 0.2s;
//  }
//
//  &:hover {
//    background: linear-gradient(135deg, #3578ff 0%, #4fd6ff 100%);
//    box-shadow: 0 8px 24px rgba(80, 120, 255, 0.28), 0 2px 8px rgba(0,0,0,0.12);
//    transform: scale(1.08);
//    svg {
//      color: #e6f7ff;
//    }
//  }
//}

.chatContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.contextTags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 4px 0;
  
  :global {
    .ant-tag {
      margin: 0;
      padding: 2px 8px;
      border-radius: 4px;
      background: #f5f5f5;
      border: 1px solid #e8e8e8;
      
      &:hover {
        background: #e6f7ff;
        border-color: #1890ff;
      }
    }
  }
}

.messageList {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  margin-bottom: 16px;
}

.inputContainer {
  padding: 12px;
  border-top: 1px solid #f0f0f0;
  background: #fff;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.historyContent {
  min-width: 200px;
  max-height: 350px;
  overflow-y: auto;
  padding: 0;
}

.topicItem {
  cursor: pointer;
  padding: 12px 16px;
  transition: background 0.2s;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;

  &:hover {
    background: #f5faff;
  }
}

//.topicName {
//  font-size: 15px;
//  color: #222;
//}

.fullscreenDrawer {
  :global {
    .ant-drawer-content-wrapper {
      box-shadow: none !important;
    }
    
    .ant-drawer-header {
      border-bottom: 1px solid #f0f0f0;
    }
    
    .ant-drawer-body {
      padding: 0;
      height: calc(100vh - 55px);
    }
  }
}

:global {
  .ant-drawer-body {
    padding: 0 !important;
    height: 100%;
    overflow: hidden;
  }

  .ant-drawer-header {
    padding: 8px 24px !important;
  }
  .wgq {
    background-image: linear-gradient(to bottom right, #f8fafc, #eff6ff);
    height: 100%;
    .ant-spin-container {
      height: 100%;
    }
  }
}

.customChatIcon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4f8cff 0%, #6fd6ff 100%);
  box-shadow: 0 4px 16px rgba(80, 120, 255, 0.18), 0 1.5px 6px rgba(0,0,0,0.08);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: box-shadow 0.2s, transform 0.2s, background 0.2s;
  position: fixed;
  z-index: 1100;
  //right: 20px;
  //bottom: 20px;
  user-select: none;
  border: none;
  outline: none;

  svg {
    width: 32px;
    height: 32px;
    color: #fff;
    transition: color 0.2s;
    pointer-events: none;
  }

  &:hover {
    background: linear-gradient(135deg, #3578ff 0%, #4fd6ff 100%);
    box-shadow: 0 8px 24px rgba(80, 120, 255, 0.28), 0 2px 8px rgba(0,0,0,0.12);
    transform: scale(1.08);
    svg {
      color: #e6f7ff;
    }
  }
  &[data-position='bottom-right'] {
    bottom: 60px;
    right: 40px;
  }

  &[data-position='bottom-left'] {
    bottom: 60px;
    left: 40px;
  }

  &[data-position='top-right'] {
    top: 60px;
    right: 40px;
  }

  &[data-position='top-left'] {
    top: 60px;
    left: 40px;
  }
} 