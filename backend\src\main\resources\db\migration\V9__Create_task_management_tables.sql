-- 创建任务定义表
CREATE TABLE `tb_task_definition` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `task_name` varchar(100) NOT NULL COMMENT '任务名称',
    `task_type` varchar(50) NOT NULL COMMENT '任务类型',
    `class_path` varchar(255) NOT NULL COMMENT '执行类路径',
    `method_name` varchar(100) NOT NULL COMMENT '方法名',
    `thread_count` int NOT NULL DEFAULT 2 COMMENT '执行线程数',
    `max_execution_count` int NOT NULL DEFAULT 1000 COMMENT '最大执行个数',
    `max_retry_count` int NOT NULL DEFAULT 3 COMMENT '最大重试次数',
    `description` varchar(500) DEFAULT NULL COMMENT '任务描述',
    `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态(0:禁用,1:启用)',
    `deleted` tinyint NOT NULL DEFAULT 0 COMMENT '逻辑删除(0:未删除,1:已删除)',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_time` datetime NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_task_name` (`task_name`),
    KEY `idx_task_type` (`task_type`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务定义表';

-- 创建任务队列表
CREATE TABLE `tb_task_queue` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID（队列序号）',
    `task_definition_id` bigint NOT NULL COMMENT '任务定义ID',
    `business_data` json NOT NULL COMMENT '业务数据（JSON格式）',
    `execution_status` tinyint NOT NULL DEFAULT 0 COMMENT '执行状态(0:待执行,1:执行中,2:执行成功,3:执行失败,4:已取消)',
    `retry_count` int NOT NULL DEFAULT 0 COMMENT '重试次数',
    `error_message` text DEFAULT NULL COMMENT '错误信息',
    `next_execution_time` datetime DEFAULT NULL COMMENT '下次执行时间',
    `created_by` bigint DEFAULT NULL COMMENT '创建者ID',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_time` datetime NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_task_definition_id` (`task_definition_id`),
    KEY `idx_execution_status` (`execution_status`),
    KEY `idx_next_execution_time` (`next_execution_time`),
    KEY `idx_create_time` (`create_time`),
    CONSTRAINT `fk_task_queue_definition` FOREIGN KEY (`task_definition_id`) REFERENCES `tb_task_definition` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务队列表';

-- 创建任务执行记录表
CREATE TABLE `tb_task_execution_record` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `task_queue_id` bigint DEFAULT NULL COMMENT '任务队列ID（来自定时任务扫描）',
    `task_definition_id` bigint NOT NULL COMMENT '任务定义ID',
    `business_data` json NOT NULL COMMENT '业务数据（JSON格式）',
    `execution_source` tinyint NOT NULL DEFAULT 1 COMMENT '执行来源(1:定时任务扫描,2:业务直接触发)',
    `execution_status` tinyint NOT NULL DEFAULT 0 COMMENT '执行状态(0:待执行,1:执行中,2:执行成功,3:执行失败,4:已取消)',
    `start_time` datetime DEFAULT NULL COMMENT '开始执行时间',
    `end_time` datetime DEFAULT NULL COMMENT '结束执行时间',
    `execution_duration` bigint DEFAULT NULL COMMENT '执行时长（毫秒）',
    `result_data` json DEFAULT NULL COMMENT '执行结果数据',
    `error_message` text DEFAULT NULL COMMENT '错误信息',
    `thread_name` varchar(100) DEFAULT NULL COMMENT '执行线程名称',
    `host_name` varchar(100) DEFAULT NULL COMMENT '执行主机名',
    `host_ip` varchar(50) DEFAULT NULL COMMENT '执行主机IP',
    `created_by` bigint DEFAULT NULL COMMENT '创建者ID',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_time` datetime NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_task_queue_id` (`task_queue_id`),
    KEY `idx_task_definition_id` (`task_definition_id`),
    KEY `idx_execution_status` (`execution_status`),
    KEY `idx_execution_source` (`execution_source`),
    KEY `idx_start_time` (`start_time`),
    KEY `idx_create_time` (`create_time`),
    CONSTRAINT `fk_task_execution_record_queue` FOREIGN KEY (`task_queue_id`) REFERENCES `tb_task_queue` (`id`) ON DELETE SET NULL,
    CONSTRAINT `fk_task_execution_record_definition` FOREIGN KEY (`task_definition_id`) REFERENCES `tb_task_definition` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务执行记录表';

-- 创建序列号获取表（用于分布式主机获取序列号）
CREATE TABLE `tb_task_sequence` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `sequence_name` varchar(50) NOT NULL COMMENT '序列名称',
    `current_value` bigint NOT NULL DEFAULT 0 COMMENT '当前值',
    `step` int NOT NULL DEFAULT 1 COMMENT '步长',
    `update_time` datetime NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_sequence_name` (`sequence_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务序列号表';

-- 插入默认序列
INSERT INTO `tb_task_sequence` (`sequence_name`, `current_value`, `step`, `update_time`) VALUES ('TASK_SCANNER_SEQUENCE', 0, 1, NOW()); 