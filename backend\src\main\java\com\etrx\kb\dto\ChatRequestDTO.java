package com.etrx.kb.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;

/**
 * 聊天请求DTO
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ChatRequestDTO {

    /**
     * 用户问题
     */
    @NotBlank(message = "问题不能为空")
    private String question;

    /**
     * 消息历史列表（用于OpenAI Chat Completion API）
     * 如果提供，将使用完整的消息历史而不是单独的question
     */
    private List<ChatMessage> messages;

    /**
     * 知识库ID列表（本地系统的知识库ID）
     */
    private List<Long> knowledgeBaseIds;

    /**
     * RAGflow知识库ID列表（直接对接RAGflow的knowledgebase表ID）
     */
    private List<String> ragflowKnowledgeBaseIds;

    /**
     * 会话ID（可选，如果不提供会创建新会话）
     */
    private String sessionId;

    /**
     * 是否启用流式响应
     */
    private Boolean stream = true;

    /**
     * 相似度阈值
     */
    private Double similarityThreshold = 0.2;

    /**
     * 关键词相似度权重
     */
    private Double keywordsSimilarityWeight = 0.7;

    /**
     * 返回的文档块数量
     */
    private Integer topN = 8;

    /**
     * 最大令牌数
     */
    private Integer maxTokens = 512;

    /**
     * 温度参数
     */
    private Double temperature = 0.1;

    /**
     * 聊天消息DTO
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ChatMessage {
        /**
         * 消息ID
         */
        private String id;
        
        /**
         * 消息内容
         */
        private String content;
        
        /**
         * 角色：user 或 assistant
         */
        private String role;
        
        /**
         * 文档ID列表（可选）
         */
        private List<String> docIds;
    }

    // 兼容性方法：保持向后兼容
    /**
     * @deprecated 使用 knowledgeBaseIds 替代
     */
    @Deprecated
    public Long getKnowledgeBaseId() {
        return knowledgeBaseIds != null && !knowledgeBaseIds.isEmpty() ? knowledgeBaseIds.get(0) : null;
    }

    /**
     * @deprecated 使用 knowledgeBaseIds 替代
     */
    @Deprecated
    public void setKnowledgeBaseId(Long knowledgeBaseId) {
        if (knowledgeBaseId != null) {
            this.knowledgeBaseIds = List.of(knowledgeBaseId);
        }
    }
} 