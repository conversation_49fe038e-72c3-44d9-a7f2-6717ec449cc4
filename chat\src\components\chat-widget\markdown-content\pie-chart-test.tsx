import React, { useState, useEffect } from 'react';
import { <PERSON>ton, Card, Input, Space, Typography, Divider } from 'antd';
import MarkdownContent from './index';
import { parsePieChart, clearPieChartCache, getPieChartCacheStats } from '@/utils/chart-parser';
import PieChartComponent from '@/components/pie-chart';

const { TextArea } = Input;
const { Title, Text } = Typography;

const PieChartTest: React.FC = () => {
  const [markdownContent, setMarkdownContent] = useState('');
  const [customPieData, setCustomPieData] = useState('');

  const handleBasicTest = () => {
    setMarkdownContent(`\`\`\`pie
苹果: 30
香蕉: 25
橙子: 20
葡萄: 15
草莓: 10
\`\`\``);
  };

  const handleMermaidTest = () => {
    setMarkdownContent(`\`\`\`mermaid pie
收入: 50000
支出: 30000
储蓄: 15000
投资: 5000
\`\`\``);
  };

  const handleCustomPieTest = () => {
    const config = parsePieChart(`\`\`\`pie
葡萄: 15
草莓: 10`);

    if (config) {
      setCustomPieData(JSON.stringify(config, null, 2));
    }
  };

  // 模拟流式消息更新
  const simulateStreamingMessage = () => {
    const pieChartContent = `\`\`\`pie
苹果: 30
香蕉: 25
橙子: 20
葡萄: 15
西瓜: 10
\`\`\``;

    let currentContent = '';
    const words = pieChartContent.split('');
    
    const interval = setInterval(() => {
      if (currentContent.length < words.length) {
        currentContent += words[currentContent.length];
        setMarkdownContent(currentContent);
      } else {
        clearInterval(interval);
      }
    }, 50); // 每50ms添加一个字符，模拟流式更新
  };

  // 模拟新问题
  const simulateNewQuestion = () => {
    const newContent = `\`\`\`pie
收入: 50000
支出: 30000
储蓄: 15000
投资: 5000
\`\`\``;
    setMarkdownContent(newContent);
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px' }}>
      <h2>饼状图防闪烁测试</h2>
      
      <div style={{ marginBottom: '20px' }}>
        <h3>测试操作</h3>
        <button onClick={handleBasicTest} style={{ marginRight: '10px' }}>
          基础饼状图测试
        </button>
        <button onClick={handleMermaidTest} style={{ marginRight: '10px' }}>
          Mermaid 格式测试
        </button>
        <button onClick={handleCustomPieTest} style={{ marginRight: '10px' }}>
          自定义饼状图测试
        </button>
        <button onClick={simulateStreamingMessage} style={{ marginRight: '10px' }}>
          模拟流式消息
        </button>
        <button onClick={simulateNewQuestion}>
          模拟新问题
        </button>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>当前内容</h3>
        <pre style={{ 
          background: '#f5f5f5', 
          padding: '10px', 
          borderRadius: '4px',
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-all'
        }}>
          {markdownContent || '暂无内容'}
        </pre>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>渲染结果</h3>
        <div style={{ border: '1px solid #ddd', padding: '20px', borderRadius: '8px' }}>
          <MarkdownContent 
            content={markdownContent} 
            loading={false}
          />
        </div>
      </div>

      {customPieData && (
        <div style={{ marginBottom: '20px' }}>
          <h3>解析结果</h3>
          <pre style={{ 
            background: '#f5f5f5', 
            padding: '10px', 
            borderRadius: '4px' 
          }}>
            {customPieData}
          </pre>
        </div>
      )}

      <div>
        <h3>说明</h3>
        <ul>
          <li><strong>基础测试</strong>：测试标准饼状图语法</li>
          <li><strong>Mermaid 测试</strong>：测试 Mermaid 格式的饼状图</li>
          <li><strong>流式消息测试</strong>：点击"模拟流式消息"按钮，会逐字符显示饼状图内容，测试是否会出现闪烁</li>
          <li><strong>新问题测试</strong>：点击"模拟新问题"按钮，会切换到不同的饼状图数据，测试 memo 机制</li>
          <li><strong>防闪烁优化</strong>：使用 React.memo 和 useMemo 防止不必要的重新渲染</li>
        </ul>
      </div>
    </div>
  );
};

export default PieChartTest; 