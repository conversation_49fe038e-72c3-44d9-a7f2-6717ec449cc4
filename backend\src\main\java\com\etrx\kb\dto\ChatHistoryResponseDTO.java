package com.etrx.kb.dto;

import lombok.Data;

import java.util.List;

/**
 * 聊天历史记录标准格式响应DTO
 */
@Data
public class ChatHistoryResponseDTO {

    /**
     * 状态码，0表示成功
     */
    private Integer code;

    /**
     * 响应数据
     */
    private ChatHistoryData data;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 聊天历史数据
     */
    @Data
    public static class ChatHistoryData {
        /**
         * 用户头像base64编码
         */
        private String avatar;

        /**
         * 创建日期（GMT格式）
         */
        private String create_date;

        /**
         * 创建时间戳
         */
        private Long create_time;

        /**
         * 对话ID
         */
        private String dialog_id;

        /**
         * 会话ID
         */
        private String id;

        /**
         * 消息列表
         */
        private List<ChatMessage> message;

        /**
         * 会话名称
         */
        private String name;

        /**
         * 参考资料列表
         */
        private List<Reference> reference;

        /**
         * 更新日期（GMT格式）
         */
        private String update_date;

        /**
         * 更新时间戳
         */
        private Long update_time;

        /**
         * 用户ID
         */
        private String user_id;
    }

    /**
     * 聊天消息
     */
    @Data
    public static class ChatMessage {
        /**
         * 消息内容
         */
        private String content;

        /**
         * 创建时间戳
         */
        private Double created_at;

        /**
         * 消息ID
         */
        private String id;

        /**
         * 角色（user/assistant）
         */
        private String role;
    }

    /**
     * 参考资料
     */
    @Data
    public static class Reference {
        /**
         * 文档块列表
         */
        private List<Chunk> chunks;

        /**
         * 文档聚合信息
         */
        private List<DocAgg> doc_aggs;

        /**
         * 总数
         */
        private Integer total;
    }

    /**
     * 文档块
     */
    @Data
    public static class Chunk {
        /**
         * 内容
         */
        private String content;

        /**
         * 数据集ID
         */
        private String dataset_id;

        /**
         * 文档类型
         */
        private String doc_type;

        /**
         * 文档ID
         */
        private String document_id;

        /**
         * 文档名称
         */
        private String document_name;

        /**
         * 块ID
         */
        private String id;

        /**
         * 图片ID
         */
        private String image_id;

        /**
         * 位置信息
         */
        private List<List<Integer>> positions;
    }

    /**
     * 文档聚合信息
     */
    @Data
    public static class DocAgg {
        /**
         * 数量
         */
        private Integer count;

        /**
         * 文档ID
         */
        private String doc_id;

        /**
         * 文档名称
         */
        private String doc_name;
    }
} 