package com.etrx.kb.util;

import com.etrx.kb.exception.ApiException;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.util.Map;

/**
 * Ragflow工具类
 */
public class RagflowUtils {
    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 处理 Ragflow 接口返回结果
     * @param result 接口返回的 JSON 字符串
     * @param errorPrefix 错误消息前缀
     * @throws ApiException 当接口返回错误码时抛出
     */
    public static void handleResponse(String result, String errorPrefix) {
        try {
            Map<String, Object> response = objectMapper.readValue(result, Map.class);
            
            Integer code = (Integer) response.get("code");
            if (code != null && code != 0) {
                String message = (String) response.get("message");
                throw new ApiException(errorPrefix + ": " + (message != null ? message : "未知错误"));
            }
        } catch (IOException e) {
            throw new ApiException(errorPrefix + ": JSON解析失败 - " + e.getMessage());
        }
    }
} 