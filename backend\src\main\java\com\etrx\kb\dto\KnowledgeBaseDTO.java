package com.etrx.kb.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 知识库DTO
 * <AUTHOR>
 */
@Data
public class KnowledgeBaseDTO {

    /**
     * 知识库ID（更新时需要）
     */
    private Long id;

    /**
     * 知识库名称
     */
    @NotBlank(message = "知识库名称不能为空")
    @Size(max = 100, message = "知识库名称长度不能超过100个字符")
    private String name;

    /**
     * 知识库描述
     */
    @Size(max = 500, message = "知识库描述长度不能超过500个字符")
    private String description;

    /**
     * 类型(Open,App)
     */
    @NotBlank(message = "知识库类型不能为空")
    private String type;

    private String appName;

    /**
     * API Key(App类型使用)
     */
    private String apiKey;

    /**
     * 数据集ID
     */
    private String datasetId;

    /**
     * 数据集配置
     */
    private String datasetConfig;

    /**
     * 所有者ID
     */
    private Long ownerId;

    /**
     * 状态(0:禁用,1:启用)
     */
    private Integer status;

    /**
     * 逻辑删除(0:未删除,1:已删除)
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 文档数量
     */
    private Long documentCount;

    /**
     * 成员数量
     */
    private Long memberCount;

    /**
     * 当前用户是否是所有者
     */
    private Boolean isOwner;
}