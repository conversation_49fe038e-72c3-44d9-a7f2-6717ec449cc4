package com.etrx.kb;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.FilterType;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 知识库管理系统启动类
 */
@SpringBootApplication(exclude = {
    org.springframework.ai.autoconfigure.azure.openai.AzureOpenAiAutoConfiguration.class,
    org.springframework.ai.autoconfigure.openai.OpenAiAutoConfiguration.class
})
@ComponentScan(basePackages = {"com.etrx.kb", "com.etrx.mcp.service"},
               excludeFilters = {
                   @ComponentScan.Filter(type = FilterType.REGEX,
                                        pattern = "com\\.etrx\\.mcp\\.service\\.config\\.RestTemplateConfig"),
                   @ComponentScan.Filter(type = FilterType.REGEX,
                                        pattern = "com\\.etrx\\.mcp\\.service\\.controller\\..*"),
                   @ComponentScan.Filter(type = FilterType.REGEX,
                                        pattern = "com\\.etrx\\.mcp\\.service\\.exception\\..*")
               })
@Configuration
@EnableAutoConfiguration(exclude = {
        org.springframework.ai.mcp.server.autoconfigure.McpServerAutoConfiguration.class  // 排除McpServer自动配置类
})
@EnableTransactionManagement
@MapperScan("com.etrx.kb.mapper")
public class KbApplication {

    public static void main(String[] args) {
        // 临时禁用 Reactor 自动 context propagation 以解决兼容性问题
        System.setProperty("reactor.netty.ioWorkerCount", "4");
        System.setProperty("reactor.schedulers.defaultBoundedElasticSize", "10");
        
        try {
            // 禁用自动 context propagation
            Class<?> hooksClass = Class.forName("reactor.core.publisher.Hooks");
            java.lang.reflect.Method disableMethod = hooksClass.getMethod("disableAutomaticContextPropagation");
            disableMethod.invoke(null);
        } catch (Exception e) {
            // 忽略错误，继续启动
            System.err.println("Warning: Failed to disable automatic context propagation: " + e.getMessage());
        }

        SpringApplication.run(KbApplication.class, args);
    }
}