package com.etrx.kb.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.*;

/**
 * 任务线程池配置
 */
@Slf4j
@Configuration
public class TaskThreadPoolConfig {

    /**
     * 核心线程数
     */
    private static final int CORE_POOL_SIZE = 2;

    /**
     * 最大线程数
     */
    private static final int MAX_POOL_SIZE = 100;

    /**
     * 空闲线程存活时间（秒）
     */
    private static final long KEEP_ALIVE_TIME = 60L;

    /**
     * 队列容量（无界队列）
     */
    private static final int QUEUE_CAPACITY = Integer.MAX_VALUE;

    /**
     * 创建任务执行线程池
     * @return 线程池
     */
    @Bean("taskExecutorThreadPool")
    public ThreadPoolExecutor taskExecutorThreadPool() {
        return new ThreadPoolExecutor(
                CORE_POOL_SIZE,
                MAX_POOL_SIZE,
                KEEP_ALIVE_TIME,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(QUEUE_CAPACITY),
                new TaskThreadFactory(),
                new TaskRejectedExecutionHandler()
        );
    }

    /**
     * 自定义线程工厂
     */
    private static class TaskThreadFactory implements ThreadFactory {
        private static final ThreadGroup threadGroup = new ThreadGroup("TaskExecutor");
        private static volatile int threadNumber = 0;

        @Override
        public Thread newThread(Runnable r) {
            Thread thread = new Thread(threadGroup, r, "TaskExecutor-" + (++threadNumber));
            if (thread.isDaemon()) {
                thread.setDaemon(false);
            }
            if (thread.getPriority() != Thread.NORM_PRIORITY) {
                thread.setPriority(Thread.NORM_PRIORITY);
            }
            return thread;
        }
    }

    /**
     * 自定义拒绝策略
     * 当线程池满时，将任务插入到任务队列表中
     */
    private static class TaskRejectedExecutionHandler implements RejectedExecutionHandler {
        @Override
        public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
            log.warn("任务线程池已满，任务将被插入到任务队列表中等待处理");
            // 这里的逻辑会在TaskExecutionService中处理
            // 将任务插入到任务队列表中
            if (r instanceof TaskWrapper) {
                TaskWrapper taskWrapper = (TaskWrapper) r;
                taskWrapper.handleRejection();
            } else {
                log.error("未知的任务类型: {}", r.getClass().getName());
            }
        }
    }

    /**
     * 任务包装器接口
     */
    public interface TaskWrapper {
        /**
         * 处理拒绝情况
         */
        void handleRejection();
    }
} 