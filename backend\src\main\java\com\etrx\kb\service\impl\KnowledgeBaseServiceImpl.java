package com.etrx.kb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.etrx.kb.common.Constants;
import com.etrx.kb.common.PageResult;
import com.etrx.kb.domain.KnowledgeBase;
import com.etrx.kb.domain.KnowledgeBaseMember;
import com.etrx.kb.domain.User;
import com.etrx.kb.domain.FileNode;
import com.etrx.kb.domain.KbFileRel;
import com.etrx.kb.dto.KnowledgeBaseDTO;
import com.etrx.kb.dto.KnowledgeBaseMemberDTO;
import com.etrx.kb.dto.DashboardStatsDTO;
import com.etrx.kb.domain.ApiKey;
import com.etrx.kb.exception.ApiException;
import com.etrx.kb.mapper.KnowledgeBaseMapper;
import com.etrx.kb.mapper.KnowledgeBaseMemberMapper;
import com.etrx.kb.mapper.UserMapper;
import com.etrx.kb.mapper.ApiKeyMapper;
import com.etrx.kb.service.KnowledgeBaseService;
import com.etrx.kb.vo.RagflowDatasetVO;
import com.etrx.kb.util.RagflowUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import erd.cloud.ai.ragflow.RagflowClientFactory;
import erd.cloud.ai.ragflow.RagflowDatasetClient;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.Iterator;
import java.util.Objects;

/**
 * 知识库服务实现类
 */
@Service
@RequiredArgsConstructor
public class KnowledgeBaseServiceImpl implements KnowledgeBaseService {

    private static final Logger log = LoggerFactory.getLogger(KnowledgeBaseServiceImpl.class);
    private final KnowledgeBaseMapper knowledgeBaseMapper;
    private final KnowledgeBaseMemberMapper memberMapper;
    private final UserMapper userMapper;
    private final ObjectMapper objectMapper;
    private final ApiKeyMapper apiKeyMapper;
    private final com.etrx.kb.service.FileService fileService;

    private final RagflowDatasetClient ragClient = RagflowClientFactory.getClient(RagflowDatasetClient.class);

    /**
     * 移除JSON节点中的空值和空字符串
     */
    private void removeEmptyValues(ObjectNode node) {
        if (node == null) return;
        
        Iterator<Map.Entry<String, JsonNode>> iterator = node.fields();
        List<String> fieldsToRemove = new ArrayList<>();
        
        while (iterator.hasNext()) {
            Map.Entry<String, JsonNode> entry = iterator.next();
            if (entry.getValue().isNull() || 
                (entry.getValue().isTextual() && entry.getValue().asText().isEmpty())) {
                fieldsToRemove.add(entry.getKey());
            }
        }
        
        fieldsToRemove.forEach(node::remove);
    }

    /**
     * 处理特定配置节点（graphrag或raptor）
     */
    private void processConfigNode(ObjectNode parentNode, String nodeName, String useFlag) {
        if (!parentNode.has(nodeName) || parentNode.get(nodeName).isNull()) {
            return;
        }

        ObjectNode configNode = (ObjectNode) parentNode.get(nodeName);
        if (configNode.has(useFlag) && !configNode.get(useFlag).asBoolean()) {
            parentNode.remove(nodeName);
        } else {
            removeEmptyValues(configNode);
            // 如果配置节点为空，移除它
            if (!configNode.fields().hasNext()) {
                parentNode.remove(nodeName);
            }
        }
    }

    private ObjectNode filterIgnoredFields(RagflowDatasetVO.DatasetVO datasetVO) {
        ObjectNode jsonNode = objectMapper.valueToTree(datasetVO);

        // 移除空值和空字符串
        removeEmptyValues(jsonNode);

        // 移除被@JsonIgnore标记的字段
        List<String> ignoredFields = Arrays.asList(
                "similarity_threshold","vector_similarity_weight",
            "language", "chunk_count", "create_date", "create_time",
            "created_by", "document_count", "id", "status", "tenant_id",
            "token_num", "update_date", "update_time"
        );
        ignoredFields.forEach(jsonNode::remove);
        
        // 处理parser_config配置
        if (jsonNode.has("parser_config") && !jsonNode.get("parser_config").isNull()) {
            ObjectNode parserConfig = (ObjectNode) jsonNode.get("parser_config");
            removeEmptyValues(parserConfig);

            // 处理graphrag和raptor配置
            processConfigNode(parserConfig, "graphrag", "use_graphrag");
            processConfigNode(parserConfig, "raptor", "use_raptor");

            // 如果parser_config为空，则移除它
            if (!parserConfig.fields().hasNext()) {
                jsonNode.remove("parser_config");
            }
        }
        
        return jsonNode;
    }

    private KnowledgeBase validateKnowledgeBase(Long id, String kbName, String type, boolean isUpdate) {
        // 校验知识库名称
        if (StringUtils.isEmpty(kbName)) {
            throw new ApiException("知识库名称非法，请输入知识库名称");
        }

        // 校验知识库类型
        if (StringUtils.isEmpty(type)) {
            throw new ApiException("请选定知识库类型");
        }

        KnowledgeBase knowledgeBase = null;
        if (isUpdate) {
            // 更新场景下校验知识库是否存在
            knowledgeBase = knowledgeBaseMapper.selectById(id);
            if (knowledgeBase == null) {
                throw new ApiException("知识库不存在");
            }
        }

        // 检查知识库名称是否已存在
        LambdaQueryWrapper<KnowledgeBase> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KnowledgeBase::getName, kbName);
        if (isUpdate) {
            // 更新场景下需要排除自身
            queryWrapper.ne(KnowledgeBase::getId, id);
        }
        if (knowledgeBaseMapper.exists(queryWrapper)) {
            throw new ApiException("知识库名称已存在");
        }

        return knowledgeBase;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public KnowledgeBaseDTO createKnowledgeBase(Long userId, KnowledgeBaseDTO dto) {
        validateKnowledgeBase(null, dto.getName(), dto.getType(), false);
        
        // 检查知识库文件夹是否已存在
        if (fileService.checkKbFolderExists(dto.getName())) {
            throw new ApiException("该知识库目录已存在，请使用其他名称");
        }
        
        KnowledgeBase knowledgeBase = createKnowledgeBaseAndOwner(userId, dto);
        KnowledgeBaseDTO result = createAndSetupDataset(knowledgeBase);
        
        // 创建知识库文件夹
        try {
            fileService.getOrCreateKbRootFolder(knowledgeBase.getId(), userId);
        } catch (Exception e) {
            log.error("创建知识库文件夹失败: {}", e.getMessage());
            throw new ApiException("创建知识库文件夹失败: " + e.getMessage());
        }
        
        return result;
    }

    private KnowledgeBase createKnowledgeBaseAndOwner(Long userId, KnowledgeBaseDTO dto) {
        // 创建知识库
        KnowledgeBase knowledgeBase = new KnowledgeBase();
        BeanUtils.copyProperties(dto, knowledgeBase);
        knowledgeBase.setOwnerId(userId);
        knowledgeBase.setStatus(Constants.UserStatus.ENABLED);
        knowledgeBase.setCreateTime(LocalDateTime.now());
        knowledgeBase.setUpdateTime(LocalDateTime.now());
        
        knowledgeBaseMapper.insert(knowledgeBase);

        // 添加创建者为owner
        KnowledgeBaseMember member = new KnowledgeBaseMember();
        member.setKbId(knowledgeBase.getId());
        member.setUserId(userId);
        member.setRole(Constants.KnowledgeBaseMemberRole.OWNER);
        member.setCreateTime(LocalDateTime.now());
        member.setUpdateTime(LocalDateTime.now());
        memberMapper.insert(member);

        return knowledgeBase;
    }

    private KnowledgeBaseDTO createAndSetupDataset(KnowledgeBase knowledgeBase) {
        KnowledgeBaseDTO result = new KnowledgeBaseDTO();
        BeanUtils.copyProperties(knowledgeBase, result);

        RagflowDatasetVO response = null;
        try {
            // 设置知识库默认配置
            String res = ragClient.createDataset(knowledgeBase.getName());
            log.info("创建知识库数据集响应: {}", res);

            // 处理返回结果
            RagflowUtils.handleResponse(res, "创建知识库数据集失败");

            // 解析返回结果
            response = objectMapper.readValue(res, RagflowDatasetVO.class);
            
            // 获取数据集ID并保存
            knowledgeBase.setDatasetId(response.getData().getId());
            knowledgeBase.setDatasetConfig(objectMapper.writeValueAsString(response.getData()));
            knowledgeBaseMapper.updateById(knowledgeBase);
            
            // 更新返回结果
            BeanUtils.copyProperties(knowledgeBase, result);
            return result;
        } catch (Exception e) {
            log.error("创建知识库数据集失败: {}", e.getMessage());
            cleanupFailedDataset(response);
            throw new ApiException(e.getMessage());
        }
    }

    private void cleanupFailedDataset(RagflowDatasetVO response) {
        if (response != null && response.getData() != null) {
            try {
                ragClient.deleteDatasets(new String[]{response.getData().getId()});
            } catch (Exception ex) {
                log.error("清理失败的数据集时发生错误: {}", ex.getMessage());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteKnowledgeBase(Long id, Long userId) {
        // 检查用户是否有权限删除知识库
        if (!checkPermission(id, userId, Constants.KnowledgeBaseMemberRole.OWNER)) {
            throw new ApiException("没有权限删除该知识库");
        }

        // 查询知识库信息（在删除前先获取数据集ID）
        KnowledgeBase knowledgeBase = knowledgeBaseMapper.selectById(id);
        if (knowledgeBase == null) {
            throw new ApiException("知识库不存在");
        }
        String datasetId = knowledgeBase.getDatasetId();

        try {
            // 处理文件关联
            List<KbFileRel> fileRelations = fileService.getKbFileRelations(id);
            if (!fileRelations.isEmpty()) {
                // 解绑非知识库目录下的文件
                unbindNonKbFiles(id, knowledgeBase, fileRelations, userId);
            }

            // 删除所有成员关系
            LambdaQueryWrapper<KnowledgeBaseMember> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(KnowledgeBaseMember::getKbId, id);
            memberMapper.delete(queryWrapper);

            // 删除知识库目录
            deleteKnowledgeBaseFolder(knowledgeBase);

            // 删除知识库
            knowledgeBaseMapper.deleteById(id);
            

            // 删除远程数据集
            if (datasetId != null && !datasetId.isEmpty()) {
                try {
                    String res = ragClient.deleteDatasets(new String[]{datasetId});
                    RagflowUtils.handleResponse(res, "删除知识库数据集失败");
                } catch (Exception e) {
                    log.error("删除知识库数据集失败: {}", e.getMessage());
                    throw new ApiException("删除知识库数据集失败，事务已回滚");
                }
            }
        } catch (Exception e) {
            log.error("删除知识库失败: {}", e.getMessage());
            throw new ApiException(e.getMessage());
        }
    }

    /**
     * 解绑非知识库目录下的文件
     * @param kbId 知识库ID
     * @param knowledgeBase 知识库信息
     * @param fileRelations 文件关联关系列表
     * @param userId 用户ID
     */
    private void unbindNonKbFiles(Long kbId, KnowledgeBase knowledgeBase, 
                                  List<KbFileRel> fileRelations, Long userId) {
        // 获取非知识库目录下的文件
        List<Long> nonKbFileIds = new ArrayList<>();
        String kbFolderPath = "/.knowledgebase/" + knowledgeBase.getName() + "/";
        
        for (KbFileRel rel : fileRelations) {
            // 获取文件信息
            FileNode fileNode = fileService.getById(rel.getFileNodeId());
            if (fileNode != null && fileNode.getFullPath() != null) {
                // 判断文件是否在知识库目录下
                if (!fileNode.getFullPath().startsWith(kbFolderPath)) {
                    nonKbFileIds.add(fileNode.getId());
                }
            }
        }
        
        // 如果存在非知识库目录下的文件，需要先解绑
        if (!nonKbFileIds.isEmpty()) {
            log.info("发现{}个非知识库目录下的关联文件，先进行解绑操作", nonKbFileIds.size());
            // 批量解绑所有非知识库目录下的文件
            for (Long fileId : nonKbFileIds) {
                try {
                    fileService.unbindKbFile(List.of(kbId), fileId, userId);
                } catch (Exception e) {
                    log.error("解绑文件失败，文件ID: {}, 错误: {}", fileId, e.getMessage());
                }
            }
        }
    }

    /**
     * 删除知识库对应的目录节点
     * @param knowledgeBase 知识库信息
     */
    private void deleteKnowledgeBaseFolder(KnowledgeBase knowledgeBase) {
        try {
            // 获取.knowledgebase根目录
            LambdaQueryWrapper<FileNode> rootWrapper = new LambdaQueryWrapper<>();
            rootWrapper.eq(FileNode::getName, ".knowledgebase")
                      .isNull(FileNode::getParentId)
                      .eq(FileNode::getNodeType, 1)
                      .eq(FileNode::getDeleted, 0);
            
            FileNode rootNode = fileService.getOne(rootWrapper);
            if (rootNode != null) {
                // 获取知识库专属目录
                LambdaQueryWrapper<FileNode> kbWrapper = new LambdaQueryWrapper<>();
                kbWrapper.eq(FileNode::getName, knowledgeBase.getName())
                        .eq(FileNode::getParentId, rootNode.getId())
                        .eq(FileNode::getNodeType, 1)
                        .eq(FileNode::getDeleted, 0);
                
                FileNode kbNode = fileService.getOne(kbWrapper);
                if (kbNode != null) {
                    // 只删除知识库目录节点本身
                    fileService.removeById(kbNode.getId());
                    log.info("已删除知识库目录: {}", kbNode.getFullPath());
                }
            }
        } catch (Exception e) {
            log.error("删除知识库目录失败: {}", e.getMessage());
            // 目录删除失败不影响知识库删除
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public KnowledgeBaseDTO updateKnowledgeBase(Long id, KnowledgeBaseDTO dto) {
        KnowledgeBase knowledgeBase = validateKnowledgeBase(id, dto.getName(), dto.getType(), true);
        
        // 创建原始对象的副本用于比较
        KnowledgeBase originalKnowledgeBase = new KnowledgeBase();
        BeanUtils.copyProperties(knowledgeBase, originalKnowledgeBase);
        
        updateKnowledgeBaseFields(knowledgeBase, dto);
        return updateDatasetIfNeeded(knowledgeBase, dto, originalKnowledgeBase);
    }

    private void updateKnowledgeBaseFields(KnowledgeBase knowledgeBase, KnowledgeBaseDTO dto) {
        // 更新知识库信息
        if (StringUtils.isNotEmpty(dto.getName())) {
            knowledgeBase.setName(dto.getName());
        }
        if (dto.getDescription() != null) {
            knowledgeBase.setDescription(dto.getDescription());
        }
        if (StringUtils.isNotEmpty(dto.getAppName())) {
            knowledgeBase.setAppName(dto.getAppName());
        }
        if (StringUtils.isNotEmpty(dto.getType())) {
            knowledgeBase.setType(dto.getType());
            // 如果类型为OPEN，强制设置appName为空字符串，确保会被更新
            if (Constants.KnowledgeBaseType.OPEN.equalsIgnoreCase(dto.getType())) {
                knowledgeBase.setAppName("");
            }
        }
        if (StringUtils.isNotEmpty(dto.getDatasetId())) {
            knowledgeBase.setDatasetId(dto.getDatasetId());
        }
    }

    private KnowledgeBaseDTO updateDatasetIfNeeded(KnowledgeBase knowledgeBase, KnowledgeBaseDTO dto, KnowledgeBase originalKnowledgeBase) {
        KnowledgeBaseDTO result = new KnowledgeBaseDTO();
        BeanUtils.copyProperties(knowledgeBase, result);

        try {
            // 无论是否有配置更新，都需要更新基础字段和时间戳
            knowledgeBase.setUpdateTime(LocalDateTime.now());
            
            // 检查是否需要更新远程数据集
            boolean needUpdateRemote = false;
            
            // 检查基础字段是否发生变化
            boolean basicFieldsChanged = isBasicFieldsChanged(dto, originalKnowledgeBase);
            
            // 检查是否有数据集配置更新
            boolean hasConfigUpdate = dto.getDatasetConfig() != null;
            
            needUpdateRemote = basicFieldsChanged || hasConfigUpdate;
            
            if (needUpdateRemote) {
                updateRemoteDataset(knowledgeBase, dto, originalKnowledgeBase);
                
                // 如果有配置更新，保存完整的配置到本地
                if (hasConfigUpdate) {
                    knowledgeBase.setDatasetConfig(dto.getDatasetConfig());
                }
            }
            
            // 更新数据库
            knowledgeBaseMapper.updateById(knowledgeBase);
            
            // 更新返回结果
            BeanUtils.copyProperties(knowledgeBase, result);
            
            return result;
        } catch (Exception e) {
            log.error("更新知识库数据集失败: {}", e.getMessage(), e);
            throw new ApiException("更新知识库数据集失败: " + e.getMessage());
        }
    }

    /**
     * 检查基础字段是否发生变化
     */
    private boolean isBasicFieldsChanged(KnowledgeBaseDTO dto, KnowledgeBase original) {
        // 检查名称是否变化（名称不能为空串）
        boolean nameChanged = dto.getName() != null && 
                             !dto.getName().trim().isEmpty() &&
                             !dto.getName().equals(original.getName());
        
        // 检查描述是否变化（描述可以为空串）
        boolean descriptionChanged = dto.getDescription() != null && 
                                   !Objects.equals(dto.getDescription(), original.getDescription());
        
        return nameChanged || descriptionChanged;
    }

    private void updateRemoteDataset(KnowledgeBase knowledgeBase, KnowledgeBaseDTO dto, KnowledgeBase original) throws Exception {
        ObjectNode filteredConfig;
        
        if (dto.getDatasetConfig() != null) {
            // 如果有配置更新，解析并过滤配置
            RagflowDatasetVO.DatasetVO datasetVO = objectMapper.readValue(dto.getDatasetConfig(), RagflowDatasetVO.DatasetVO.class);
            filteredConfig = filterIgnoredFields(datasetVO);
        } else {
            // 如果只是基础字段更新，创建包含基础字段的更新配置
            filteredConfig = objectMapper.createObjectNode();
            
            // 只更新发生变化的基础字段
            // 名称不能为空串
            if (dto.getName() != null && !dto.getName().trim().isEmpty() && !dto.getName().equals(original.getName())) {
                filteredConfig.put("name", dto.getName());
            }
            // 描述可以为空串
            if (dto.getDescription() != null && !Objects.equals(dto.getDescription(), original.getDescription())) {
                filteredConfig.put("description", dto.getDescription());
            }
        }
        
        String filteredConfigStr = objectMapper.writeValueAsString(filteredConfig);
        log.info("更新知识库数据集配置: {}:{}", knowledgeBase.getDatasetId(), filteredConfigStr);

        // 更新远程数据集
        String res = ragClient.updateDataset(knowledgeBase.getDatasetId(), filteredConfigStr);
        log.info("更新知识库数据集响应: {}", res);
        
        // 处理返回结果
        RagflowUtils.handleResponse(res, "更新知识库数据集失败");
    }

    @Override
    public KnowledgeBaseDTO getKnowledgeBase(Long id) {
        KnowledgeBase knowledgeBase = knowledgeBaseMapper.selectById(id);
        if (knowledgeBase == null) {
            throw new ApiException("知识库不存在");
        }

        KnowledgeBaseDTO dto = new KnowledgeBaseDTO();
        BeanUtils.copyProperties(knowledgeBase, dto);
        return dto;
    }

    /**
     * 获取用户所属的知识库ID列表
     */
    private List<Long> getUserMemberKbIds(Long userId) {
        return memberMapper.findByUserId(userId).stream()
                .map(KnowledgeBaseMember::getKbId)
                .collect(Collectors.toList());
    }

    /**
     * 构建开放类型和应用类型的组合查询条件
     */
    private void buildTypeConditions(LambdaQueryWrapper<KnowledgeBase> queryWrapper, 
                                   List<Long> memberKbIds,
                                   Long userId,
                                   boolean isSharedMode) {
        queryWrapper.and(wrapper -> wrapper
                .eq(KnowledgeBase::getType, Constants.KnowledgeBaseType.OPEN)
                .or(w -> w
                    .eq(KnowledgeBase::getType, Constants.KnowledgeBaseType.APP)
                    .and(sw -> {
                        if (isSharedMode) {
                            // 共享模式：在成员列表中且不是创建者
                            sw.in(!memberKbIds.isEmpty(), KnowledgeBase::getId, memberKbIds)
                              .ne(KnowledgeBase::getOwnerId, userId);
                        } else {
                            // 普通模式：是创建者或是成员
                            sw.eq(KnowledgeBase::getOwnerId, userId)
                              .or()
                              .in(!memberKbIds.isEmpty(), KnowledgeBase::getId, memberKbIds);
                        }
                    })
                )
        );
    }

    /**
     * 添加关键字搜索条件
     */
    private void addKeywordCondition(LambdaQueryWrapper<KnowledgeBase> queryWrapper, String keyword) {
        if (keyword != null && !keyword.isEmpty()) {
            queryWrapper.and(wrapper -> wrapper
                    .like(KnowledgeBase::getName, keyword)
                    .or()
                    .like(KnowledgeBase::getDescription, keyword)
            );
        }
    }

    /**
     * 批量获取知识库的文档数量
     */
    private Map<Long, Long> batchGetDocumentCounts(List<Long> kbIds) {
        if (kbIds.isEmpty()) {
            return new HashMap<>();
        }
        // 从 KbFileRel 表中统计文档数量
        return knowledgeBaseMapper.batchCountDocuments(kbIds);
    }

    /**
     * 批量获取知识库的成员数量
     */
    private Map<Long, Long> batchGetMemberCounts(List<Long> kbIds) {
        if (kbIds.isEmpty()) {
            return new HashMap<>();
        }
        return memberMapper.batchCountByKbIds(kbIds);
    }

    /**
     * 批量获取用户在知识库中的角色
     */
    private Map<Long, KnowledgeBaseMember> batchGetUserRoles(List<Long> kbIds, Long userId) {
        if (kbIds.isEmpty()) {
            return new HashMap<>();
        }
        return memberMapper.findByKbIdsAndUserId(kbIds, userId).stream()
                .collect(Collectors.toMap(KnowledgeBaseMember::getKbId, member -> member));
    }

    /**
     * 批量转换知识库列表为DTO
     */
    private List<KnowledgeBaseDTO> batchConvertToDTO(List<KnowledgeBase> kbList, Long userId) {
        if (kbList.isEmpty()) {
            return new ArrayList<>();
        }

        // 获取所有知识库ID
        List<Long> kbIds = kbList.stream()
                .map(KnowledgeBase::getId)
                .collect(Collectors.toList());

        // 批量获取各种统计数据
        Map<Long, Long> documentCounts = batchGetDocumentCounts(kbIds);
        Map<Long, Long> memberCounts = batchGetMemberCounts(kbIds);
        Map<Long, KnowledgeBaseMember> userRoles = batchGetUserRoles(kbIds, userId);

        // 批量转换
        return kbList.stream().map(kb -> {
            KnowledgeBaseDTO dto = new KnowledgeBaseDTO();
            BeanUtils.copyProperties(kb, dto);
            
            // 设置文档数量
            dto.setDocumentCount(documentCounts.getOrDefault(kb.getId(), 0L));
            
            // 设置成员数量
            dto.setMemberCount(memberCounts.getOrDefault(kb.getId(), 0L));
            
            // 设置是否是所有者
            KnowledgeBaseMember member = userRoles.get(kb.getId());
            dto.setIsOwner(member != null && member.getRole() >= Constants.KnowledgeBaseMemberRole.OWNER);
            
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public PageResult<KnowledgeBaseDTO> listKnowledgeBases(Integer pageNum, Integer pageSize, String keyword, String type, Long userId) {
        // 获取用户信息
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ApiException("用户不存在");
        }

        // 构建查询条件
        LambdaQueryWrapper<KnowledgeBase> queryWrapper = new LambdaQueryWrapper<>();
        
        // 根据type参数过滤
        if ("owned".equals(type)) {
            // 只查询用户创建的知识库
            queryWrapper.eq(KnowledgeBase::getOwnerId, userId);
        } else {
            List<Long> memberKbIds = getUserMemberKbIds(userId);
            
            if ("shared".equals(type)) {
                if (memberKbIds.isEmpty()) {
                    // 如果没有任何知识库成员关系，返回空结果
                    return createEmptyResult(pageNum, pageSize);
                }
                buildTypeConditions(queryWrapper, memberKbIds, userId, true);
            } else if (user.getRole() < Constants.UserRole.ADMIN) {
                // 非管理员用户查看所有知识库时需要权限控制
                buildTypeConditions(queryWrapper, memberKbIds, userId, false);
            }
        }

        // 添加关键字搜索条件
        addKeywordCondition(queryWrapper, keyword);

        // 分页查询
        IPage<KnowledgeBase> page = knowledgeBaseMapper.selectPage(new Page<>(pageNum, pageSize), queryWrapper);

        // 批量转换为DTO并填充额外信息
        List<KnowledgeBaseDTO> dtoList = batchConvertToDTO(page.getRecords(), userId);

        // 构建分页结果
        return createPageResult(page, dtoList);
    }

    /**
     * 创建空的分页结果
     */
    private PageResult<KnowledgeBaseDTO> createEmptyResult(Integer pageNum, Integer pageSize) {
        PageResult<KnowledgeBaseDTO> emptyResult = new PageResult<>();
        emptyResult.setPageNum(pageNum.longValue());
        emptyResult.setPageSize(pageSize.longValue());
        emptyResult.setTotal(0L);
        emptyResult.setTotalPage(0L);
        emptyResult.setList(List.of());
        return emptyResult;
    }

    /**
     * 创建分页结果
     */
    private PageResult<KnowledgeBaseDTO> createPageResult(IPage<KnowledgeBase> page, List<KnowledgeBaseDTO> dtoList) {
        PageResult<KnowledgeBaseDTO> result = new PageResult<>();
        result.setPageNum(page.getCurrent());
        result.setPageSize(page.getSize());
        result.setTotal(page.getTotal());
        result.setTotalPage(page.getPages());
        result.setList(dtoList);
        return result;
    }

    @Override
    public List<KnowledgeBaseDTO> getUserKnowledgeBases(Long userId) {
        // 获取用户有权限的知识库ID列表
        List<Long> kbIds = memberMapper.findByUserId(userId).stream()
                .map(KnowledgeBaseMember::getKbId)
                .collect(Collectors.toList());

        if (kbIds.isEmpty()) {
            return List.of();
        }

        // 查询知识库信息
        return knowledgeBaseMapper.selectBatchIds(kbIds).stream()
                .map(kb -> {
                    KnowledgeBaseDTO dto = new KnowledgeBaseDTO();
                    BeanUtils.copyProperties(kb, dto);
                    return dto;
                })
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addMember(Long operatorId, KnowledgeBaseMemberDTO dto) {
        // 检查操作者是否有权限添加成员
        if (!checkPermission(dto.getKbId(), operatorId, Constants.KnowledgeBaseMemberRole.ADMIN)) {
            throw new ApiException("没有权限添加成员");
        }

        // 检查用户是否存在
        if (userMapper.selectById(dto.getUserId()) == null) {
            throw new ApiException("用户不存在");
        }

        // 检查是否已经是成员（包括已删除的记录）
        KnowledgeBaseMember existingMember = memberMapper.findByKbIdAndUserIdWithDeleted(dto.getKbId(), dto.getUserId());
        if (existingMember != null) {
            // 如果是已删除状态，则恢复并更新角色
            if (existingMember.getDeleted() == 1) {
                // 使用自定义SQL更新已删除的记录
                int updated = memberMapper.restoreDeletedMember(
                    existingMember.getId(),
                    dto.getRole(),
                    LocalDateTime.now()
                );
                
                if (updated == 0) {
                    throw new ApiException("恢复成员失败，请重试");
                }
                return;
            }
            // 如果不是已删除状态，说明是活跃成员
            throw new ApiException("用户已经是该知识库成员");
        }

        // 添加新成员
        KnowledgeBaseMember member = new KnowledgeBaseMember();
        member.setKbId(dto.getKbId());
        member.setUserId(dto.getUserId());
        member.setRole(dto.getRole());
        member.setCreateTime(LocalDateTime.now());
        member.setUpdateTime(LocalDateTime.now());
        member.setDeleted(0);
        
        memberMapper.insert(member);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMemberRole(Long operatorId, KnowledgeBaseMemberDTO dto) {
        // 检查操作者是否有权限修改成员角色
        if (!checkPermission(dto.getKbId(), operatorId, Constants.KnowledgeBaseMemberRole.ADMIN)) {
            throw new ApiException("没有权限修改成员角色");
        }

        // 检查成员是否存在
        KnowledgeBaseMember member = memberMapper.findByKbIdAndUserId(dto.getKbId(), dto.getUserId());
        if (member == null) {
            throw new ApiException("成员不存在");
        }

        // 更新角色
        member.setRole(dto.getRole());
        member.setUpdateTime(LocalDateTime.now());
        memberMapper.updateById(member);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeMember(Long operatorId, Long kbId, Long userId) {
        // 检查操作者是否有权限移除成员
        if (!checkPermission(kbId, operatorId, Constants.KnowledgeBaseMemberRole.ADMIN)) {
            throw new ApiException("没有权限移除成员");
        }

        // 检查是否是移除自己
        if (operatorId.equals(userId)) {
            throw new ApiException("不能移除自己");
        }

        // 检查成员是否存在
        KnowledgeBaseMember member = memberMapper.findByKbIdAndUserId(kbId, userId);
        if (member == null) {
            throw new ApiException("成员不存在");
        }

        // 移除成员
        memberMapper.deleteById(member.getId());
    }

    @Override
    public List<KnowledgeBaseMemberDTO> listMembers(Long kbId) {
        return memberMapper.findMembersWithUserInfo(kbId).stream()
                .map(member -> {
                    KnowledgeBaseMemberDTO dto = new KnowledgeBaseMemberDTO();
                    dto.setKbId(member.getKbId());
                    dto.setUserId(member.getUserId());
                    dto.setRole(member.getRole());
                    dto.setUsername(member.getUsername());
                    dto.setNickname(member.getNickname());
                    dto.setAvatar(member.getAvatar());
                    return dto;
                })
                .collect(Collectors.toList());
    }

    @Override
    public boolean checkPermission(Long kbId, Long userId, Integer role) {
        // 检查用户信息
        User user = userMapper.selectById(userId);
        if (user == null) {
            return false;
        }
        
        // 如果是超级管理员，无需校验成员角色，直接返回 true
        if (user.getRole().equals(Constants.UserRole.SUPER_ADMIN)) {
            return true;
        }
        
        // 检查知识库是否存在
        if (knowledgeBaseMapper.selectById(kbId) == null) {
            return false;
        }

        // 检查用户是否是成员
        KnowledgeBaseMember member = memberMapper.findByKbIdAndUserId(kbId, userId);
        if (member == null) {
            return false;
        }

        // 检查角色权限
        return member.getRole() >= role;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transferOwnership(Long kbId, Long operatorId, Long newOwnerId) {
        // 检查操作者是否是当前owner
        if (!checkPermission(kbId, operatorId, Constants.KnowledgeBaseMemberRole.OWNER)) {
            throw new ApiException("只有所有者可以转移所有权");
        }

        // 检查新owner是否存在
        if (userMapper.selectById(newOwnerId) == null) {
            throw new ApiException("新所有者不存在");
        }

        // 检查新owner是否是当前成员
        KnowledgeBaseMember newOwnerMember = memberMapper.findByKbIdAndUserId(kbId, newOwnerId);
        if (newOwnerMember == null) {
            throw new ApiException("新所有者必须是知识库成员");
        }

        // 更新原owner角色为admin
        KnowledgeBaseMember oldOwnerMember = memberMapper.findByKbIdAndUserId(kbId, operatorId);
        oldOwnerMember.setRole(Constants.KnowledgeBaseMemberRole.ADMIN);
        oldOwnerMember.setUpdateTime(LocalDateTime.now());
        memberMapper.updateById(oldOwnerMember);

        // 更新新owner角色为owner
        newOwnerMember.setRole(Constants.KnowledgeBaseMemberRole.OWNER);
        newOwnerMember.setUpdateTime(LocalDateTime.now());
        memberMapper.updateById(newOwnerMember);

        // 更新知识库的ownerId
        KnowledgeBase knowledgeBase = knowledgeBaseMapper.selectById(kbId);
        knowledgeBase.setOwnerId(newOwnerId);
        knowledgeBase.setUpdateTime(LocalDateTime.now());
        knowledgeBaseMapper.updateById(knowledgeBase);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(Long id, Integer status) {
        KnowledgeBase knowledgeBase = knowledgeBaseMapper.selectById(id);
        if (knowledgeBase == null) {
            throw new ApiException("知识库不存在");
        }

        knowledgeBase.setStatus(status);
        knowledgeBase.setUpdateTime(LocalDateTime.now());
        knowledgeBaseMapper.updateById(knowledgeBase);
    }

    @Override
    public DashboardStatsDTO getDashboardStats(Long userId) {
        // 验证用户是否存在
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ApiException("用户不存在");
        }

        // 计算本周开始和结束时间
        LocalDateTime[] weekRange = getThisWeekRange();
        LocalDateTime weekStart = weekRange[0];
        LocalDateTime weekEnd = weekRange[1];

        DashboardStatsDTO stats = new DashboardStatsDTO();

        // 判断是否为超级管理员
        boolean isSuperAdmin = user.getRole().equals(Constants.UserRole.SUPER_ADMIN);

        if (isSuperAdmin) {
            // 超级管理员查看所有数据
            // 1. 统计知识库总数
            stats.setKnowledgeBaseCount(knowledgeBaseMapper.countAllKnowledgeBases());

            // 2. 统计文档总数
            stats.setDocumentCount(knowledgeBaseMapper.countAllDocuments());

            // 3. 统计成员总数
            stats.setMemberCount(knowledgeBaseMapper.countAllKnowledgeBaseMembers());

            // 4. 统计API密钥总数
            LambdaQueryWrapper<ApiKey> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ApiKey::getDeleted, 0);
            stats.setApiKeyCount(apiKeyMapper.selectCount(queryWrapper));

            // 5. 本周新增/删除统计
            stats.setWeeklyNewKnowledgeBaseCount(knowledgeBaseMapper.countAllWeeklyNewKnowledgeBases(weekStart, weekEnd));
            stats.setWeeklyDeletedKnowledgeBaseCount(knowledgeBaseMapper.countAllWeeklyDeletedKnowledgeBases(weekStart, weekEnd));
            stats.setWeeklyNewDocumentCount(knowledgeBaseMapper.countAllWeeklyNewDocuments(weekStart, weekEnd));
            stats.setWeeklyDeletedDocumentCount(knowledgeBaseMapper.countAllWeeklyDeletedDocuments(weekStart, weekEnd));
            stats.setWeeklyNewMemberCount(knowledgeBaseMapper.countAllWeeklyNewMembers(weekStart, weekEnd));
            stats.setWeeklyDeletedMemberCount(knowledgeBaseMapper.countAllWeeklyDeletedMembers(weekStart, weekEnd));
        } else {
            // 普通用户查看自己有权限的数据
            // 1. 统计知识库总数
            stats.setKnowledgeBaseCount(knowledgeBaseMapper.countUserKnowledgeBases(userId));

            // 2. 统计文档总数（从 KbFileRel 表中统计）
            stats.setDocumentCount(knowledgeBaseMapper.countUserDocuments(userId));

            // 3. 统计成员总数
            stats.setMemberCount(knowledgeBaseMapper.countUserKnowledgeBaseMembers(userId));

            // 4. 统计API密钥总数
            LambdaQueryWrapper<ApiKey> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ApiKey::getCreatorId, userId)
                       .eq(ApiKey::getDeleted, 0);
            stats.setApiKeyCount(apiKeyMapper.selectCount(queryWrapper));

            // 5. 本周新增/删除统计
            stats.setWeeklyNewKnowledgeBaseCount(knowledgeBaseMapper.countUserWeeklyNewKnowledgeBases(userId, weekStart, weekEnd));
            stats.setWeeklyDeletedKnowledgeBaseCount(knowledgeBaseMapper.countUserWeeklyDeletedKnowledgeBases(userId, weekStart, weekEnd));
            stats.setWeeklyNewDocumentCount(knowledgeBaseMapper.countUserWeeklyNewDocuments(userId, weekStart, weekEnd));
            stats.setWeeklyDeletedDocumentCount(knowledgeBaseMapper.countUserWeeklyDeletedDocuments(userId, weekStart, weekEnd));
            stats.setWeeklyNewMemberCount(knowledgeBaseMapper.countUserWeeklyNewMembers(userId, weekStart, weekEnd));
            stats.setWeeklyDeletedMemberCount(knowledgeBaseMapper.countUserWeeklyDeletedMembers(userId, weekStart, weekEnd));
        }

        return stats;
    }

    /**
     * 获取本周的开始和结束时间
     * @return [开始时间, 结束时间]
     */
    private LocalDateTime[] getThisWeekRange() {
        LocalDateTime now = LocalDateTime.now();
        // 获取本周一的开始时间（00:00:00）
        LocalDateTime weekStart = now.toLocalDate()
                .atStartOfDay()
                .with(DayOfWeek.MONDAY);
        // 获取下周一的开始时间（00:00:00）
        LocalDateTime weekEnd = weekStart.plusWeeks(1);
        
        return new LocalDateTime[]{weekStart, weekEnd};
    }

    @Override
    public List<KnowledgeBaseDTO> listKnowledgeBaseNames(String keyword, Long userId) {
        // 获取用户信息
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ApiException("用户不存在");
        }

        // 构建查询条件
        LambdaQueryWrapper<KnowledgeBase> queryWrapper = new LambdaQueryWrapper<>();
        // 只查询ID、名称和datasetId字段，提高性能
        queryWrapper.select(KnowledgeBase::getId, KnowledgeBase::getName, KnowledgeBase::getDatasetId);
        
        // 添加关键字搜索条件
        if (keyword != null && !keyword.isEmpty()) {
            queryWrapper.like(KnowledgeBase::getName, keyword);
        }

        List<Long> memberKbIds = getUserMemberKbIds(userId);
        // 非管理员用户查看所有知识库时需要权限控制
        buildTypeConditions(queryWrapper, memberKbIds, userId, false);

        // 查询并转换为DTO
        return knowledgeBaseMapper.selectList(queryWrapper).stream()
                .map(kb -> {
                    KnowledgeBaseDTO dto = new KnowledgeBaseDTO();
                    dto.setId(kb.getId());
                    dto.setName(kb.getName());
                    dto.setDatasetId(kb.getDatasetId());
                    return dto;
                })
                .collect(Collectors.toList());
    }
}