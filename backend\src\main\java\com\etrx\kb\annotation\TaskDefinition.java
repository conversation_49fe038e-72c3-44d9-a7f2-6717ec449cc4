package com.etrx.kb.annotation;

import java.lang.annotation.*;

/**
 * 任务定义注解
 * 用于标记方法为可执行任务，在应用启动时自动扫描并注册到任务定义表
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface TaskDefinition {

    /**
     * 任务名称（必填）
     * 唯一标识任务，如果不指定则使用 类名.方法名
     */
    String taskName() default "";

    /**
     * 任务类型（可选）
     * 用于分类管理任务，默认为 DEFAULT
     */
    String taskType() default "DEFAULT";

    /**
     * 执行线程数（可选）
     * 默认为 2 个线程
     */
    int threadCount() default 2;

    /**
     * 最大执行个数（可选）
     * 默认为 1000 个
     */
    int maxExecutionCount() default 1000;

    /**
     * 最大重试次数（可选）
     * 默认为 3 次
     */
    int maxRetryCount() default 3;

    /**
     * 任务描述（可选）
     */
    String description() default "";
} 