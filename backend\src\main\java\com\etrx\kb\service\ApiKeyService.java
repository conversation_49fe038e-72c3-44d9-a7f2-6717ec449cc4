package com.etrx.kb.service;

import com.etrx.kb.common.PageResult;
import com.etrx.kb.dto.ApiKeyDTO;

import java.util.List;

/**
 * API密钥服务接口
 */
public interface ApiKeyService {

    /**
     * 创建API密钥
     *
     * @param appName   应用名称
     * @param creatorId 创建者ID
     * @return API密钥信息
     */
    ApiKeyDTO createApiKey(String appName, Long creatorId);

    /**
     * 获取API密钥信息
     *
     * @param id API密钥ID
     * @return API密钥信息
     */
    ApiKeyDTO getApiKey(Long id);

    /**
     * 删除API密钥
     *
     * @param id        API密钥ID
     * @param operatorId 操作者ID
     */
    void deleteApiKey(Long id, Long operatorId);

    /**
     * 更新API密钥状态
     *
     * @param id        API密钥ID
     * @param status    状态(0:禁用,1:启用)
     * @param operatorId 操作者ID
     */
    void updateStatus(Long id, Integer status, Long operatorId);

    /**
     * 分页查询API密钥列表
     *
     * @param pageNum   页码
     * @param pageSize  每页数量
     * @param keyword   搜索关键字
     * @param userId    用户ID
     * @return API密钥分页列表
     */
    PageResult<ApiKeyDTO> listApiKeys(Integer pageNum, Integer pageSize, String keyword, Long userId);

    /**
     * 验证API密钥
     *
     * @param apiKey API密钥
     * @return 是否有效
     */
    boolean validateApiKey(String apiKey);

    /**
     * 获取用户的所有API密钥
     *
     * @param userId 用户ID
     * @return API密钥列表
     */
    List<ApiKeyDTO> getUserApiKeys(Long userId);

    /**
     * 根据API密钥获取应用名称
     *
     * @param apiKey API密钥
     * @return 应用名称
     */
    String getAppNameByApiKey(String apiKey);

    /**
     * 根据API密钥获取应用ID
     *
     * @param apiKey API密钥
     * @return 应用ID
     */
    Long getAppIdByApiKey(String apiKey);

    /**
     * 根据API密钥获取应用信息
     *
     * @param apiKey API密钥
     * @return 应用信息(包含ID和名称)
     */
    ApiKeyDTO getAppInfoByApiKey(String apiKey);
}