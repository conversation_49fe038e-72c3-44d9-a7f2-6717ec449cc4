# 任务执行机制使用说明

## 概述

本系统实现了一个完整的异步任务执行机制，用于处理需要长时间运行或需要重试的任务，特别是与RAGFlow相关的操作。

## 核心组件

### 1. 数据库表结构

- **tb_task_definition**: 任务定义表，存储任务的元信息
- **tb_task_queue**: 任务队列表，存储待执行的任务
- **tb_task_execution_record**: 任务执行记录表，记录每次任务执行的详细信息
- **tb_task_sequence**: 任务序列表，用于分布式主机的任务分配

### 2. 核心类和接口

- **@TaskDefinition**: 任务定义注解，标记可执行任务的方法
- **TaskDefinitionScanner**: 任务定义扫描器，应用启动时自动扫描注册任务
- **TaskExecutionService**: 任务执行服务，负责任务的提交和执行
- **TaskSchedulerService**: 任务调度服务，定时扫描任务队列
- **TaskScheduler**: 定时任务调度器，每30秒扫描一次任务队列

### 3. 线程池配置

- **核心线程数**: 2
- **最大线程数**: 100
- **队列**: 无界队列
- **拒绝策略**: 当线程池满时，将任务插入到任务队列表中

## 使用方法

### 1. 定义任务

使用`@TaskDefinition`注解标记任务方法：

```java
@TaskDefinition(
    taskName = "uploadFileToRagflow",
    taskType = "RAGFLOW",
    threadCount = 5,
    maxExecutionCount = 1000,
    maxRetryCount = 3,
    description = "上传文件到Ragflow知识库"
)
public String uploadFileToRagflow(String businessData) {
    // 任务执行逻辑
    return "执行结果";
}
```

### 2. 提交任务

#### 直接执行（业务触发）
```java
// 构建业务数据
Map<String, Object> taskData = new HashMap<>();
taskData.put("key1", "value1");
String businessData = objectMapper.writeValueAsString(taskData);

// 直接执行任务
taskExecutionService.executeTaskByName("taskName", businessData, userId);
```

#### 提交到队列
```java
// 提交任务到队列，由定时任务扫描执行
Long taskQueueId = taskExecutionService.submitTaskByName("taskName", businessData, userId);
```

### 3. 任务数据格式

任务业务数据使用JSON格式，根据具体任务需要包含不同字段：

```json
{
    "kbId": 1,
    "fileIds": [1, 2, 3],
    "userId": 1,
    "status": "active"
}
```

## 分布式支持

### 主机配置
```yml
task:
  scheduler:
    enabled: true
    host-count: 5      # 主机数量
    batch-size: 100    # 每次扫描的任务数量
    fixed-delay: 30000 # 扫描间隔（毫秒）
```

### 任务分配机制
- 使用任务队列ID对主机数量取模来分配任务
- 每台主机通过序列号获取属于自己的任务区间
- 支持动态调整主机数量

## 任务状态管理

### 任务队列状态
- **0**: 待执行
- **1**: 执行中
- **2**: 执行成功
- **3**: 执行失败
- **4**: 已取消

### 任务执行记录状态
- **0**: 待执行
- **1**: 执行中
- **2**: 执行成功
- **3**: 执行失败
- **4**: 已取消

## 重试机制

- 任务失败时会自动重试
- 重试次数由任务定义中的`maxRetryCount`控制
- 超过最大重试次数后，任务标记为最终失败
- 重试间隔为5分钟

## 监控和日志

### 执行记录
- 每次任务执行都会记录详细信息
- 包括开始时间、结束时间、执行时长、结果数据等
- 记录执行线程名称、主机名、主机IP等环境信息

### 日志记录
- 任务开始和结束时会记录日志
- 任务失败时会记录错误信息
- 支持查看任务执行历史

## 最佳实践

1. **任务幂等性**: 确保任务可以重复执行而不产生副作用
2. **任务超时**: 避免任务长时间运行阻塞线程池
3. **资源管理**: 任务中使用的资源要及时释放
4. **错误处理**: 妥善处理任务中的异常情况
5. **监控告警**: 监控任务执行状态和失败率

## 配置参数

```yml
task:
  scheduler:
    enabled: true          # 是否启用任务调度
    host-count: 5          # 主机数量
    batch-size: 100        # 每次扫描的任务数量
    fixed-delay: 30000     # 扫描间隔（毫秒）
```