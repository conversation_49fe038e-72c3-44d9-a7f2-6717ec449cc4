<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.etrx.kb.mapper.TaskSequenceMapper">
    
    <!-- 获取并更新序列号 -->
    <update id="updateSequence">
        UPDATE tb_task_sequence 
        SET current_value = current_value + step,
            update_time = NOW()
        WHERE sequence_name = #{sequenceName}
        and current_value = #{currentSequence}
    </update>

    <select id="getNextSequence" resultType="java.lang.Long">
        SELECT current_value
        FROM tb_task_sequence
        WHERE sequence_name = #{sequenceName}
    </select>
    
</mapper> 