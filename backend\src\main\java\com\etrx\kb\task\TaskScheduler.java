package com.etrx.kb.task;

import com.etrx.kb.service.TaskSchedulerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 任务调度器
 * 定时扫描任务队列中的待执行任务
 */
@Slf4j
@Component
@EnableScheduling
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "task.scheduler", name = "enabled", havingValue = "true", matchIfMissing = true)
public class TaskScheduler {

    private final TaskSchedulerService taskSchedulerService;

    /**
     * 定时扫描任务队列
     * 默认每30秒执行一次，可通过配置文件修改
     */
    @Scheduled(fixedDelayString = "${task.scheduler.fixed-delay:30000}")
    public void scanTasks() {
        log.debug("开始扫描任务队列...");
        
        try {
            taskSchedulerService.scanAndExecuteTasks();
        } catch (Exception e) {
            log.error("扫描任务队列失败", e);
        }
        
        log.debug("扫描任务队列完成");
    }
} 