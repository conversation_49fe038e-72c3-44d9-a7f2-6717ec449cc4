package com.etrx.kb.controller;

import com.etrx.kb.common.Result;
import com.etrx.kb.dto.FolderDTO;
import com.etrx.kb.dto.FolderTreeDTO;
import com.etrx.kb.service.FolderService;
import com.etrx.kb.util.SecurityUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 文件夹控制器
 */
@RestController
@RequestMapping("/aikb/folders")
@Tag(name = "文件夹控制器(废弃)", description = "文件夹控制器(废弃)")
@RequiredArgsConstructor
public class FolderController {

    private final FolderService folderService;
    private final SecurityUtils securityUtils;

    /**
     * 创建文件夹
     */
    @PostMapping
    public Result<FolderDTO> createFolder(
            @RequestParam Long kbId,
            @RequestParam(required = false) Long parentId,
            @RequestParam String name) {
        return Result.success(folderService.createFolder(
                kbId, parentId, name, securityUtils.getCurrentUserId()));
    }

    /**
     * 获取文件夹信息
     */
    @GetMapping("/{id}")
    public Result<FolderDTO> getFolder(@PathVariable Long id) {
        return Result.success(folderService.getFolder(id));
    }

    /**
     * 删除文件夹
     */
    @DeleteMapping("/{id}")
    public Result<?> deleteFolder(@PathVariable Long id) {
        folderService.deleteFolder(id, securityUtils.getCurrentUserId());
        return Result.success();
    }

    /**
     * 移动文件夹
     */
    @PutMapping("/{id}/move")
    public Result<?> moveFolder(
            @PathVariable Long id,
            @RequestParam(required = false) Long parentId) {
        folderService.moveFolder(id, parentId, securityUtils.getCurrentUserId());
        return Result.success();
    }

    /**
     * 重命名文件夹
     */
    @PutMapping("/{id}/rename")
    public Result<?> renameFolder(
            @PathVariable Long id,
            @RequestParam String newName) {
        folderService.renameFolder(id, newName, securityUtils.getCurrentUserId());
        return Result.success();
    }

    /**
     * 获取文件夹树
     */
    @GetMapping("/tree")
    public Result<List<FolderTreeDTO>> getFolderTree(@RequestParam Long kbId) {
        return Result.success(folderService.getFolderTree(kbId, securityUtils.getCurrentUserId()));
    }

    /**
     * 获取子文件夹列表
     */
    @GetMapping("/{parentId}/children")
    public Result<List<FolderDTO>> getSubFolders(@PathVariable Long parentId) {
        return Result.success(folderService.getSubFolders(parentId, securityUtils.getCurrentUserId()));
    }
}