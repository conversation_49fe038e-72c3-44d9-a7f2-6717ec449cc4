package com.etrx.kb.vo.base;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;
import java.util.Map;

/**
 * Ragflow基础VO类
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class RagflowBaseVO {
    private Integer code;
    private String message;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class BaseUploadFileVO {
        private String chunk_method;
        private String created_by;
        private String dataset_id;
        private String id;
        private String location;
        private String name;
        private BaseParserConfigVO parser_config;
        private String run;
        private Long size;
        private String thumbnail;
        private String type;
        private Integer chunk_count;
        private String create_date;
        private Long create_time;
        private Map<String, String> meta_fields;
        private String process_begin_at;
        private Double process_duation;
        private Double progress;
        private String progress_msg;
        private String source_type;
        private String status;
        private Integer token_count;
        private String update_date;
        private Long update_time;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class BaseParserConfigVO {
        private Integer chunk_token_num;
        private String delimiter;
        private Boolean html4excel;
        private String layout_recognize;
        private BaseRaptorVO raptor;
        private Integer auto_keywords;
        private Integer auto_questions;
        private List<Object> pages;
        private Integer task_page_size;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class BaseRaptorVO {
        private Boolean use_raptor;
        private Integer max_cluster;
        private Integer max_token;
        private String prompt;
        private Integer random_seed;
        private Double threshold;
    }
} 