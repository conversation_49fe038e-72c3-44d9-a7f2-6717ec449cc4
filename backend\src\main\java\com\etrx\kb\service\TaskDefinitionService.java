package com.etrx.kb.service;

import com.etrx.kb.domain.TaskDefinition;

/**
 * 任务定义服务接口
 */
public interface TaskDefinitionService {
    
    /**
     * 保存或更新任务定义
     * @param taskDefinition 任务定义
     */
    void saveOrUpdateTaskDefinition(TaskDefinition taskDefinition);
    
    /**
     * 根据任务名称获取任务定义
     * @param taskName 任务名称
     * @return 任务定义
     */
    TaskDefinition getByTaskName(String taskName);
    
    /**
     * 根据ID获取任务定义
     * @param id 任务ID
     * @return 任务定义
     */
    TaskDefinition getById(Long id);
} 