package com.etrx.kb.controller;

import com.etrx.kb.common.Result;
import com.etrx.kb.dto.LoginResponseDTO;
import com.etrx.kb.dto.UserLoginDTO;
import com.etrx.kb.dto.UserRegisterDTO;
import com.etrx.kb.dto.ThirdPartyLoginDTO;
import com.etrx.kb.service.UserService;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 认证控制器
 */
@RestController
@RequestMapping("/aikb/auth")
@RequiredArgsConstructor
public class AuthController {

    private final UserService userService;

    /**
     * 用户注册
     */
    @PostMapping("/register")
    public Result<?> register(@Valid @RequestBody UserRegisterDTO registerDTO) {
        return Result.success(userService.register(registerDTO));
    }

    /**
     * 用户登录
     */
    @PostMapping("/login")
    public Result<LoginResponseDTO> login(@Valid @RequestBody UserLoginDTO loginDTO) {
        return Result.success(userService.login(loginDTO));
    }

    /**
     * 第三方系统登录
     */
    @PostMapping("/app-login")
    public Result<LoginResponseDTO> thirdPartyLogin(@Valid @RequestBody ThirdPartyLoginDTO loginDTO) {
        return Result.success(userService.thirdPartyLogin(loginDTO.getApiKey(), loginDTO.getUserInfo()));
    }
}