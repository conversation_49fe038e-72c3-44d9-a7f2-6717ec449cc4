package com.etrx.kb.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 聊天会话映射实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ChatSessionMapping {

    /**
     * ID
     */
    private Long id;

    /**
     * 平台用户ID
     */
    private Long userId;

    /**
     * RAGflow会话ID
     */
    private String sessionId;

    /**
     * RAGflow聊天助手ID
     */
    private String chatAssistantId;

    /**
     * 会话名称
     */
    private String sessionName;

    /**
     * 会话消息记录(JSON)
     */
    private String message;

    /**
     * 会话引用记录(JSON)
     */
    private String reference;

    /**
     * 状态: 0-禁用, 1-启用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 状态枚举
     */
    public enum Status {
        DISABLED(0, "禁用"),
        ENABLED(1, "启用");

        private final Integer code;
        private final String description;

        Status(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
} 