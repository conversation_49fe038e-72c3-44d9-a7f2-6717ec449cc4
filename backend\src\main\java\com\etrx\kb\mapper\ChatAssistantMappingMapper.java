package com.etrx.kb.mapper;

import com.etrx.kb.domain.ChatAssistantMapping;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 聊天助手映射Mapper
 */
@Mapper
public interface ChatAssistantMappingMapper {

    /**
     * 插入聊天助手映射
     */
    @Insert("INSERT INTO chat_assistant_mapping (user_id, ragflow_chat_assistant_id, assistant_name, icon, " +
            "ragflow_knowledge_base_ids, local_knowledge_base_ids, assistant_type, status) " +
            "VALUES (#{userId}, #{ragflowChatAssistantId}, #{assistantName}, #{icon}, " +
            "#{ragflowKnowledgeBaseIds}, #{localKnowledgeBaseIds}, #{assistantType}, #{status})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(ChatAssistantMapping mapping);

    /**
     * 根据用户ID查询聊天助手列表
     */
    @Select("SELECT * FROM chat_assistant_mapping WHERE user_id = #{userId} AND status = 1 ORDER BY create_time DESC")
    List<ChatAssistantMapping> selectByUserId(Long userId);

    /**
     * 根据用户ID和助手ID查询
     */
    @Select("SELECT * FROM chat_assistant_mapping WHERE user_id = #{userId} AND ragflow_chat_assistant_id = #{assistantId}")
    ChatAssistantMapping selectByUserIdAndAssistantId(@Param("userId") Long userId, @Param("assistantId") String assistantId);

    /**
     * 根据ID查询
     */
    @Select("SELECT * FROM chat_assistant_mapping WHERE id = #{id}")
    ChatAssistantMapping selectById(Long id);

    /**
     * 更新状态
     */
    @Update("UPDATE chat_assistant_mapping SET status = #{status} WHERE id = #{id}")
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 更新助手信息
     */
    @Update("UPDATE chat_assistant_mapping SET assistant_name = #{assistantName}, icon = #{icon}, " +
            "ragflow_knowledge_base_ids = #{ragflowKbIds}, local_knowledge_base_ids = #{localKbIds} " +
            "WHERE user_id = #{userId} AND ragflow_chat_assistant_id = #{ragflowChatAssistantId}")
    int updateAssistantInfo(@Param("userId") Long userId, @Param("ragflowChatAssistantId") String ragflowChatAssistantId, 
                           @Param("assistantName") String assistantName, @Param("icon") String icon,
                           @Param("ragflowKbIds") String ragflowKbIds, @Param("localKbIds") String localKbIds);

    /**
     * 删除映射
     */
    @Delete("DELETE FROM chat_assistant_mapping WHERE id = #{id}")
    int deleteById(Long id);

    /**
     * 根据用户ID删除所有映射
     */
    @Delete("DELETE FROM chat_assistant_mapping WHERE user_id = #{userId}")
    int deleteByUserId(Long userId);

    /**
     * 根据用户ID统计数量
     */
    @Select("SELECT COUNT(*) FROM chat_assistant_mapping WHERE user_id = #{userId} AND status = 1")
    int countByUserId(Long userId);

    /**
     * 分页查询用户的聊天助手列表
     */
    @Select("SELECT * FROM chat_assistant_mapping WHERE user_id = #{userId} AND status = 1 ORDER BY create_time DESC LIMIT #{offset}, #{pageSize}")
    List<ChatAssistantMapping> selectByUserIdWithPage(@Param("userId") Long userId, @Param("offset") int offset, @Param("pageSize") int pageSize);
} 