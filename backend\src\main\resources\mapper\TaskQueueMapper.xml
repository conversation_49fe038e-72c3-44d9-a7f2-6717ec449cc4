<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.etrx.kb.mapper.TaskQueueMapper">
    
    <!-- 根据主机数量和序列号查询待执行任务 -->
    <select id="selectPendingTasksByModulo" resultType="com.etrx.kb.domain.TaskQueue">
        SELECT 
            id,
            task_definition_id,
            business_data,
            execution_status,
            retry_count,
            error_message,
            next_execution_time,
            created_by,
            create_time,
            update_time
        FROM tb_task_queue
        WHERE execution_status = 0
          AND (next_execution_time IS NULL OR next_execution_time &lt;= NOW())
          AND MOD(id, #{hostCount}) = MOD(#{sequenceNumber}, #{hostCount})
        ORDER BY create_time ASC
        LIMIT #{limit}
    </select>
    
</mapper> 