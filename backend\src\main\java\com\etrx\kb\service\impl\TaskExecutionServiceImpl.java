package com.etrx.kb.service.impl;

import com.etrx.kb.config.TaskThreadPoolConfig;
import com.etrx.kb.domain.TaskDefinition;
import com.etrx.kb.domain.TaskExecutionRecord;
import com.etrx.kb.domain.TaskQueue;
import com.etrx.kb.enums.TaskExecutionStatus;
import com.etrx.kb.mapper.TaskExecutionRecordMapper;
import com.etrx.kb.mapper.TaskQueueMapper;
import com.etrx.kb.service.TaskDefinitionService;
import com.etrx.kb.service.TaskExecutionService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Method;
import java.net.InetAddress;
import java.time.LocalDateTime;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 任务执行服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskExecutionServiceImpl implements TaskExecutionService {

    private final TaskQueueMapper taskQueueMapper;
    private final TaskExecutionRecordMapper taskExecutionRecordMapper;
    private final TaskDefinitionService taskDefinitionService;
    private final ThreadPoolExecutor taskExecutorThreadPool;
    private final ApplicationContext applicationContext;
    private final ObjectMapper objectMapper;

    @Override
    @Transactional
    public Long submitTask(Long taskDefinitionId, String businessData, Long createdBy) {
        TaskQueue taskQueue = new TaskQueue();
        taskQueue.setTaskDefinitionId(taskDefinitionId);
        taskQueue.setBusinessData(businessData);
        taskQueue.setExecutionStatus(TaskExecutionStatus.PENDING);
        taskQueue.setRetryCount(0);
        taskQueue.setCreatedBy(createdBy);
        taskQueue.setCreateTime(LocalDateTime.now());
        taskQueue.setUpdateTime(LocalDateTime.now());

        taskQueueMapper.insert(taskQueue);
        return taskQueue.getId();
    }

    @Override
    public void executeTaskDirectly(Long taskDefinitionId, String businessData, Long createdBy) {
        TaskDefinition taskDefinition = taskDefinitionService.getById(taskDefinitionId);
        if (taskDefinition == null) {
            log.error("任务定义不存在: {}", taskDefinitionId);
            return;
        }

        // 创建任务执行记录
        TaskExecutionRecord record = createExecutionRecord(null, taskDefinitionId, businessData, 
                TaskExecutionRecord.ExecutionSource.BUSINESS_TRIGGER, createdBy);
        
        // 创建任务执行器
        TaskExecutor executor = new TaskExecutor(taskDefinition, record, businessData);
        
        try {
            // 提交到线程池执行
            taskExecutorThreadPool.submit(executor);
        } catch (Exception e) {
            log.error("提交任务到线程池失败", e);
            // 如果提交失败，更新执行记录状态
            updateExecutionRecordStatus(record, TaskExecutionRecord.ExecutionStatus.FAILED, e.getMessage());
        }
    }

    @Override
    public void executeTask(TaskQueue taskQueue) {
        TaskDefinition taskDefinition = taskDefinitionService.getById(taskQueue.getTaskDefinitionId());
        if (taskDefinition == null) {
            log.error("任务定义不存在: {}", taskQueue.getTaskDefinitionId());
            return;
        }

        // 创建任务执行记录
        TaskExecutionRecord record = createExecutionRecord(taskQueue.getId(), taskQueue.getTaskDefinitionId(), 
                taskQueue.getBusinessData(), TaskExecutionRecord.ExecutionSource.SCHEDULED_SCAN, taskQueue.getCreatedBy());
        
        // 创建任务执行器
        TaskExecutor executor = new TaskExecutor(taskDefinition, record, taskQueue.getBusinessData());
        
        try {
            // 提交到线程池执行
            taskExecutorThreadPool.submit(executor);
        } catch (Exception e) {
            log.error("提交任务到线程池失败", e);
            // 如果提交失败，更新执行记录状态
            updateExecutionRecordStatus(record, TaskExecutionRecord.ExecutionStatus.FAILED, e.getMessage());
        }
    }

    @Override
    public void executeTaskByName(String taskName, String businessData, Long createdBy) {
        TaskDefinition taskDefinition = taskDefinitionService.getByTaskName(taskName);
        if (taskDefinition == null) {
            log.error("任务定义不存在: {}", taskName);
            return;
        }
        executeTaskDirectly(taskDefinition.getId(), businessData, createdBy);
    }

    @Override
    public Long submitTaskByName(String taskName, String businessData, Long createdBy) {
        TaskDefinition taskDefinition = taskDefinitionService.getByTaskName(taskName);
        if (taskDefinition == null) {
            log.error("任务定义不存在: {}", taskName);
            return null;
        }
        return submitTask(taskDefinition.getId(), businessData, createdBy);
    }

    /**
     * 创建任务执行记录
     */
    private TaskExecutionRecord createExecutionRecord(Long taskQueueId, Long taskDefinitionId, String businessData, 
                                                    Integer executionSource, Long createdBy) {
        TaskExecutionRecord record = new TaskExecutionRecord();
        record.setTaskQueueId(taskQueueId);
        record.setTaskDefinitionId(taskDefinitionId);
        record.setBusinessData(businessData);
        record.setExecutionSource(executionSource);
        record.setExecutionStatus(TaskExecutionRecord.ExecutionStatus.PENDING);
        record.setCreatedBy(createdBy);
        record.setCreateTime(LocalDateTime.now());
        record.setUpdateTime(LocalDateTime.now());

        // 获取主机信息
        try {
            InetAddress localHost = InetAddress.getLocalHost();
            record.setHostName(localHost.getHostName());
            record.setHostIp(localHost.getHostAddress());
        } catch (Exception e) {
            log.warn("获取主机信息失败", e);
        }

        taskExecutionRecordMapper.insert(record);
        return record;
    }

    /**
     * 更新任务执行记录状态
     */
    private void updateExecutionRecordStatus(TaskExecutionRecord record, Integer status, String errorMessage) {
        record.setExecutionStatus(status);
        record.setErrorMessage(errorMessage);
        record.setUpdateTime(LocalDateTime.now());
        taskExecutionRecordMapper.updateById(record);
    }

    /**
     * 构建错误消息，确保不超过数据库字段长度限制
     */
    private String buildErrorMessage(Exception e) {
        if (e == null) {
            return null;
        }

        StringBuilder errorMsg = new StringBuilder();

        // 添加异常类型和消息
        errorMsg.append(e.getClass().getSimpleName()).append(": ");
        if (e.getMessage() != null) {
            errorMsg.append(e.getMessage());
        }

        // 添加根本原因
        Throwable cause = e.getCause();
        if (cause != null && cause != e) {
            errorMsg.append(" | Caused by: ").append(cause.getClass().getSimpleName());
            if (cause.getMessage() != null) {
                errorMsg.append(": ").append(cause.getMessage());
            }
        }

        // 限制长度，假设数据库字段长度为2000字符
        String result = errorMsg.toString();
        if (result.length() > 2000) {
            result = result.substring(0, 1997) + "...";
        }

        return result;
    }

    /**
     * 构建错误消息（字符串版本）
     */
    private String buildErrorMessage(String errorMessage) {
        if (errorMessage == null) {
            return null;
        }

        // 限制长度
        if (errorMessage.length() > 2000) {
            return errorMessage.substring(0, 1997) + "...";
        }

        return errorMessage;
    }

    /**
     * 任务执行器
     */
    private class TaskExecutor implements Runnable, TaskThreadPoolConfig.TaskWrapper {
        private final TaskDefinition taskDefinition;
        private final TaskExecutionRecord record;
        private final String businessData;

        public TaskExecutor(TaskDefinition taskDefinition, TaskExecutionRecord record, String businessData) {
            this.taskDefinition = taskDefinition;
            this.record = record;
            this.businessData = businessData;
        }

        @Override
        public void run() {
            executeTaskInternal();
        }

        @Override
        public void handleRejection() {
            // 当线程池满时，将任务插入到任务队列表中
            try {
                Long taskQueueId = submitTask(taskDefinition.getId(), businessData, record.getCreatedBy());
                log.info("任务因线程池满被插入到任务队列: {}", taskQueueId);
            } catch (Exception e) {
                log.error("插入任务到队列失败", e);
                updateExecutionRecordStatus(record, TaskExecutionRecord.ExecutionStatus.FAILED, 
                        "线程池满且插入队列失败: " + e.getMessage());
            }
        }

        /**
         * 执行任务的内部方法
         */
        private void executeTaskInternal() {
            LocalDateTime startTime = LocalDateTime.now();
            record.setStartTime(startTime);
            record.setExecutionStatus(TaskExecutionRecord.ExecutionStatus.RUNNING);
            record.setThreadName(Thread.currentThread().getName());
            record.setUpdateTime(LocalDateTime.now());
            taskExecutionRecordMapper.updateById(record);

            try {
                // 通过反射执行任务
                Object result = invokeTaskMethod();
                
                // 执行成功
                LocalDateTime endTime = LocalDateTime.now();
                record.setEndTime(endTime);
                record.setExecutionDuration(java.time.Duration.between(startTime, endTime).toMillis());
                record.setExecutionStatus(TaskExecutionRecord.ExecutionStatus.SUCCESS);
                
                // 保存执行结果
                if (result != null) {
                    record.setResultData(objectMapper.writeValueAsString(result));
                }
                
                record.setUpdateTime(LocalDateTime.now());
                taskExecutionRecordMapper.updateById(record);
                
                log.info("任务执行成功: {}", taskDefinition.getTaskName());
                
            } catch (Exception e) {
                // 执行失败
                log.error("任务执行失败: {}", taskDefinition.getTaskName(), e);
                
                LocalDateTime endTime = LocalDateTime.now();
                record.setEndTime(endTime);
                record.setExecutionDuration(java.time.Duration.between(startTime, endTime).toMillis());
                record.setExecutionStatus(TaskExecutionRecord.ExecutionStatus.FAILED);
                record.setErrorMessage(e.getMessage());
                record.setUpdateTime(LocalDateTime.now());
                taskExecutionRecordMapper.updateById(record);
                
                // 如果是队列任务且重试次数未达到最大值，则重新加入队列
                if (record.getTaskQueueId() != null) {
                    handleTaskRetry();
                }
            }
        }

        /**
         * 通过反射调用任务方法
         */
        private Object invokeTaskMethod() throws Exception {
            // 获取目标类
            Class<?> clazz = Class.forName(taskDefinition.getClassPath());
            
            // 从Spring容器获取Bean实例
            Object bean = applicationContext.getBean(clazz);
            
            // 获取方法
            Method method = clazz.getMethod(taskDefinition.getMethodName(), String.class);
            
            // 执行方法
            return method.invoke(bean, businessData);
        }

        /**
         * 处理任务重试
         */
        private void handleTaskRetry() {
            try {
                TaskQueue taskQueue = taskQueueMapper.selectById(record.getTaskQueueId());
                if (taskQueue != null && taskQueue.getRetryCount() < taskDefinition.getMaxRetryCount()) {
                    // 增加重试次数
                    taskQueue.setRetryCount(taskQueue.getRetryCount() + 1);
                    taskQueue.setExecutionStatus(TaskExecutionStatus.PENDING);
                    taskQueue.setErrorMessage(record.getErrorMessage());
                    taskQueue.setNextExecutionTime(LocalDateTime.now().plusMinutes(5)); // 5分钟后重试
                    taskQueue.setUpdateTime(LocalDateTime.now());
                    taskQueueMapper.updateById(taskQueue);
                    
                    log.info("任务重试已安排: {}, 重试次数: {}", taskDefinition.getTaskName(), taskQueue.getRetryCount());
                } else {
                    // 达到最大重试次数，标记为失败
                    if (taskQueue != null) {
                        taskQueue.setExecutionStatus(TaskExecutionStatus.FAILED);
                        taskQueue.setUpdateTime(LocalDateTime.now());
                        taskQueueMapper.updateById(taskQueue);
                    }
                    log.error("任务达到最大重试次数，标记为失败: {}", taskDefinition.getTaskName());
                }
            } catch (Exception e) {
                log.error("处理任务重试失败", e);
            }
        }
    }
} 