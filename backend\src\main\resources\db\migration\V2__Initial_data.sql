-- 初始管理员账号 (密码: liutao)
INSERT INTO `tb_user` (`username`, `password`, `nickname`, `email`, `phone`, `role`, `status`)
VALUES ('admin', '$2a$10$rbz/y24dOjnpYKwwaAVcr.VZ7QIt9kX1CbJr/fLGjAqNgJPGL7S22', '系统管理员', '<EMAIL>', '13800000000', 3, 1);

-- 初始知识库
INSERT INTO `tb_knowledge_base` (`name`, `description`, `type`, `owner_id`, `status`)
VALUES ('系统知识库', '系统默认知识库', 'Open', 1, 1);

-- 初始文件夹结构
INSERT INTO `tb_folder` (`kb_id`, `name`, `parent_id`, `creator_id`)
VALUES (1, '系统文档', NULL, 1),
       (1, '技术文档', 1, 1),
       (1, '产品文档', 1, 1),
       (1, '用户手册', 1, 1);

-- 初始API密钥
INSERT INTO `tb_api_key` (`app_name`, `api_key`, `status`, `creator_id`)
VALUES ('系统集成', 'system_integration_key', 1, 1);

-- 初始知识库成员
INSERT INTO `tb_kb_member` (`kb_id`, `user_id`, `role`)
VALUES (1, 1, 30);