package com.etrx.kb.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 聊天响应DTO
 */
@Data
public class ChatResponseDTO {

    /**
     * AI回答
     */
    private String answer;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 引用的文档信息
     */
    private Map<String, Object> reference;

    /**
     * 相关文档块
     */
    private List<DocumentChunkDTO> chunks;

    /**
     * 是否完成
     */
    private Boolean finished = true;

    /**
     * 错误信息
     */
    private String error;

    /**
     * 文档块DTO
     */
    @Data
    public static class DocumentChunkDTO {
        /**
         * 文档块ID
         */
        private String chunkId;

        /**
         * 文档名称
         */
        private String documentName;

        /**
         * 文档块内容
         */
        private String content;

        /**
         * 相似度分数
         */
        private Double score;

        /**
         * 关键词
         */
        private List<String> keywords;
    }
} 