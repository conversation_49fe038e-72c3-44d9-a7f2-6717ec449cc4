import CopyToClipboard from '@/components/copy-to-clipboard';
import { IRemoveMessageById } from '@/hooks/logic-hooks';
import {
  SyncOutlined,
} from '@ant-design/icons';
import { Radio, Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';

interface IProps {
  messageId: string;
  content: string;
  prompt?: string;
  showLikeButton: boolean;
  audioBinary?: string;
  showLoudspeaker?: boolean;
}

export const AssistantGroupButton = ({
  messageId,
  content,
  audioBinary,
}: IProps) => {
  return (
    <>
      <Radio.Group size="small">
        <Radio.Button value="a" className="etrx-ai">
          <CopyToClipboard text={content}></CopyToClipboard>
        </Radio.Button>
      </Radio.Group>
    </>
  );
};

interface UserGroupButtonProps extends Partial<IRemoveMessageById> {
  messageId: string;
  content: string;
  regenerateMessage?: () => void;
  sendLoading: boolean;
}

export const UserGroupButton = ({
  content,
  sendLoading,
  regenerateMessage,
}: UserGroupButtonProps) => {

  const { t } = useTranslation();

  return (
    <Radio.Group size="small">
      <Radio.Button value="a" className="etrx-ai">
        <CopyToClipboard text={content}></CopyToClipboard>
      </Radio.Button>
      {regenerateMessage && (
        <Radio.Button
          value="b"
          onClick={regenerateMessage}
          disabled={sendLoading}
          className="etrx-ai"
        >
          <Tooltip title={t('chat.regenerate')}>
            <SyncOutlined spin={sendLoading} />
          </Tooltip>
        </Radio.Button>
      )}
    </Radio.Group>
  );
};
