package com.etrx.kb.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 数据字典实体（支持方法级别精确控制）
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("tb_dict")
public class Dict {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 字典类型
     */
    @TableField("dict_type")
    private String dictType;

    /**
     * 字典键（支持路径格式，如：third_party_method_api.user_controller.get_user）
     */
    @TableField("dict_key")
    private String dictKey;

    /**
     * 字典值（类路径+方法名，如：com.etrx.kb.controller.UserController.getCurrentUserInfo）
     */
    @TableField("dict_value")
    private String dictValue;

    /**
     * 字典标签
     */
    @TableField("dict_label")
    private String dictLabel;

    /**
     * 父节点ID
     */
    @TableField("parent_id")
    private Long parentId;

    /**
     * 节点类型（1:目录,2:配置项）
     */
    @TableField("node_type")
    private Integer nodeType;

    /**
     * 层级深度
     */
    @TableField("path_level")
    private Integer pathLevel;

    /**
     * 匹配类型（1:精确匹配,2:类通配符,3:包通配符）
     */
    @TableField("match_type")
    private Integer matchType;

    /**
     * HTTP方法（GET,POST,PUT,DELETE等,可选）
     */
    @TableField("http_method")
    private String httpMethod;

    /**
     * URL模式（可选,用于双重验证）
     */
    @TableField("url_pattern")
    private String urlPattern;

    /**
     * 描述
     */
    @TableField("description")
    private String description;

    /**
     * 排序
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 状态（0:禁用,1:启用）
     */
    @TableField("status")
    private Integer status;

    /**
     * 逻辑删除（0:未删除,1:已删除）
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 子节点列表（用于构建树形结构，不映射到数据库）
     */
    @TableField(exist = false)
    private List<Dict> children;

    /**
     * 节点类型枚举
     */
    public static class NodeType {
        public static final int DIRECTORY = 1; // 目录
        public static final int CONFIG_ITEM = 2; // 配置项
    }

    /**
     * 匹配类型枚举
     */
    public static class MatchType {
        public static final int EXACT = 1; // 精确匹配（完整类名+方法名）
        public static final int CLASS_WILDCARD = 2; // 类通配符（类名.方法名*）
        public static final int PACKAGE_WILDCARD = 3; // 包通配符（包名.*）
    }

    /**
     * 状态枚举
     */
    public static class Status {
        public static final int DISABLED = 0; // 禁用
        public static final int ENABLED = 1; // 启用
    }
} 