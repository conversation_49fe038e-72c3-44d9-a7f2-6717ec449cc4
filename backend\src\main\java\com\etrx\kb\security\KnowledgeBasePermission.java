package com.etrx.kb.security;

import com.etrx.kb.service.KnowledgeBaseService;
import com.etrx.kb.mapper.KnowledgeBaseMapper;
import com.etrx.kb.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import java.util.List;

/**
 * 知识库权限检查类
 */
@Component
@RequiredArgsConstructor
public class KnowledgeBasePermission {

    private final KnowledgeBaseService knowledgeBaseService;
    private final SecurityUtils securityUtils;
    private final KnowledgeBaseMapper knowledgeBaseMapper;

    /**
     * 检查用户对知识库的权限
     *
     * @param kbId 知识库ID
     * @param requiredRole 需要的角色级别
     * @return 是否有权限
     */
    public boolean check(Long kbId, Integer requiredRole) {
        // 获取当前用户ID并检查权限
        return knowledgeBaseService.checkPermission(kbId, securityUtils.getCurrentUserId(), requiredRole);
    }

    /**
     * 检查用户对多个知识库的权限
     *
     * @param kbIds 知识库ID列表
     * @param requiredRole 需要的角色级别
     * @return 是否都有权限
     */
    public boolean checkAll(List<Long> kbIds, Integer requiredRole) {
        if (kbIds == null || kbIds.isEmpty()) {
            return true;
        }
        Long userId = securityUtils.getCurrentUserId();
        return kbIds.stream().allMatch(kbId -> 
            knowledgeBaseService.checkPermission(kbId, userId, requiredRole)
        );
    }

    /**
     * 检查用户对数据集的权限
     *
     * @param datasetId 数据集ID
     * @param requiredRole 需要的角色级别
     * @return 是否有权限
     */
    public boolean checkDataset(String datasetId, Integer requiredRole) {
        Long kbId = knowledgeBaseMapper.getKbIdByDatasetId(datasetId);
        return kbId != null && check(kbId, requiredRole);
    }

    /**
     * 检查用户对多个数据集的权限
     *
     * @param datasetIds 数据集ID列表
     * @param requiredRole 需要的角色级别
     * @return 是否都有权限
     */
    public boolean checkDatasets(List<String> datasetIds, Integer requiredRole) {
        if (datasetIds == null || datasetIds.isEmpty()) {
            return true;
        }
        List<Long> kbIds = knowledgeBaseMapper.getKbIdsByDatasetIds(datasetIds);
        return kbIds != null && kbIds.size() == datasetIds.size() && checkAll(kbIds, requiredRole);
    }
}