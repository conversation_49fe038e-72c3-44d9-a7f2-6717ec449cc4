.messageInputWrapper {
  position: relative;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  padding: 0;

  &:hover {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
}

.contextTags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 8px 10px;
  border-bottom: 1px solid #f0f0f0;
}

.tag {
  display: inline-flex;
  align-items: center;
  max-width: 100px;
  background: #f5f5f5;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 2px 8px;
  font-size: 14px;
  line-height: 20px;
  transition: all 0.3s;

  &:hover {
    background: #e6f7ff;
    border-color: #1890ff;
  }
}

.tagContent {
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tagClose {
  flex-shrink: 0;
  margin-left: 4px;
  font-size: 12px;
  color: #999;
  background: none;
  border: none;
  padding: 0 2px;
  cursor: pointer;
  line-height: 1;
  transition: color 0.3s;

  &:hover {
    color: #666;
  }
}

.inputContainer {
  display: flex;
  align-items: center;
  padding: 8px;
  gap: 8px;

  //:global {
  //  .ant-input-textarea-affix-wrapper {
  //    flex: 1;
  //    background: transparent;
  //    border: none;
  //    padding: 0;
  //
  //    &:hover, &:focus {
  //      border: none;
  //      box-shadow: none;
  //    }
  //  }
  //
  //  .ant-input {
  //    background: transparent;
  //
  //    &:focus {
  //      box-shadow: none;
  //    }
  //  }
  //}
}

//.buttonGroup {
//  display: flex;
//  align-items: center;
//  gap: 4px;
//
//  :global {
//    .ant-btn {
//      padding: 4px 8px;
//      height: 32px;
//      color: #666;
//
//      &:hover {
//        color: #40a9ff;
//        background: rgba(24, 144, 255, 0.1);
//      }
//
//      &[disabled] {
//        color: rgba(0, 0, 0, 0.25);
//      }
//    }
//  }
//}

:global {
  .wgq {
    .ant-input-clear-icon {
      inset-block-start: 14px !important;
    }
  }
}

//.buttonContainer {
//  display: flex;
//  justify-content: flex-end;
//  gap: 8px;
//  padding: 8px 10px;
//  border-top: 1px solid #f0f0f0;
//}