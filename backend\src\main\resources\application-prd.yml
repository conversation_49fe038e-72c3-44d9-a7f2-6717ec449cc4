server:
  port: 8080
  servlet:
    context-path: /aikb

spring:
  application:
    name: etrx-ai-kb
  
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************************
    username: root
    password: 123456
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall
  
  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      password: 
      database: 0
      timeout: 10s
      lettuce:
        pool:
          min-idle: 0
          max-idle: 8
          max-active: 8
          max-wait: -1ms
  
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath:mapper/**/*.xml
  type-aliases-package: com.etrx.kb.domain
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# MinIO配置
minio:
  endpoint: http://localhost:9000
  accessKey: minioadmin
  secretKey: minioadmin
  bucketName: etrx-kb-docs

# JWT配置
jwt:
  secret: etrx-ai-kb-secret-key-for-jwt-token-generation-and-validation
  expiration: 86400000  # 24小时
  header: Authorization
  token-prefix: Bearer 

# 日志配置
logging:
  level:
    root: info
    org.springframework: INFO
    com.etrx: DEBUG

# Knife4j配置
knife4j:
  enable: true
  setting:
    language: zh-CN
    swagger-model-name: 实体类列表
    enable-swagger-models: true
    enable-document-manage: true
    enable-home-custom: false
    enable-footer-custom: false
    enable-footer: false