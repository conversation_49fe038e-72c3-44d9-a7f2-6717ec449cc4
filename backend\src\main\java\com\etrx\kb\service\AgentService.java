package com.etrx.kb.service;

import com.etrx.kb.dto.AgentDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Agent服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AgentService {

    private final Map<String, AgentDTO> agentMap;

    /**
     * 获取所有Agent列表
     */
    public List<AgentDTO> getAllAgents() {
        return agentMap.values().stream()
                .collect(Collectors.toList());
    }

    /**
     * 根据ID获取Agent
     */
    public AgentDTO getAgentById(String id) {
        return agentMap.get(id);
    }

    /**
     * 根据分组获取Agent列表
     */
    public List<AgentDTO> getAgentsByGroup(String group) {
        return agentMap.values().stream()
                .filter(agent -> agent.getGroup() != null && agent.getGroup().contains(group))
                .collect(Collectors.toList());
    }

    /**
     * 获取所有分组
     */
    public List<String> getAllGroups() {
        return agentMap.values().stream()
                .flatMap(agent -> agent.getGroup() != null ? agent.getGroup().stream() : Stream.empty())
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 根据关键词搜索Agent
     */
    public List<AgentDTO> searchAgents(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return getAllAgents();
        }
        
        String lowerKeyword = keyword.toLowerCase();
        return agentMap.values().stream()
                .filter(agent -> 
                    (agent.getName() != null && agent.getName().toLowerCase().contains(lowerKeyword)) ||
                    (agent.getDescription() != null && agent.getDescription().toLowerCase().contains(lowerKeyword)) ||
                    (agent.getGroup() != null && agent.getGroup().stream()
                            .anyMatch(group -> group.toLowerCase().contains(lowerKeyword)))
                )
                .collect(Collectors.toList());
    }
} 