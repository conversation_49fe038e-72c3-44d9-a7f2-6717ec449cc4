package com.etrx.kb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.etrx.kb.common.Constants;
import com.etrx.kb.domain.FileNode;
import com.etrx.kb.domain.KbFileRel;
import com.etrx.kb.domain.KnowledgeBase;
import com.etrx.kb.domain.User;
import com.etrx.kb.exception.ApiException;
import com.etrx.kb.mapper.FileNodeMapper;
import com.etrx.kb.mapper.KbFileRelMapper;
import com.etrx.kb.mapper.KnowledgeBaseMapper;
import com.etrx.kb.mapper.UserMapper;
import com.etrx.kb.service.DictService;
import com.etrx.kb.service.FileService;
import com.etrx.kb.service.TaskExecutionService;
import com.etrx.kb.service.model.*;
import com.etrx.kb.dto.BatchCreateFolderRequest;
import com.etrx.kb.util.RagflowUtils;
import com.etrx.kb.util.SecurityUtils;
import com.etrx.kb.vo.*;
import com.etrx.kb.vo.base.RagflowBaseVO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import erd.cloud.ai.ragflow.RagflowFileClient;
import erd.cloud.ai.ragflow.RagflowFileClientFactory;
import io.minio.*;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class FileServiceImpl extends ServiceImpl<FileNodeMapper, FileNode> implements FileService {

    @Value("${minio.bucketName}")
    private String bucketName;

    @Value("${ragflow.static-url}")
    private String ragflowStaticUrl;

    private static final int MAX_FILENAME_LENGTH = 200; // 文件名最大长度（包括扩展名）
    private static final int MAX_BASENAME_LENGTH = 150; // 文件名主体最大长度（不包括时间戳、UUID和扩展名）
    private static final String TIMESTAMP_FORMAT = "yyyyMMddHHmmss";
    private static final int UUID_LENGTH = 8;
    private static final String TRUNCATE_MARKER = "...";
    private static final String IS_ASYNC_PROCESS_KEY = "IS_ASYNC_PROCESS";

    private final KnowledgeBaseMapper knowledgeBaseMapper;
    private final KbFileRelMapper kbFileRelMapper;
    private final MinioClient minioClient;
    private final UserMapper userMapper;
    private final RagflowFileClient ragClient = RagflowFileClientFactory.getClient();
    private final SecurityUtils securityUtils;
    private final RestTemplate restTemplate;
    private final TaskExecutionService taskExecutionService;
    private final DictService dictService;
    private final ObjectMapper objectMapper;

    @Override
    @Transactional
    public FileNode uploadKbFile(Long kbId, Long folderId, MultipartFile file, Long userId) {
        // 1. 获取知识库信息
        KnowledgeBase kb = knowledgeBaseMapper.selectById(kbId);
        if (kb == null) {
            throw new ApiException("知识库不存在");
        }

        // 2. 获取目标文件夹
        FileNode targetFolder;
        if (folderId != null) {
            targetFolder = getById(folderId);
            if (targetFolder == null || targetFolder.getNodeType() != 1) {
                throw new ApiException("目标文件夹不存在或不是文件夹");
            }
            
            // 验证目标文件夹是否属于当前知识库
            String kbRootPath = "/.knowledgebase/" + kb.getName();
            String folderPath = targetFolder.getFullPath();
            if (!folderPath.startsWith(kbRootPath)) {
                throw new ApiException("目标文件夹不属于当前知识库");
            }
        } else {
            // 如果未指定文件夹，使用知识库根目录
            targetFolder = getOrCreateKbRootFolder(kbId, userId);
        }

        // 3. 上传文件
        FileNode fileNode = uploadFile(targetFolder.getId(), kb, file, userId);
        
        // 4. 建立知识库关联
        KbFileRel rel = new KbFileRel();
        rel.setKbId(kbId);
        rel.setFileNodeId(fileNode.getId());
        rel.setRelType(1); // 直接上传
        rel.setCreatorId(userId);

        if ("Y".equalsIgnoreCase(dictService.getDictByKey("IS_ASYNC_PROCESS_KEY","Y"))) {
            // 异步模式：先设置为等待上传状态
            rel.setDocumentStatus(Constants.DocumentStatus.PENDING_UPLOAD);
            kbFileRelMapper.insert(rel);
            processAsyncFileForRagflow(userId, kb, fileNode, rel);
        } else {
            // 同步模式：先插入记录，然后同步处理
            kbFileRelMapper.insert(rel);
            processSyncFileForRagflow(file, kb, rel);
        }

        return fileNode;
    }

    private void processAsyncFileForRagflow(Long userId, KnowledgeBase kb, FileNode fileNode, KbFileRel rel) {
        // 异步处理模式
        UploadToRagflowTaskData taskData = new UploadToRagflowTaskData();
        taskData.setKbId(kb.getId());
        taskData.setFileNodeId(fileNode.getId());
        taskData.setFileName(fileNode.getName());
        taskData.setDatasetId(kb.getDatasetId());
        taskData.setBucketName(bucketName);
        taskData.setStoragePath(fileNode.getStoragePath());
        taskData.setRelId(rel.getId());
        taskData.setUserId(userId);

        // 将任务数据转换为JSON
        String businessData = null;
        try {
            businessData = objectMapper.writeValueAsString(taskData);
        } catch (JsonProcessingException e) {
            throw new ApiException("将任务数据转换为JSON失败: " + e.getMessage());
        }

        log.info("准备异步上传文件到Ragflow，知识库ID: {}，文件名: {}，存储路径: {}",
                kb.getId(), fileNode.getName(), fileNode.getStoragePath());

        try {
            // 尝试直接执行任务
            taskExecutionService.executeTaskByName("uploadToRagflow", businessData, userId);
        } catch (Exception e) {
            log.warn("直接执行上传到Ragflow失败，将任务提交到队列重试", e);
            // 如果直接执行失败，提交到任务队列
            taskExecutionService.submitTaskByName("uploadToRagflow", businessData, userId);
        }
    }

    private void processSyncFileForRagflow(MultipartFile file, KnowledgeBase kb, KbFileRel rel) {
        try {
            // 创建临时目录
            String tempDirPrefix = "upload_" + UUID.randomUUID().toString().replace("-", "");
            File tempDir = new File(System.getProperty("java.io.tmpdir"), tempDirPrefix);
            if (!tempDir.mkdir()) {
                throw new ApiException("创建临时目录失败");
            }

            // 在临时目录中创建原始文件名的文件
            File tempFile = new File(tempDir, file.getOriginalFilename());
            file.transferTo(tempFile);

            long ragflowStartTime = System.nanoTime();
            log.info("开始上传文件到Ragflow，知识库ID: {}，文件名: {}，文件大小: {}字节",
                kb.getId(), file.getOriginalFilename(), file.getSize());

            String res = ragClient.uploadDocuments(kb.getDatasetId(), List.of(tempFile));

            long ragflowEndTime = System.nanoTime();
            long ragflowDuration = (ragflowEndTime - ragflowStartTime) / 1_000_000; // 转换为毫秒
            log.info("文件上传到Ragflow完成，知识库ID: {}，文件名: {}，文件大小: {}字节，耗时: {}ms",
                kb.getId(), file.getOriginalFilename(), file.getSize(), ragflowDuration);

            // 解析返回结果
            ObjectMapper mapper = new ObjectMapper();
            RagflowUploadVO uploadResult = mapper.readValue(res, RagflowUploadVO.class);

            if (uploadResult.getCode() == 0 && uploadResult.getData() != null && !uploadResult.getData().isEmpty()) {
                RagflowUploadVO.UploadFileVO fileInfo = uploadResult.getData().get(0);
                // 更新关联表中的文档信息
                rel.setDocumentId(fileInfo.getId());
                rel.setDocumentStatus(Constants.DocumentStatus.READY);
                // 保存整个data数组信息
                String dataJson = mapper.writeValueAsString(uploadResult.getData());
                rel.setDocumentInfo(dataJson);
                kbFileRelMapper.updateById(rel);

                // 删除临时文件和目录
                tempFile.delete();
                tempDir.delete();
            } else {
                // 删除临时文件和目录
                tempFile.delete();
                tempDir.delete();
                throw new ApiException("上传文件到Ragflow失败: " + uploadResult.getMessage());
            }
        } catch (Exception e) {
            log.error("上传文件到Ragflow失败", e);
            throw new ApiException("上传文件到Ragflow失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public void deleteKbFile(Long kbId, Long fileId, Long userId) {
        // 1. 查询关联关系
        LambdaQueryWrapper<KbFileRel> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(KbFileRel::getKbId, kbId)
               .eq(KbFileRel::getFileNodeId, fileId);
        KbFileRel rel = kbFileRelMapper.selectOne(wrapper);
        
        if (rel == null) {
            throw new ApiException("文件未关联到该知识库");
        }

        // 2. 获取文件和知识库信息
        FileNode file = getById(fileId);
        if (file == null) {
            throw new ApiException("文件不存在");
        }
        
        KnowledgeBase kb = knowledgeBaseMapper.selectById(kbId);
        if (kb == null) {
            throw new ApiException("知识库不存在");
        }

        // 3. 先处理本地数据
        if (rel.getRelType() == 1) { // 直接上传
            try {
                // 先从MinIO删除文件
                if (StringUtils.hasText(file.getStoragePath())) {
                    minioClient.removeObject(
                        RemoveObjectArgs.builder()
                            .bucket(bucketName)
                            .object(file.getStoragePath())
                            .build()
                    );
                } else {
                    log.warn("文件存储路径为空，跳过MinIO删除，文件ID: {}", file.getId());
                }
                
                // 再删除文件节点和关联关系
                removeById(fileId);
                kbFileRelMapper.deleteById(rel.getId());
            } catch (Exception e) {
                log.error("删除本地文件失败", e);
                throw new ApiException("删除本地文件失败: " + e.getMessage());
            }
        } else { // 手动关联
            kbFileRelMapper.deleteById(rel.getId());
        }

        // 4. 异步删除Ragflow文档
        if (rel.getDocumentId() != null) {
            processAsyncDeleteFromRagflow(file, kb, rel, userId);
        }
    }

    @Override
    @Transactional
    public void bindKbFile(List<Long> kbIds, Long fileId, Long userId) {
        // 检查文件是否存在
        FileNode file = getById(fileId);
        if (file == null) {
            throw new ApiException("文件不存在");
        }

        // 获取所有知识库信息
        List<KnowledgeBase> kbs = knowledgeBaseMapper.selectBatchIds(kbIds);
        if (kbs.isEmpty()) {
            throw new ApiException("未找到指定的知识库");
        }

        // 检查是否已关联（包括已删除的记录）
        List<KbFileRel> allRels = kbFileRelMapper.selectByFileNodeIdAndKbIds(fileId, kbIds);
        
        // 分类处理：已存在的关联关系和已删除的关联关系
        List<Long> activeKbIds = new ArrayList<>();
        List<KbFileRel> deletedRels = new ArrayList<>();
        
        for (KbFileRel rel : allRels) {
            if (rel.getDeleted() == 0) {
                // 未删除的记录
                activeKbIds.add(rel.getKbId());
            } else {
                // 已删除的记录
                deletedRels.add(rel);
            }
        }
        
        // 找出需要新建关联的知识库ID
        List<Long> newKbIds = kbIds.stream()
                .filter(id -> !activeKbIds.contains(id))
                .toList();

        // 硬删除已删除的无效记录
        for (KbFileRel deletedRel : deletedRels) {
            if (newKbIds.contains(deletedRel.getKbId())) {
                kbFileRelMapper.hardDeleteById(deletedRel.getId());
            }
        }

        // 建立新的关联关系
        for (Long kbId : newKbIds) {
            KbFileRel rel = new KbFileRel();
            rel.setKbId(kbId);
            rel.setFileNodeId(fileId);
            rel.setRelType(2); // 手动关联
            rel.setCreatorId(userId);
            rel.setDocumentStatus(Constants.DocumentStatus.PENDING_UPLOAD); // 设置为等待上传状态
            kbFileRelMapper.insert(rel);

            // 异步上传ragflow文档信息
            KnowledgeBase kb = kbs.stream()
                    .filter(k -> k.getId().equals(kbId))
                    .findFirst()
                    .orElseThrow(() -> new ApiException("知识库不存在"));
            processAsyncUploadToRagflow(file, kb, rel, userId);
        }
    }

    @Override
    @Transactional
    public void unbindKbFile(List<Long> kbIds, Long fileId, Long userId) {
        // 获取文件信息
        FileNode file = getById(fileId);
        if (file == null) {
            throw new ApiException("文件不存在");
        }

        // 构建查询条件
        LambdaQueryWrapper<KbFileRel> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(KbFileRel::getFileNodeId, fileId);
        if (kbIds != null && !kbIds.isEmpty()) {
            wrapper.in(KbFileRel::getKbId, kbIds);
        }

        // 获取所有需要解绑的关联关系
        List<KbFileRel> rels = kbFileRelMapper.selectList(wrapper);
        if (rels.isEmpty()) {
            throw new ApiException("文件未关联到指定的知识库");
        }

        // 获取所有相关的知识库信息
        List<Long> relatedKbIds = rels.stream()
                .map(KbFileRel::getKbId)
                .toList();
        List<KnowledgeBase> kbs = knowledgeBaseMapper.selectBatchIds(relatedKbIds);

        // 硬删除关联关系并异步删除ragflow文档信息
        for (KbFileRel rel : rels) {
            kbFileRelMapper.hardDeleteById(rel.getId());

            // 异步删除ragflow文档信息
            KnowledgeBase kb = kbs.stream()
                    .filter(k -> k.getId().equals(rel.getKbId()))
                    .findFirst()
                    .orElseThrow(() -> new ApiException("知识库不存在"));
            processAsyncDeleteFromRagflow(file, kb, rel, userId);
        }
    }



    /**
     * 检查文档是否存在于Ragflow中
     * @param datasetId 数据集ID
     * @param documentId 文档ID
     * @return true如果文档存在，false如果不存在
     */
    private boolean checkDocumentExistsInRagflow(String datasetId, String documentId) {
        try {
            // 通过查询文档列表，查找特定ID的文档
            Map<String, String> params = new HashMap<>();
            params.put("id", documentId);
            
            String result = ragClient.listDocuments(datasetId, params);
            
            ObjectMapper mapper = new ObjectMapper();
            RagflowDocsListVO response = mapper.readValue(result, RagflowDocsListVO.class);
            
            if (response.getCode() == 0 && response.getData() != null) {
                List<RagflowDocsListVO.UploadFileVO> docs = response.getData().getDocs();
                return docs != null && docs.stream().anyMatch(doc -> documentId.equals(doc.getId()));
            }
            
            return false;
        } catch (Exception e) {
            log.warn("检查文档是否存在于Ragflow失败, datasetId: {}, documentId: {}", datasetId, documentId, e);
            // 发生异常时为了安全起见，假设文档存在，尝试删除
            return true;
        }
    }

    /**
     * 更新文件的文档信息
     * @param rel 关联关系
     * @param fileInfo Ragflow文件信息
     * @param fullData 完整的上传返回数据
     */
    private void updateFileDocumentInfo(KbFileRel rel, RagflowUploadVO.UploadFileVO fileInfo, List<RagflowUploadVO.UploadFileVO> fullData) {
        try {
            rel.setDocumentId(fileInfo.getId());
            rel.setDocumentStatus(Constants.DocumentStatus.READY);
            rel.setDocumentInfo(new ObjectMapper().writeValueAsString(fullData));
            kbFileRelMapper.updateById(rel);
        } catch (Exception e) {
            log.error("更新文件文档信息失败, fileId: {}", rel.getId(), e);
        }
    }

    /**
     * 清理临时文件
     * @param tempFile 临时文件
     * @param tempDir 临时目录
     */
    private void cleanupTempFiles(File tempFile, File tempDir) {
        try {
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
            if (tempDir != null && tempDir.exists()) {
                tempDir.delete();
            }
        } catch (Exception e) {
            log.error("清理临时文件失败", e);
        }
    }

    /**
     * 异步上传文件到Ragflow（使用线程池）
     * @param file 文件节点
     * @param kb 知识库信息
     * @param rel 关联关系
     * @param userId 用户ID
     */
    private void processAsyncUploadToRagflow(FileNode file, KnowledgeBase kb, KbFileRel rel, Long userId) {
        try {
            // 构建任务数据
            UploadToRagflowTaskData taskData = new UploadToRagflowTaskData();
            taskData.setKbId(kb.getId());
            taskData.setFileNodeId(file.getId());
            taskData.setFileName(file.getName());
            taskData.setDatasetId(kb.getDatasetId());
            taskData.setBucketName(bucketName);
            taskData.setStoragePath(file.getStoragePath());
            taskData.setRelId(rel.getId());
            taskData.setUserId(userId);

            String businessData = objectMapper.writeValueAsString(taskData);

            log.info("准备异步上传文件到Ragflow，文件ID: {}，知识库ID: {}，文件名: {}",
                    file.getId(), kb.getId(), file.getName());

            try {
                // 尝试直接执行任务
                taskExecutionService.executeTaskByName("uploadToRagflow", businessData, userId);
            } catch (Exception e) {
                log.warn("直接执行上传到Ragflow失败，将任务提交到队列重试", e);
                // 如果直接执行失败，提交到任务队列
                taskExecutionService.submitTaskByName("uploadToRagflow", businessData, userId);
            }
        } catch (Exception e) {
            log.error("处理异步上传到Ragflow失败，文件ID: {}，知识库ID: {}", file.getId(), kb.getId(), e);
        }
    }

    /**
     * 异步从Ragflow删除文档（使用线程池）
     * @param file 文件节点
     * @param kb 知识库信息
     * @param rel 关联关系
     * @param userId 用户ID
     */
    private void processAsyncDeleteFromRagflow(FileNode file, KnowledgeBase kb, KbFileRel rel, Long userId) {
        try {
            // 构建任务数据
            DeleteFromRagflowTaskData taskData = new DeleteFromRagflowTaskData();
            taskData.setKbId(kb.getId());
            taskData.setFileNodeId(file.getId());
            taskData.setFileName(file.getName());
            taskData.setDatasetId(kb.getDatasetId());
            taskData.setDocumentId(rel.getDocumentId());
            taskData.setRelId(rel.getId());
            taskData.setUserId(userId);

            String businessData = objectMapper.writeValueAsString(taskData);

            log.info("准备异步从Ragflow删除文档，文件ID: {}，知识库ID: {}，文档ID: {}",
                    file.getId(), kb.getId(), rel.getDocumentId());

            try {
                // 尝试直接执行任务
                taskExecutionService.executeTaskByName("deleteFromRagflow", businessData, userId);
            } catch (Exception e) {
                log.warn("直接执行从Ragflow删除失败，将任务提交到队列重试", e);
                // 如果直接执行失败，提交到任务队列
                taskExecutionService.submitTaskByName("deleteFromRagflow", businessData, userId);
            }
        } catch (Exception e) {
            log.error("处理异步从Ragflow删除失败，文件ID: {}，知识库ID: {}", file.getId(), kb.getId(), e);
        }
    }

    @Override
    public FileNode createFolder(String name, Long parentId, Long userId) {
        return createFolder(name, parentId, userId, false);
    }

    /**
     * 创建文件夹（支持系统创建标识）
     * @param name 文件夹名称
     * @param parentId 父文件夹ID
     * @param userId 用户ID
     * @param isSystemCreate 是否为系统创建
     * @return 创建的文件夹节点
     */
    public FileNode createFolder(String name, Long parentId, Long userId, boolean isSystemCreate) {
        // 1. 验证文件夹名称
        if (!StringUtils.hasText(name)) {
            throw new ApiException("文件夹名称不能为空");
        }
        
        if (!isValidFileName(name, isSystemCreate)) {
            throw new ApiException("文件夹名称包含非法字符，不能包含 < > : \" | ? * \\ / 等特殊字符，且不能以点开头或结尾");
        }

        // 2. 验证父文件夹
        if (parentId != null && parentId != -1L) {
            FileNode parentFolder = getById(parentId);
            if (parentFolder == null || parentFolder.getNodeType() != 1) {
                throw new ApiException("父文件夹不存在或不是文件夹");
            }
        }

        // 3. 检查是否存在同名文件夹
        checkDuplicateName(parentId, name, null);

        // 4. 创建文件夹节点
        FileNode folder = new FileNode();
        folder.setName(name);
        folder.setNodeType(1); // 1表示文件夹
        folder.setParentId(parentId != null && parentId == -1L ? null : parentId);
        
        // 构建完整路径
        String fullPath;
        if (parentId != null && parentId == -1L) {
            // 顶层目录路径特殊处理
            fullPath = "/" + name;
        } else {
            fullPath = buildFullPath(parentId, name);
        }
        folder.setFullPath(fullPath);
        
        folder.setCreatorId(userId);
        folder.setCreateTime(LocalDateTime.now());
        folder.setUpdateTime(LocalDateTime.now());
        
        save(folder);
        return folder;
    }

    @Override
    @Transactional
    public Map<String, Long> createFoldersInBatch(List<BatchCreateFolderRequest.FolderInfo> folders, Long parentId, Long userId) {
        if (folders == null || folders.isEmpty()) {
            return new HashMap<>();
        }

        Map<String, Long> folderMap = new HashMap<>();

        // 按路径深度排序，确保父文件夹先创建
        List<BatchCreateFolderRequest.FolderInfo> sortedFolders = folders.stream()
                .sorted((a, b) -> {
                    int depthA = a.getPath().split("/").length;
                    int depthB = b.getPath().split("/").length;
                    if (depthA != depthB) {
                        return Integer.compare(depthA, depthB);
                    }
                    // 如果深度相同，按order排序
                    if (a.getOrder() != null && b.getOrder() != null) {
                        return Integer.compare(a.getOrder(), b.getOrder());
                    }
                    return a.getPath().compareTo(b.getPath());
                })
                .collect(Collectors.toList());

        for (BatchCreateFolderRequest.FolderInfo folderInfo : sortedFolders) {
            try {
                // 确定父文件夹ID
                String[] pathParts = folderInfo.getPath().split("/");
                Long currentParentId = parentId;

                if (pathParts.length > 1) {
                    // 如果不是根级文件夹，找到父文件夹ID
                    String parentPath = String.join("/", Arrays.copyOf(pathParts, pathParts.length - 1));
                    currentParentId = folderMap.getOrDefault(parentPath, parentId);
                }

                // 检查文件夹是否已存在
                FileNode existingFolder = findFolderByNameAndParent(folderInfo.getName(), currentParentId);
                if (existingFolder != null) {
                    // 文件夹已存在，直接使用
                    folderMap.put(folderInfo.getPath(), existingFolder.getId());
                    log.info("文件夹已存在，跳过创建: {} -> ID: {}", folderInfo.getPath(), existingFolder.getId());
                    continue;
                }

                // 创建文件夹
                FileNode newFolder = createFolder(folderInfo.getName(), currentParentId, userId, false);
                folderMap.put(folderInfo.getPath(), newFolder.getId());

                log.info("批量创建文件夹成功: {} -> ID: {}", folderInfo.getPath(), newFolder.getId());

            } catch (Exception e) {
                log.error("批量创建文件夹失败: {}", folderInfo.getPath(), e);
                throw new ApiException("创建文件夹 \"" + folderInfo.getName() + "\" 失败: " + e.getMessage());
            }
        }

        return folderMap;
    }

    /**
     * 根据名称和父文件夹ID查找文件夹
     */
    private FileNode findFolderByNameAndParent(String name, Long parentId) {
        LambdaQueryWrapper<FileNode> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FileNode::getName, name)
               .eq(FileNode::getNodeType, 1) // 文件夹类型
               .eq(FileNode::getDeleted, 0);

        if (parentId != null) {
            wrapper.eq(FileNode::getParentId, parentId);
        } else {
            wrapper.isNull(FileNode::getParentId);
        }

        return getOne(wrapper);
    }

    @Override
    @Transactional
    public void deleteFolder(Long folderId, Long userId) {
        // 检查文件夹是否存在
        FileNode folder = getById(folderId);
        if (folder == null || folder.getNodeType() != 1) {
            throw new ApiException("文件目录不存在");
        }

        // 检查是否为知识库根目录
        if (folder.getName().equals(".knowledgebase") && folder.getParentId() == null) {
            throw new ApiException("知识库根目录不允许删除");
        }

        // 检查目录下是否存在文件
        if (hasFiles(folderId)) {
            throw new ApiException("目录下存在文件，无法删除");
        }

        // 递归删除所有空目录
        deleteEmptyFolders(folderId);
    }

    /**
     * 递归检查目录下是否存在文件
     * @param folderId 文件夹ID
     * @return true如果目录或其子目录下存在文件，false如果全是空目录
     */
    private boolean hasFiles(Long folderId) {
        // 查询当前目录下的所有节点
        LambdaQueryWrapper<FileNode> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FileNode::getParentId, folderId)
              .eq(FileNode::getDeleted, 0);
        List<FileNode> nodes = list(wrapper);

        for (FileNode node : nodes) {
            // 如果是文件，直接返回true
            if (node.getNodeType() == 2) {
                return true;
            }
            // 如果是目录，递归检查
            if (node.getNodeType() == 1 && hasFiles(node.getId())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 递归删除空目录
     * @param folderId 文件夹ID
     */
    private void deleteEmptyFolders(Long folderId) {
        // 查询当前目录下的所有子目录
        LambdaQueryWrapper<FileNode> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FileNode::getParentId, folderId)
              .eq(FileNode::getNodeType, 1)
              .eq(FileNode::getDeleted, 0);
        List<FileNode> subFolders = list(wrapper);

        // 递归删除子目录
        for (FileNode subFolder : subFolders) {
            deleteEmptyFolders(subFolder.getId());
        }

        // 删除当前目录
        removeById(folderId);
    }

    @Override
    public FileListVO listFolder(Long folderId, Long kbId, Integer pageNum, Integer pageSize, String keyword) {
        // 1. 检查文件夹是否存在（如果不是根目录）
        if (folderId != null && folderId != -1) {
            FileNode folder = getById(folderId);
            if (folder == null || folder.getNodeType() != 1) {
                throw new ApiException("文件夹不存在");
            }
        }

        // 2. 如果是知识库查询且未指定目录，则获取或创建知识库根目录
        if (kbId != null && (folderId == null || folderId == -1)) {
            FileNode rootFolder = getOrCreateKbRootFolder(kbId, securityUtils.getCurrentUserId());
            folderId = rootFolder.getId();
        }

        // 3. 如果未指定目录，则使用-1表示根目录
        if (folderId == null) {
            folderId = -1L;
        }

        // 4. 判断目录类型
        boolean isKbRoot = false;
        boolean isNonKbDirectory = folderId == -1;
        
        if (folderId != null && folderId != -1) {
            FileNode currentFolder = getById(folderId);
            if (currentFolder != null && currentFolder.getNodeType() == 1) {
                // 判断是否为知识库根目录
                FileNode parentFolder = null;
                if (currentFolder.getParentId() != null) {
                    parentFolder = getById(currentFolder.getParentId());
                }
                
                // 检查父文件夹是否为根级的.knowledgebase目录
                if (parentFolder != null && 
                    parentFolder.getNodeType() == 1 && 
                    ".knowledgebase".equals(parentFolder.getName()) &&
                    parentFolder.getParentId() == null) {
                    isKbRoot = true;
                } else {
                    // 检查是否为非知识库目录（不在.knowledgebase路径下）
                    String fullPath = currentFolder.getFullPath();
                    if (!fullPath.startsWith("/.knowledgebase")) {
                        isNonKbDirectory = true;
                    }
                }
            }
        }

        // 5. 统计总数
        Long total = baseMapper.countFiles(folderId, kbId, keyword, isKbRoot, isNonKbDirectory);
        
        // 6. 查询分页数据
        List<FileNode> records = baseMapper.selectFilesWithPaging(
            folderId, 
            kbId, 
            (pageNum - 1) * pageSize, 
            pageSize, 
            keyword,
            isKbRoot,
            isNonKbDirectory
        );
        
        // 7. 如果指定了知识库ID，异步更新文档的状态
        if (kbId != null) {
            syncRagflowStatus(kbId, records);
        }

        // 8. 为每个文件节点设置知识库标识
        setKnowledgeBaseFlag(records);

        // 9. 构建并返回FileListVO
        return FileListVO.build(
            total,
            pageNum.longValue(),
            pageSize.longValue(),
            records,
            isNonKbDirectory
        );
    }

    /**
     * 为文件节点设置知识库标识
     * @param records 文件节点列表
     */
    private void setKnowledgeBaseFlag(List<FileNode> records) {
        if (records == null || records.isEmpty()) {
            return;
        }
        
        // 为每个文件节点设置标识
        for (FileNode record : records) {
            boolean isCurrentKb = false;
            
            if (record.getFullPath() != null) {
                // 检查文件路径是否以知识库目录开头
                // 知识库文件的路径格式为：/.knowledgebase/{知识库名称}/...
                isCurrentKb = record.getFullPath().startsWith("/.knowledgebase/");
            }
            
            record.setIsCurrentKnowledgeBase(isCurrentKb);
            // 判定完之后，将文件路径屏蔽
            record.setFullPath(null);
            record.setStoragePath(null);
        }
    }

    private void syncRagflowStatus(Long kbId, List<FileNode> records) {
        if (kbId != null && !records.isEmpty()) {
            // 获取知识库信息
            KnowledgeBase kb = knowledgeBaseMapper.selectById(kbId);
            if (kb != null) {
                // 获取所有关联关系
                List<KbFileRel> rels = new ArrayList<>();
                for (FileNode node : records) {
                    LambdaQueryWrapper<KbFileRel> wrapper = new LambdaQueryWrapper<>();
                    wrapper.eq(KbFileRel::getKbId, kbId)
                          .eq(KbFileRel::getFileNodeId, node.getId());
                    KbFileRel rel = kbFileRelMapper.selectOne(wrapper);
                    if (rel != null && Integer.valueOf(Constants.DocumentStatus.PROCESSING).equals(rel.getDocumentStatus())) {
                        rels.add(rel);
                    }
                }

                if (!rels.isEmpty()) {
                    // 异步执行状态同步
                    CompletableFuture.runAsync(() -> {
                        try {
                            processDocsStatus(kbId, records, kb);
                        } catch (Exception e) {
                            log.error("异步同步文档状态失败，知识库ID: {}", kbId, e);
                        }
                    });
                }
            }
        }
    }

    @Override
    public void processDocsStatus(Long kbId, List<FileNode> processingDocs, KnowledgeBase kb) {
        log.info("开始异步同步文档状态，知识库ID: {}，处理中文档数: {}", kbId, processingDocs.size());
        
        // 获取所有处理中的关联关系
        List<KbFileRel> processingRels = new ArrayList<>();
        for (FileNode doc : processingDocs) {
            LambdaQueryWrapper<KbFileRel> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(KbFileRel::getKbId, kbId)
                   .eq(KbFileRel::getFileNodeId, doc.getId())
                   .eq(KbFileRel::getDocumentStatus, Constants.DocumentStatus.PROCESSING);
            KbFileRel rel = kbFileRelMapper.selectOne(wrapper);
            if (rel != null) {
                processingRels.add(rel);
            }
        }
        
        // 单独查询每个处理中文档的状态
        for (KbFileRel rel : processingRels) {
            try {
                Map<String, String> params = new HashMap<>();
                params.put("id", rel.getDocumentId());
                String result = ragClient.listDocuments(kb.getDatasetId(), params);
                RagflowUtils.handleResponse(result, "查询文档状态失败: " + result);
                updateDocumentStatus(List.of(rel), result);
            } catch (Exception e) {
                log.error("异步查询处理中文档状态失败，documentId: {}", rel.getDocumentId(), e);
            }
        }
        log.info("异步同步文档状态完成，知识库ID: {}", kbId);
    }

    /**
     * 更新文档状态
     * @param rels 关联关系列表
     * @param ragflowResult Ragflow返回的结果
     */
    private void updateDocumentStatus(List<KbFileRel> rels, String ragflowResult) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            RagflowDocsListVO response = mapper.readValue(ragflowResult, RagflowDocsListVO.class);
            
            if (response.getCode() == 0 && response.getData() != null) {
                Map<String, Integer> statusMap = new HashMap<>();
                
                // 构建文档ID到状态的映射
                for (RagflowDocsListVO.UploadFileVO doc : response.getData().getDocs()) {
                    if (doc.getId() != null && doc.getRun() != null) {
                        // 将run状态字符串转换为整数
                        Integer status = convertRunStatusToInt(doc.getRun());
                        if (status != null) {
                            statusMap.put(doc.getId(), status);
                        }
                    }
                }
                
                // 收集需要更新状态的关联关系
                List<KbFileRel> needUpdateRels = new ArrayList<>();
                
                // 比对文档状态，只收集状态不一致的关联关系
                for (KbFileRel rel : rels) {
                    if (rel.getDocumentId() != null && statusMap.containsKey(rel.getDocumentId())) {
                        Integer ragflowStatus = statusMap.get(rel.getDocumentId());
                        Integer currentStatus = rel.getDocumentStatus();
                        
                        // 只有当状态不一致时才更新
                        if (!Objects.equals(currentStatus, ragflowStatus)) {
                            log.info("文档状态不一致，documentId: {}, 本地状态: {}, Ragflow状态: {}", 
                                rel.getDocumentId(), currentStatus, ragflowStatus);
                            rel.setDocumentStatus(ragflowStatus);
                            needUpdateRels.add(rel);
                        }
                    }
                }
                
                // 批量更新状态不一致的关联关系
                if (!needUpdateRels.isEmpty()) {
                    log.info("开始批量更新文档状态，需要更新的文档数: {}", needUpdateRels.size());
                    for (KbFileRel rel : needUpdateRels) {
                        kbFileRelMapper.updateById(rel);
                    }
                }
            }
        } catch (Exception e) {
            log.error("解析Ragflow返回结果失败", e);
        }
    }

    /**
     * 将Ragflow run状态字符串转换为整数状态码
     * @param runStatus Ragflow run状态字符串
     * @return 整数状态码
     */
    private Integer convertRunStatusToInt(String runStatus) {
        if (runStatus == null) {
            return null;
        }
        
        return switch (runStatus.toUpperCase()) {
            case "UNSTART" -> Constants.DocumentStatus.READY;
            case "DONE" -> Constants.DocumentStatus.COMPLETED;
            case "RUNNING" -> Constants.DocumentStatus.PROCESSING;
            case "FAIL" -> Constants.DocumentStatus.FAILED;
            case "CANCEL" -> Constants.DocumentStatus.CANCELED;
            default -> null;
        };
    }

    /**
     * 批量填充创建人名称
     *
     * @param nodes 需要填充创建人名称的节点列表
     */
    private void fillCreatorNames(List<FileNode> nodes) {
        if (nodes == null || nodes.isEmpty()) {
            return;
        }
        
        // 收集所有创建人ID
        List<Long> creatorIds = nodes.stream()
                .map(FileNode::getCreatorId)
                .filter(id -> id != null)
                .distinct()
                .toList();
                
        if (creatorIds.isEmpty()) {
            return;
        }

        // 批量查询用户信息
        LambdaQueryWrapper<User> userWrapper = new LambdaQueryWrapper<>();
        userWrapper.in(User::getId, creatorIds);
        List<User> creators = userMapper.selectList(userWrapper);
        
        // 构建用户ID到昵称的映射
        Map<Long, String> creatorMap = creators.stream()
                .collect(Collectors.toMap(
                    User::getId,
                    User::getNickname,
                    (existing, replacement) -> existing
                ));
        
        // 填充创建人名称
        for (FileNode node : nodes) {
            if (node.getCreatorId() != null) {
                node.setCreatorName(creatorMap.get(node.getCreatorId()));
            }
        }
    }

    @Override
    public List<FileNode> searchFiles(String keyword) {
        LambdaQueryWrapper<FileNode> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(FileNode::getName, keyword)
               .or()
               .like(FileNode::getFullPath, keyword);
        return list(wrapper);
    }

    @Override
    @Transactional
    public FileNode uploadFile(Long parentId, KnowledgeBase kb, MultipartFile file, Long userId) {
        // 1. 校验顶层目录规则
        if (parentId == null || parentId == -1L) {
            throw new ApiException("不能将文件上传到顶层目录");
        }

        // 2. 验证父文件夹
        FileNode parentFolder = getById(parentId);
        if (parentFolder == null || parentFolder.getNodeType() != 1) {
            throw new ApiException("目标文件夹不存在或不是文件夹");
        }

        if (file == null || file.isEmpty()) {
            throw new ApiException("上传文件不能为空");
        }

        // 获取原始文件名和扩展名
        String originalFilename = file.getOriginalFilename();
        String extension = StringUtils.getFilenameExtension(originalFilename);
        String nameWithoutExt = StringUtils.stripFilenameExtension(originalFilename);

        // 生成唯一文件名
        String finalFilename = generateUniqueFilename(nameWithoutExt, extension, parentId);

        // 1. 生成存储路径
        String storagePath = generateStoragePath(finalFilename, userId, kb);
        log.info("生成文件存储路径: {}", storagePath);
        
        // 2. 保存文件到MinIO
        try {
            long minioStartTime = System.nanoTime();
            log.info("开始上传文件到MinIO，文件大小: {}字节，存储路径: {}", file.getSize(), storagePath);
            
            // 构建PutObjectArgs
            PutObjectArgs putObjectArgs = PutObjectArgs.builder()
                .bucket(bucketName)
                .object(storagePath)
                .stream(file.getInputStream(), file.getSize(), -1)
                .contentType(file.getContentType())
                .build();
            
            // 上传文件
            minioClient.putObject(putObjectArgs);
            
            long minioEndTime = System.nanoTime();
            long minioDuration = (minioEndTime - minioStartTime) / 1_000_000; // 转换为毫秒
            log.info("文件上传到MinIO成功，文件大小: {}字节，耗时: {}ms，存储路径: {}", 
                file.getSize(), minioDuration, storagePath);
        } catch (Exception e) {
            log.error("文件上传到MinIO失败: {}", e.getMessage(), e);
            throw new ApiException("文件上传失败: " + e.getMessage());
        }
    
        // 3. 创建文件节点
        try {
            FileNode fileNode = new FileNode();
            fileNode.setName(finalFilename);
            fileNode.setNodeType(2);
            fileNode.setParentId(parentId);
            fileNode.setFullPath(buildFullPath(parentId, finalFilename));
            fileNode.setFileType(extension);
            fileNode.setFileSize(file.getSize());
            fileNode.setStoragePath(storagePath);
            fileNode.setStatus(1);
            fileNode.setCreatorId(userId);
            save(fileNode);
            log.info("文件节点创建成功: {}", fileNode.getId());
            
            return fileNode;
        } catch (Exception e) {
            log.error("创建文件节点失败: {}", e.getMessage(), e);
            // 如果创建文件节点失败，尝试删除已上传的文件
            try {
                if (StringUtils.hasText(storagePath)) {
                    minioClient.removeObject(
                        RemoveObjectArgs.builder()
                            .bucket(bucketName)
                            .object(storagePath)
                            .build()
                    );
                } else {
                    log.warn("存储路径为空，跳过MinIO清理");
                }
            } catch (Exception ex) {
                log.error("清理MinIO文件失败: {}", ex.getMessage(), ex);
            }
            throw new ApiException("创建文件节点失败: " + e.getMessage());
        }
    }

    /**
     * 生成唯一的文件名
     * 格式: 原文件名_yyyyMMddHHmmss_uuid简写.扩展名
     */
    private String generateUniqueFilename(String nameWithoutExt, String extension, Long parentId) {
        int maxAttempts = 5;  // 最大重试次数
        int attempt = 0;

        // 计算时间戳和UUID部分的固定长度
        String timestamp = LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern(TIMESTAMP_FORMAT));
        int suffixLength = 1 + timestamp.length() + 1 + UUID_LENGTH; // _timestamp_uuid
        if (extension != null) {
            suffixLength += 1 + extension.length(); // .extension
        }

        // 处理文件名主体
        String processedName = processBaseName(nameWithoutExt, suffixLength);
        
        while (attempt < maxAttempts) {
            try {
                // 生成8位UUID（取前8位）
                String shortUuid = UUID.randomUUID().toString().substring(0, UUID_LENGTH);
                
                // 组合新文件名
                String newFilename = extension != null ?
                    String.format("%s_%s_%s.%s", processedName, timestamp, shortUuid, extension) :
                    String.format("%s_%s_%s", processedName, timestamp, shortUuid);

                // 确保最终文件名不超过最大长度
                if (newFilename.length() > MAX_FILENAME_LENGTH) {
                    log.warn("生成的文件名超过最大长度限制，将进行截断处理");
                    // 重新处理文件名主体，考虑更短的长度
                    processedName = processBaseName(nameWithoutExt, suffixLength, MAX_FILENAME_LENGTH - suffixLength);
                    newFilename = extension != null ?
                        String.format("%s_%s_%s.%s", processedName, timestamp, shortUuid, extension) :
                        String.format("%s_%s_%s", processedName, timestamp, shortUuid);
                }
                
                // 检查文件名是否已存在
                LambdaQueryWrapper<FileNode> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(FileNode::getParentId, parentId)
                      .eq(FileNode::getName, newFilename)
                      .eq(FileNode::getNodeType, 2)
                      .eq(FileNode::getDeleted, 0);
                
                if (count(wrapper) == 0) {
                    return newFilename;
                }
                
                attempt++;
                // 如果文件名已存在，等待一小段随机时间后重试
                Thread.sleep(new Random().nextInt(100));
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new ApiException("生成唯一文件名被中断");
            }
        }
        
        throw new ApiException("无法生成唯一文件名，请稍后重试");
    }

    /**
     * 处理文件名主体，确保其长度合适
     * @param baseName 原始文件名主体
     * @param suffixLength 后缀部分的长度（时间戳、UUID、扩展名等）
     * @param maxLength 可选参数，指定主体的最大长度
     * @return 处理后的文件名主体
     */
    private String processBaseName(String baseName, int suffixLength, int... maxLength) {
        if (baseName == null || baseName.isEmpty()) {
            return "unnamed";
        }

        // 确定文件名主体的最大长度
        int maxBaseLength = maxLength.length > 0 ? 
            maxLength[0] : 
            Math.min(MAX_BASENAME_LENGTH, MAX_FILENAME_LENGTH - suffixLength);

        // 如果文件名主体超过最大长度，进行截断
        if (baseName.length() > maxBaseLength) {
            // 预留TRUNCATE_MARKER的长度
            int truncateLength = maxBaseLength - TRUNCATE_MARKER.length();
            if (truncateLength > 0) {
                return baseName.substring(0, truncateLength) + TRUNCATE_MARKER;
            } else {
                // 如果连省略号都放不下，直接截断到最大长度
                return baseName.substring(0, maxBaseLength);
            }
        }

        return baseName;
    }

    @Override
    @Transactional
    public void deleteFile(Long fileId, Long userId) {
        // 1. 获取文件信息
        FileNode file = getById(fileId);
        if (file == null || file.getNodeType() != 2) {
            throw new ApiException("文件不存在或不是文件");
        }

        // 2. 从MinIO删除文件
        try {
            if (StringUtils.hasText(file.getStoragePath())) {
                minioClient.removeObject(
                    RemoveObjectArgs.builder()
                        .bucket(bucketName)
                        .object(file.getStoragePath())
                        .build()
                );
            } else {
                log.warn("文件存储路径为空，跳过MinIO删除，文件ID: {}", file.getId());
            }
        } catch (Exception e) {
            log.error("从MinIO删除文件失败", e);
        }

        // 3. 删除文件节点
        removeById(fileId);
    }

    @Override
    public String generateStoragePath(String originalFilename, Long userId, KnowledgeBase kb) {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String ext = StringUtils.getFilenameExtension(originalFilename);
        LocalDateTime now = LocalDateTime.now();
        
        if (kb != null) {
            // 知识库文件路径：kb/{kb_id}/{kb_name}/{year}/{month}/{uuid_prefix}/{uuid}.{ext}
            return String.format("kb/%d/%s/%d/%02d/%s/%s.%s",
                kb.getId(),                  // 知识库ID
                sanitizePath(kb.getName()),  // 知识库名称（安全处理后）
                now.getYear(),              // 年份
                now.getMonthValue(),        // 月份
                uuid.substring(0, 2),       // UUID前缀（目录分片）
                uuid.substring(2),          // UUID剩余部分
                ext                         // 文件扩展名
            );
        } else {
            // 个人文件路径：user/{user_id}/{year}/{month}/{uuid_prefix}/{uuid}.{ext}
            return String.format("user/%d/%d/%02d/%s/%s.%s",
                userId,                     // 用户ID
                now.getYear(),              // 年份
                now.getMonthValue(),        // 月份
                uuid.substring(0, 2),       // UUID前缀（目录分片）
                uuid.substring(2),          // UUID剩余部分
                ext                         // 文件扩展名
            );
        }
    }

    // 路径安全处理方法（用于存储路径）
    private String sanitizePath(String name) {
        return name == null ? "" : name.replaceAll("[^a-zA-Z0-9_-]", "_");
    }

    @Override
    public String buildFullPath(Long parentId, String name) {
        if (parentId == null) {
            return "/" + name;
        }
        FileNode parent = getById(parentId);
        return parent.getFullPath() + "/" + name;
    }

    @Override
    public FileNode getOrCreateKbRootFolder(Long kbId, Long userId) {
        // 1. 获取知识库信息
        KnowledgeBase kb = knowledgeBaseMapper.selectById(kbId);
        if (kb == null) {
            throw new ApiException("知识库不存在");
        }

        // 2. 获取或创建.knowledgebase根目录
        final String ROOT_FOLDER = ".knowledgebase";
        LambdaQueryWrapper<FileNode> rootWrapper = new LambdaQueryWrapper<>();
        rootWrapper.eq(FileNode::getName, ROOT_FOLDER)
                  .isNull(FileNode::getParentId)
                  .eq(FileNode::getNodeType, 1)
                  .eq(FileNode::getDeleted, 0);
        
        FileNode rootNode = getOne(rootWrapper);
        if (rootNode == null) {
            rootNode = createFolder(ROOT_FOLDER, null, userId, true); // 系统创建的根目录
            log.info("创建知识库根目录: {}", rootNode.getFullPath());
        }

        // 3. 获取或创建知识库专属目录
        LambdaQueryWrapper<FileNode> kbWrapper = new LambdaQueryWrapper<>();
        kbWrapper.eq(FileNode::getName, kb.getName())
                .eq(FileNode::getParentId, rootNode.getId())
                .eq(FileNode::getNodeType, 1)
                .eq(FileNode::getDeleted, 0);
        
        FileNode kbNode = getOne(kbWrapper);
        if (kbNode == null) {
            kbNode = createFolder(kb.getName(), rootNode.getId(), userId, false); // 知识库目录按普通规则创建
            log.info("创建知识库目录: {}", kbNode.getFullPath());
        }

        return kbNode;
    }

    @Override
    public void downloadFile(Long fileId, HttpServletResponse response, Long userId) {
        // 1. 获取文件信息
        FileNode file = getById(fileId);
        if (file == null || file.getNodeType() != 2) {
            throw new ApiException("文件不存在或不是文件");
        }

        try {
            // 2. 从MinIO获取文件
            GetObjectResponse objectResponse = minioClient.getObject(
                GetObjectArgs.builder()
                    .bucket(bucketName)
                    .object(file.getStoragePath())
                    .build()
            );

            // 3. 设置响应头
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + 
                java.net.URLEncoder.encode(file.getName(), "UTF-8").replaceAll("\\+", "%20") + "\"");
            response.setHeader("Content-Length", String.valueOf(file.getFileSize()));

            // 4. 写入响应流
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = objectResponse.read(buffer)) != -1) {
                response.getOutputStream().write(buffer, 0, bytesRead);
            }
            response.getOutputStream().flush();
        } catch (Exception e) {
            log.error("下载文件失败", e);
            throw new ApiException("下载文件失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public void parseFiles(Long kbId, List<Long> fileIds, Long userId) {
        try {
            // 1. 参数校验
            if (kbId == null) {
                throw new ApiException("知识库ID不能为空");
            }
            if (fileIds == null || fileIds.isEmpty()) {
                throw new ApiException("文件ID列表不能为空");
            }

            // 2. 获取知识库信息
            KnowledgeBase kb = knowledgeBaseMapper.selectById(kbId);
            if (kb == null) {
                throw new ApiException("知识库不存在");
            }

            // 3. 获取文件信息
            List<FileNode> files = listByIds(fileIds);
            if (files.isEmpty()) {
                throw new ApiException("未找到指定的文件");
            }

            // 4. 验证文件与知识库的关联关系
            List<KbFileRel> rels = new ArrayList<>();
            for (FileNode file : files) {
                // 根据文件ID和知识库ID查询唯一的关联关系
                LambdaQueryWrapper<KbFileRel> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(KbFileRel::getFileNodeId, file.getId())
                       .eq(KbFileRel::getKbId, kbId);
                KbFileRel rel = kbFileRelMapper.selectOne(wrapper);
                
                if (rel == null) {
                    throw new ApiException("文件未关联到指定知识库：" + file.getName());
                }
                
                rels.add(rel);
            }

            // 5. 调用Ragflow解析文件
            List<String> documentIds = rels.stream()
                .map(KbFileRel::getDocumentId)
                .filter(id -> id != null)
                .toList();

            if (documentIds.isEmpty()) {
                throw new ApiException("没有可解析的文件");
            }

            String result = ragClient.parseDocuments(kb.getDatasetId(), documentIds);
            
            // 处理返回结果
            RagflowUtils.handleResponse(result, "解析文件失败");
            
            // 6. 更新文件状态
            for (KbFileRel rel : rels) {
                if (rel.getDocumentId() != null) {
                    rel.setDocumentStatus(Constants.DocumentStatus.PROCESSING);
                    kbFileRelMapper.updateById(rel);
                }
            }
        } catch (Exception e) {
            log.error("解析文件失败，知识库ID: {}, 文件ID列表: {}", kbId, fileIds, e);
            throw new ApiException("解析文件失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public void stopParsingFiles(Long kbId, List<Long> fileIds, Long userId) {
        try {
            // 1. 参数校验
            if (kbId == null) {
                throw new ApiException("知识库ID不能为空");
            }
            if (fileIds == null || fileIds.isEmpty()) {
                throw new ApiException("文件ID列表不能为空");
            }

            // 2. 获取知识库信息
            KnowledgeBase kb = knowledgeBaseMapper.selectById(kbId);
            if (kb == null) {
                throw new ApiException("知识库不存在");
            }

            // 3. 获取文件信息
            List<FileNode> files = listByIds(fileIds);
            if (files.isEmpty()) {
                throw new ApiException("未找到指定的文件");
            }

            // 4. 验证文件与知识库的关联关系
            List<KbFileRel> rels = new ArrayList<>();
            for (FileNode file : files) {
                // 根据文件ID和知识库ID查询唯一的关联关系
                LambdaQueryWrapper<KbFileRel> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(KbFileRel::getFileNodeId, file.getId())
                       .eq(KbFileRel::getKbId, kbId);
                KbFileRel rel = kbFileRelMapper.selectOne(wrapper);
                
                if (rel == null) {
                    throw new ApiException("文件未关联到指定知识库：" + file.getName());
                }
                
                rels.add(rel);
            }

            // 5. 调用Ragflow终止解析
            List<String> documentIds = rels.stream()
                .map(KbFileRel::getDocumentId)
                .filter(id -> id != null)
                .toList();

            if (documentIds.isEmpty()) {
                throw new ApiException("没有可终止解析的文件");
            }

            String result = ragClient.stopParsingDocuments(kb.getDatasetId(), documentIds);
            
            // 处理返回结果
            RagflowUtils.handleResponse(result, "终止文件解析失败");
            
            // 6. 更新文件状态
            for (KbFileRel rel : rels) {
                if (rel.getDocumentId() != null) {
                    rel.setDocumentStatus(Constants.DocumentStatus.CANCELED);
                    kbFileRelMapper.updateById(rel);
                }
            }
        } catch (Exception e) {
            log.error("终止文件解析失败，知识库ID: {}, 文件ID列表: {}", kbId, fileIds, e);
            throw new ApiException("终止文件解析失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public void updateDocument(Long kbId, Long fileId, RagflowUploadVO.UploadFileVO updateVO, Long userId) {
        // 1. 参数校验
        if (kbId == null || fileId == null || updateVO == null || userId == null) {
            throw new ApiException("参数不能为空");
        }

        // 2. 查询文件和知识库信息
        FileNode fileNode = getById(fileId);
        if (fileNode == null) {
            throw new ApiException("文件不存在");
        }

        KnowledgeBase kb = knowledgeBaseMapper.selectById(kbId);
        if (kb == null) {
            throw new ApiException("知识库不存在");
        }

        // 3. 验证文件是否属于该知识库并获取关联关系
        LambdaQueryWrapper<KbFileRel> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(KbFileRel::getKbId, kbId)
               .eq(KbFileRel::getFileNodeId, fileId);
        KbFileRel rel = kbFileRelMapper.selectOne(wrapper);
        if (rel == null) {
            throw new ApiException("文件不属于该知识库");
        }

        // 4. 验证文件是否已上传到Ragflow
        if (rel.getDocumentId() == null) {
            throw new ApiException("文件尚未上传到Ragflow");
        }

        processSyncDocumentForRagflow(fileId, updateVO, kb, rel, fileNode);
    }

    private void processSyncDocumentForRagflow(Long fileId, RagflowUploadVO.UploadFileVO updateVO, KnowledgeBase kb, KbFileRel rel, FileNode fileNode) {
        try {
            // 5. 验证是否有需要更新的字段
            if (updateVO.getName() == null &&
                updateVO.getMeta_fields() == null &&
                updateVO.getChunk_method() == null) {
                throw new ApiException("没有需要更新的字段");
            }

            // 6. 构建更新字段
            Map<String, Object> updateFields = new HashMap<>();
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(updateVO.getName())) {
                updateFields.put("name", updateVO.getName());
            }
            if (updateVO.getMeta_fields() != null && !updateVO.getMeta_fields().isEmpty()) {
                updateFields.put("meta_fields", updateVO.getMeta_fields());
            }
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(updateVO.getChunk_method())) {
                updateFields.put("chunk_method", updateVO.getChunk_method());
                updateFields.put("parser_config", updateVO.getParser_config());
            }

            // 7. 调用Ragflow API更新文档
            String response = ragClient.updateDocument(
                kb.getDatasetId(),
                rel.getDocumentId(),
                updateFields
            );

            // 8. 处理Ragflow返回结果
            RagflowUtils.handleResponse(response, "更新文档元数据失败");

            Map<String, String> params = new HashMap<>();
            params.put("id", rel.getDocumentId());
            String res = ragClient.listDocuments(kb.getDatasetId(), params);

            ObjectMapper mapper = new ObjectMapper();
            RagflowDocsListVO docsList = mapper.readValue(res, RagflowDocsListVO.class);
            if (docsList.getCode() == 0) {
                List<RagflowDocsListVO.UploadFileVO> docs = docsList.getData().getDocs();
                if (!CollectionUtils.isEmpty(docs)) {
                    RagflowDocsListVO.UploadFileVO doc = docs.get(0);
                    updateDocumentInfo(rel, doc);
                }
            }

            log.info("更新文档元数据后，查询文档信息: {}", res);

            // 9. 更新本地文件节点信息
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(updateVO.getName()) && !updateVO.getName().equals(fileNode.getName())) {
                // 检查新名称在目标目录是否已存在
                checkDuplicateName(fileNode.getParentId(), updateVO.getName(), fileNode.getId());

                fileNode.setName(updateVO.getName());
                fileNode.setFullPath(buildFullPath(fileNode.getParentId(), updateVO.getName()));
                updateById(fileNode);
            }

            log.info("更新文档元数据成功，fileId: {}, response: {}", fileId, response);
        } catch (ApiException e) {
            log.error("更新文档元数据失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("更新文档元数据失败", e);
            throw new ApiException("更新文档元数据失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional
    public void batchUpdateDocumentStatus(Long kbId, List<Long> fileIds, String status, Long userId) {
        // 1. 参数校验
        if (kbId == null || fileIds == null || fileIds.isEmpty() || status == null || userId == null) {
            throw new ApiException("参数不能为空");
        }

        // 2. 查询知识库信息
        KnowledgeBase kb = knowledgeBaseMapper.selectById(kbId);
        if (kb == null) {
            throw new ApiException("知识库不存在");
        }

        // 3. 批量查询文件关联关系
        LambdaQueryWrapper<KbFileRel> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(KbFileRel::getKbId, kbId)
               .in(KbFileRel::getFileNodeId, fileIds);
        List<KbFileRel> rels = kbFileRelMapper.selectList(wrapper);
        
        if (rels.isEmpty()) {
            throw new ApiException("没有找到属于该知识库的文件");
        }

        // 4. 过滤出已上传到Ragflow的文件
        List<KbFileRel> validRels = rels.stream()
                .filter(rel -> rel.getDocumentId() != null)
                .collect(Collectors.toList());
        
        if (validRels.isEmpty()) {
            throw new ApiException("没有找到已上传到Ragflow的文件");
        }

        // 5. 批量更新状态
        List<String> successfulUpdates = new ArrayList<>();
        List<String> failedUpdates = new ArrayList<>();

        processDocStatusToRagflow(status, validRels, kb, successfulUpdates, failedUpdates);
    }

    private void processDocStatusToRagflow(String status, List<KbFileRel> validRels, KnowledgeBase kb, List<String> successfulUpdates, List<String> failedUpdates) {
        for (KbFileRel rel : validRels) {
            try {
                // 6. 构建更新字段，只更新status
                Map<String, Object> updateFields = new HashMap<>();
                updateFields.put("status", status);

                // 7. 调用Ragflow API更新文档状态
                String response = ragClient.updateDocument(
                    kb.getDatasetId(),
                    rel.getDocumentId(),
                    updateFields
                );

                // 8. 处理Ragflow返回结果
                RagflowUtils.handleResponse(response, "更新文档状态失败");

                Map<String,String> p1 = new HashMap<>();
                p1.put("id", rel.getDocumentId());
                String res = ragClient.listDocuments(kb.getDatasetId(),p1);
                log.info(res);

                successfulUpdates.add(rel.getDocumentId());

                log.info("批量更新文档状态成功，documentId: {}, status: {}", rel.getDocumentId(), status);
            } catch (Exception e) {
                failedUpdates.add(rel.getDocumentId());
                log.error("批量更新文档状态失败，documentId: {}, error: {}", rel.getDocumentId(), e.getMessage());
            }
        }

        // 9. 同步更新本地document_info中的状态
        List<KbFileRel> successfulRels = validRels.stream()
                .filter(rel -> successfulUpdates.contains(rel.getDocumentId()))
                .collect(Collectors.toList());

        for (KbFileRel rel : successfulRels) {
            try {
                updateDocumentStatusInfo(rel, status);
            } catch (Exception e) {
                log.error("更新本地文档状态信息失败，documentId: {}, error: {}", rel.getDocumentId(), e.getMessage());
            }
        }

        // 10. 记录更新结果
        if (!successfulUpdates.isEmpty()) {
            log.info("批量更新文档状态完成，成功更新 {} 个文档，失败 {} 个文档",
                    successfulUpdates.size(), failedUpdates.size());
        }

        if (!failedUpdates.isEmpty()) {
            log.warn("批量更新文档状态部分失败，失败的文档ID: {}", failedUpdates);
            throw new ApiException("部分文档状态更新失败，失败数量: " + failedUpdates.size());
        }
    }

    /**
     * 验证chunk_method和parser_config的合法性
     */
    private void validateChunkMethodAndConfig(DocumentUpdateVO updateVO) {
        String method = updateVO.getChunk_method();
        DocumentUpdateVO.ParserConfigVO config = updateVO.getParser_config();

        // 如果是table、picture、one或email方法，parser_config必须为空
        if (List.of("table", "picture", "one", "email").contains(method)) {
            if (config != null && (
                config.getChunk_token_count() != null ||
                config.getLayout_recognize() != null ||
                config.getHtml4excel() != null ||
                config.getDelimiter() != null ||
                config.getTask_page_size() != null ||
                config.getRaptor() != null
            )) {
                throw new ApiException("当chunk_method为" + method + "时，parser_config必须为空");
            }
            return;
        }

        // 如果是naive方法，可以设置所有配置
        if ("naive".equals(method)) {
            return;
        }

        // 如果是qa、manual、paper、book、laws或presentation方法，只能设置raptor
        if (List.of("qa", "manual", "paper", "book", "laws", "presentation").contains(method)) {
            if (config != null && (
                config.getChunk_token_count() != null ||
                config.getLayout_recognize() != null ||
                config.getHtml4excel() != null ||
                config.getDelimiter() != null ||
                config.getTask_page_size() != null
            )) {
                throw new ApiException("当chunk_method为" + method + "时，parser_config只能包含raptor配置");
            }
            return;
        }

        throw new ApiException("不支持的chunk_method：" + method);
    }

    /**
     * 检查目标位置是否存在同名文件或文件夹
     */
    private void checkDuplicateName(Long parentId, String name, Long excludeId) {
        LambdaQueryWrapper<FileNode> wrapper = new LambdaQueryWrapper<>();
        if (parentId != null) {
            wrapper.eq(FileNode::getParentId, parentId);
        } else {
            wrapper.isNull(FileNode::getParentId);
        }
        wrapper.eq(FileNode::getName, name)
               .eq(FileNode::getDeleted, 0)
               .ne(FileNode::getId, excludeId);
        
        if (count(wrapper) > 0) {
            throw new ApiException("目标位置已存在同名文件或文件夹");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void moveNode(Long nodeId, Long targetFolderId, Long userId) {
        // 1. 获取要移动的节点
        FileNode node = getById(nodeId);
        if (node == null) {
            throw new ApiException("要移动的文件或文件夹不存在");
        }

        // 2. 校验顶层目录的移动规则
        if (targetFolderId != null && targetFolderId == -1L) {
            // 只允许文件夹移动到顶层目录
            if (node.getNodeType() != 1) {
                throw new ApiException("只能将文件夹移动到顶层目录");
            }
        } else if (targetFolderId != null) {
            // 如果目标不是顶层目录，验证目标文件夹
            FileNode targetFolder = getById(targetFolderId);
            if (targetFolder == null || targetFolder.getNodeType() != 1) {
                throw new ApiException("目标文件夹不存在或不是文件夹");
            }

            // 检查是否形成循环引用
            if (isCircularReference(node, targetFolderId)) {
                throw new ApiException("不能将文件夹移动到其子文件夹中");
            }
        }

        // 3. 构建新的完整路径
        String newFullPath;
        if (targetFolderId != null && targetFolderId == -1L) {
            // 顶层目录路径特殊处理
            newFullPath = "/" + node.getName();
        } else {
            newFullPath = buildFullPath(targetFolderId, node.getName());
        }

        // 4. 检查目标位置是否有同名文件
        checkDuplicateName(targetFolderId, node.getName(), nodeId);

        // 5. 如果是文件夹，更新所有子节点的路径
        if (node.getNodeType() == 1) {
            String oldPath = node.getFullPath();
            List<FileNode> children = list(new LambdaQueryWrapper<FileNode>()
                    .likeRight(FileNode::getFullPath, oldPath + "/"));
            
            if (!children.isEmpty()) {
                List<FileNode> batchUpdateList = new ArrayList<>();
                for (FileNode child : children) {
                    String childNewPath = newFullPath + child.getFullPath().substring(oldPath.length());
                    child.setFullPath(childNewPath);
                    child.setUpdateTime(LocalDateTime.now());
                    batchUpdateList.add(child);
                    
                    if (batchUpdateList.size() >= 500) {
                        updateBatchById(batchUpdateList);
                        batchUpdateList.clear();
                    }
                }
                
                if (!batchUpdateList.isEmpty()) {
                    updateBatchById(batchUpdateList);
                }
            }
        }

        // 6. 更新节点信息
        node.setParentId(targetFolderId != null && targetFolderId == -1L ? null : targetFolderId);
        node.setFullPath(newFullPath);
        node.setUpdateTime(LocalDateTime.now());
        updateById(node);
    }

    /**
     * 检查是否形成循环引用
     */
    private boolean isCircularReference(FileNode node, Long targetFolderId) {
        if (node.getNodeType() != 1) {
            return false;
        }
        
        String nodePath = node.getFullPath();
        FileNode targetFolder = getById(targetFolderId);
        return targetFolder != null && targetFolder.getFullPath().startsWith(nodePath + "/");
    }

    /**
     * 重命名文件或文件夹
     *
     * @param nodeId 要重命名的节点ID
     * @param newName 新的名称
     * @param userId 当前用户ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void renameNode(Long nodeId, String newName, Long userId) {
        // 1. 获取节点信息
        FileNode node = getById(nodeId);
        if (node == null) {
            throw new ApiException("文件或文件夹不存在");
        }

        // 2. 验证新名称
        if (!StringUtils.hasText(newName)) {
            throw new ApiException("新名称不能为空");
        }
        
        // 3. 检查新名称的合法性（重命名不允许系统级特殊名称）
        if (!isValidFileName(newName, false)) {
            throw new ApiException("名称包含非法字符，不能包含 < > : \" | ? * \\ / 等特殊字符，且不能以点开头或结尾");
        }

        // 4. 检查同级目录下是否有重名
        checkDuplicateName(node.getParentId(), newName, nodeId);

        // 5. 如果是文件，需要检查ragflow端的状态和名称冲突
        if (node.getNodeType() == 2) {
            checkFileRenameConditions(nodeId, newName);
        }

        // 6. 构建新的完整路径
        String newFullPath = buildFullPath(node.getParentId(), newName);

        // 7. 如果是文件夹，需要批量更新所有子节点的路径
        if (node.getNodeType() == 1) {
            String oldPath = node.getFullPath();
            // 获取所有需要更新的子节点
            List<FileNode> children = list(new LambdaQueryWrapper<FileNode>()
                    .likeRight(FileNode::getFullPath, oldPath + "/"));
            
            if (!children.isEmpty()) {
                List<FileNode> batchUpdateList = new ArrayList<>();
                for (FileNode child : children) {
                    String childNewPath = newFullPath + child.getFullPath().substring(oldPath.length());
                    child.setFullPath(childNewPath);
                    child.setUpdateTime(LocalDateTime.now());
                    batchUpdateList.add(child);
                    
                    // 每500条执行一次批量更新，避免单次更新数据过多
                    if (batchUpdateList.size() >= 500) {
                        updateBatchById(batchUpdateList);
                        batchUpdateList.clear();
                    }
                }
                
                // 更新剩余的记录
                if (!batchUpdateList.isEmpty()) {
                    updateBatchById(batchUpdateList);
                }
            }
        }

        // 8. 更新节点信息
        node.setName(newName);
        node.setFullPath(newFullPath);
        node.setUpdateTime(LocalDateTime.now());
        
        updateById(node);
    }

    /**
     * 更新文档状态信息（仅更新status字段）
     * @param rel 关联关系
     * @param status 新的状态值
     */
    private void updateDocumentStatusInfo(KbFileRel rel, String status) {
        try {
            if (rel.getDocumentInfo() == null || rel.getDocumentInfo().trim().isEmpty()) {
                log.warn("文档信息为空，无法更新状态，关联ID: {}", rel.getId());
                return;
            }

            ObjectMapper mapper = new ObjectMapper();
            // 解析原配置信息
            JsonNode originalJsonNode = mapper.readTree(rel.getDocumentInfo());
            RagflowUploadVO originalVO = new RagflowUploadVO();
            originalVO.setData(List.of(mapper.treeToValue(originalJsonNode.get(0), RagflowUploadVO.UploadFileVO.class)));
            
            RagflowUploadVO.UploadFileVO originalDoc = originalVO.getData().get(0);
            boolean hasChanges = false;
            
            // 只更新status字段
            if (!Objects.equals(originalDoc.getStatus(), status)) {
                originalDoc.setStatus(status);
                hasChanges = true;
            }
            
            // 只有在有变化时才更新数据库
            if (hasChanges) {
                String updatedDocumentInfo = mapper.writeValueAsString(List.of(originalDoc));
                rel.setDocumentInfo(updatedDocumentInfo);
                kbFileRelMapper.updateById(rel);
                log.info("文档状态信息已更新，关联ID: {}, 新状态: {}", rel.getId(), status);
            } else {
                log.debug("文档状态未发生变化，不需要更新，关联ID: {}", rel.getId());
            }
        } catch (Exception e) {
            log.error("更新文档状态信息失败", e);
            throw new ApiException("更新文档状态信息失败: " + e.getMessage());
        }
    }

    /**
     * 更新文档信息
     * @param rel 关联关系
     * @param doc Ragflow文档信息
     */
    private void updateDocumentInfo(KbFileRel rel, RagflowDocsListVO.UploadFileVO doc) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            // 解析原配置信息
            JsonNode originalJsonNode = mapper.readTree(rel.getDocumentInfo());
            RagflowUploadVO originalVO = new RagflowUploadVO();
            originalVO.setData(List.of(mapper.treeToValue(originalJsonNode.get(0), RagflowUploadVO.UploadFileVO.class)));
            
            RagflowUploadVO.UploadFileVO originalDoc = originalVO.getData().get(0);
            boolean hasChanges = false;
            
            // 更新基本字段
            hasChanges |= updateBasicFields(originalDoc, doc);
            
            // 更新解析器配置
            if (doc.getParser_config() != null) {
                hasChanges |= updateParserConfig(originalDoc, doc);
            }
            
            // 更新状态相关字段
            hasChanges |= updateStatusFields(originalDoc, doc);
            
            // 只有在有变化时才更新数据库
            if (hasChanges) {
                String updatedDocumentInfo = mapper.writeValueAsString(List.of(originalDoc));
                rel.setDocumentInfo(updatedDocumentInfo);
                kbFileRelMapper.updateById(rel);
                log.info("文档信息已更新，关联ID: {}", rel.getId());
            } else {
                log.debug("文档信息未发生变化，不需要更新，关联ID: {}", rel.getId());
            }
        } catch (Exception e) {
            log.error("更新文档信息失败", e);
            throw new ApiException("更新文档信息失败: " + e.getMessage());
        }
    }

    /**
     * 更新基本字段
     */
    private boolean updateBasicFields(RagflowUploadVO.UploadFileVO originalDoc, RagflowDocsListVO.UploadFileVO doc) {
        boolean hasChanges = false;
        
        if (!Objects.equals(originalDoc.getName(), doc.getName())) {
            originalDoc.setName(doc.getName());
            hasChanges = true;
        }
        
        if (!Objects.equals(originalDoc.getChunk_method(), doc.getChunk_method())) {
            originalDoc.setChunk_method(doc.getChunk_method());
            hasChanges = true;
        }
        
        if (!Objects.equals(originalDoc.getMeta_fields(), doc.getMeta_fields())) {
            originalDoc.setMeta_fields(doc.getMeta_fields());
            hasChanges = true;
        }

        if (!Objects.equals(originalDoc.getLocation(), doc.getLocation())) {
            originalDoc.setLocation(doc.getLocation());
            hasChanges = true;
        }

        if (!Objects.equals(originalDoc.getSize(), doc.getSize())) {
            originalDoc.setSize(doc.getSize());
            hasChanges = true;
        }

        if (!Objects.equals(originalDoc.getThumbnail(), doc.getThumbnail())) {
            originalDoc.setThumbnail(doc.getThumbnail());
            hasChanges = true;
        }

        if (!Objects.equals(originalDoc.getType(), doc.getType())) {
            originalDoc.setType(doc.getType());
            hasChanges = true;
        }

        if (!Objects.equals(originalDoc.getCreate_date(), doc.getCreate_date())) {
            originalDoc.setCreate_date(doc.getCreate_date());
            hasChanges = true;
        }

        if (!Objects.equals(originalDoc.getCreate_time(), doc.getCreate_time())) {
            originalDoc.setCreate_time(doc.getCreate_time());
            hasChanges = true;
        }

        if (!Objects.equals(originalDoc.getSource_type(), doc.getSource_type())) {
            originalDoc.setSource_type(doc.getSource_type());
            hasChanges = true;
        }
        
        return hasChanges;
    }

    /**
     * 更新解析器配置
     */
    private boolean updateParserConfig(RagflowUploadVO.UploadFileVO originalDoc, RagflowDocsListVO.UploadFileVO doc) {
        boolean hasChanges = false;
        
        RagflowBaseVO.BaseParserConfigVO originalParserConfig = originalDoc.getParser_config();
        RagflowBaseVO.BaseParserConfigVO newParserConfig = doc.getParser_config();
        
        if (originalParserConfig == null) {
            originalParserConfig = new RagflowBaseVO.BaseParserConfigVO();
            hasChanges = true;
        }
        
        // 更新基本解析器配置
        hasChanges |= updateBasicParserConfig(originalParserConfig, newParserConfig);
        
        // 更新Raptor配置
        if (newParserConfig != null && newParserConfig.getRaptor() != null) {
            hasChanges |= updateRaptorConfig(originalParserConfig, newParserConfig);
        }
        
        originalDoc.setParser_config(originalParserConfig);
        return hasChanges;
    }

    /**
     * 更新基本解析器配置
     */
    private boolean updateBasicParserConfig(RagflowBaseVO.BaseParserConfigVO originalConfig, RagflowBaseVO.BaseParserConfigVO newConfig) {
        boolean hasChanges = false;
        
        if (!Objects.equals(originalConfig.getChunk_token_num(), newConfig.getChunk_token_num())) {
            originalConfig.setChunk_token_num(newConfig.getChunk_token_num());
            hasChanges = true;
        }
        
        if (!Objects.equals(originalConfig.getDelimiter(), newConfig.getDelimiter())) {
            originalConfig.setDelimiter(newConfig.getDelimiter());
            hasChanges = true;
        }
        
        if (!Objects.equals(originalConfig.getHtml4excel(), newConfig.getHtml4excel())) {
            originalConfig.setHtml4excel(newConfig.getHtml4excel());
            hasChanges = true;
        }
        
        if (!Objects.equals(originalConfig.getLayout_recognize(), newConfig.getLayout_recognize())) {
            originalConfig.setLayout_recognize(newConfig.getLayout_recognize());
            hasChanges = true;
        }

        // 添加新的解析器配置字段对比
        if (!Objects.equals(originalConfig.getAuto_keywords(), newConfig.getAuto_keywords())) {
            originalConfig.setAuto_keywords(newConfig.getAuto_keywords());
            hasChanges = true;
        }

        if (!Objects.equals(originalConfig.getAuto_questions(), newConfig.getAuto_questions())) {
            originalConfig.setAuto_questions(newConfig.getAuto_questions());
            hasChanges = true;
        }

        if (!Objects.equals(originalConfig.getPages(), newConfig.getPages())) {
            originalConfig.setPages(newConfig.getPages());
            hasChanges = true;
        }

        if (!Objects.equals(originalConfig.getTask_page_size(), newConfig.getTask_page_size())) {
            originalConfig.setTask_page_size(newConfig.getTask_page_size());
            hasChanges = true;
        }
        
        return hasChanges;
    }

    /**
     * 更新Raptor配置
     */
    private boolean updateRaptorConfig(RagflowBaseVO.BaseParserConfigVO originalConfig, RagflowBaseVO.BaseParserConfigVO newConfig) {
        boolean hasChanges = false;
        
        RagflowBaseVO.BaseRaptorVO originalRaptor = originalConfig.getRaptor();
        if (originalRaptor == null) {
            originalRaptor = new RagflowBaseVO.BaseRaptorVO();
            hasChanges = true;
        }
        
        ObjectMapper mapper = new ObjectMapper();
        JsonNode raptorNode = mapper.valueToTree(newConfig.getRaptor());
        
        if (!Objects.equals(originalRaptor.getUse_raptor(), raptorNode.get("use_raptor").asBoolean())) {
            originalRaptor.setUse_raptor(raptorNode.get("use_raptor").asBoolean());
            hasChanges = true;
        }
        
        if (raptorNode.has("max_cluster") && 
            !Objects.equals(originalRaptor.getMax_cluster(), raptorNode.get("max_cluster").asInt())) {
            originalRaptor.setMax_cluster(raptorNode.get("max_cluster").asInt());
            hasChanges = true;
        }
        
        if (raptorNode.has("max_token") && 
            !Objects.equals(originalRaptor.getMax_token(), raptorNode.get("max_token").asInt())) {
            originalRaptor.setMax_token(raptorNode.get("max_token").asInt());
            hasChanges = true;
        }
        
        if (raptorNode.has("prompt") && 
            !Objects.equals(originalRaptor.getPrompt(), raptorNode.get("prompt").asText())) {
            originalRaptor.setPrompt(raptorNode.get("prompt").asText());
            hasChanges = true;
        }
        
        if (raptorNode.has("random_seed") && 
            !Objects.equals(originalRaptor.getRandom_seed(), raptorNode.get("random_seed").asInt())) {
            originalRaptor.setRandom_seed(raptorNode.get("random_seed").asInt());
            hasChanges = true;
        }
        
        if (raptorNode.has("threshold") && 
            !Objects.equals(originalRaptor.getThreshold(), raptorNode.get("threshold").asDouble())) {
            originalRaptor.setThreshold(raptorNode.get("threshold").asDouble());
            hasChanges = true;
        }
        
        originalConfig.setRaptor(originalRaptor);
        return hasChanges;
    }

    /**
     * 更新状态相关字段
     */
    private boolean updateStatusFields(RagflowUploadVO.UploadFileVO originalDoc, RagflowDocsListVO.UploadFileVO doc) {
        boolean hasChanges = false;
        
        if (!Objects.equals(originalDoc.getStatus(), doc.getStatus())) {
            originalDoc.setStatus(doc.getStatus());
            hasChanges = true;
        }
        
        if (!Objects.equals(originalDoc.getProgress(), doc.getProgress())) {
            originalDoc.setProgress(doc.getProgress());
            hasChanges = true;
        }
        
        if (!Objects.equals(originalDoc.getProgress_msg(), doc.getProgress_msg())) {
            originalDoc.setProgress_msg(doc.getProgress_msg());
            hasChanges = true;
        }
        
        if (!Objects.equals(originalDoc.getUpdate_time(), doc.getUpdate_time())) {
            originalDoc.setUpdate_time(doc.getUpdate_time());
            hasChanges = true;
        }
        
        if (!Objects.equals(originalDoc.getUpdate_date(), doc.getUpdate_date())) {
            originalDoc.setUpdate_date(doc.getUpdate_date());
            hasChanges = true;
        }
        
        if (!Objects.equals(originalDoc.getChunk_count(), doc.getChunk_count())) {
            originalDoc.setChunk_count(doc.getChunk_count());
            hasChanges = true;
        }
        
        if (!Objects.equals(originalDoc.getToken_count(), doc.getToken_count())) {
            originalDoc.setToken_count(doc.getToken_count());
            hasChanges = true;
        }
        
        // 处理process_begin_at的类型转换
        if (doc.getProcess_begin_at() != null) {
            try {
                String newProcessBeginAt = doc.getProcess_begin_at();
                if (!Objects.equals(originalDoc.getProcess_begin_at(), newProcessBeginAt)) {
                    originalDoc.setProcess_begin_at(newProcessBeginAt);
                    hasChanges = true;
                }
            } catch (Exception e) {
                log.warn("无法解析process_begin_at: {}", doc.getProcess_begin_at());
            }
        }
        
        if (!Objects.equals(originalDoc.getProcess_duation(), doc.getProcess_duation())) {
            originalDoc.setProcess_duation(doc.getProcess_duation());
            hasChanges = true;
        }
        
        return hasChanges;
    }

    /**
     * 批量更新关联关系
     * @param rels 需要更新的关联关系列表
     */
    private void updateBatchKbFileRels(List<KbFileRel> rels) {
        if (!rels.isEmpty()) {
            for (KbFileRel rel : rels) {
                kbFileRelMapper.updateById(rel);
            }
        }
    }

    /**
     * 验证文件名是否包含非法字符
     * 允许中文字符、英文字母、数字、下划线、连字符、空格、点号、括号等常用字符
     * 排除文件系统不支持的特殊字符
     * 
     * 合法的文件名示例：
     * - "我的文档.txt"
     * - "项目计划 2024.docx"
     * - "Meeting Notes (Final).pdf"
     * - "数据分析_v1.2.xlsx"
     * 
     * 不合法的文件名示例：
     * - "文件<1>.txt" (包含 <)
     * - "项目:重要.docx" (包含 :)
     * - "con.txt" (Windows保留名)
     * - ".hidden" (隐藏文件)
     * 
     * @param name 文件名
     * @param isSystemCreate 是否为系统创建（系统创建的特殊目录如.knowledgebase允许以点开头）
     * @return true如果文件名合法，false如果包含非法字符
     */
    private boolean isValidFileName(String name, boolean isSystemCreate) {
        if (name == null || name.trim().isEmpty()) {
            return false;
        }
        
        // 排除文件系统不支持的特殊字符
        // Windows系统禁用的字符: < > : " | ? * \ /
        // 加上一些其他可能有问题的控制字符
        String forbiddenChars = "[<>:\"|?*\\\\/\\x00-\\x1f\\x7f]";
        
        // 检查是否包含禁用字符
        if (name.matches(".*" + forbiddenChars + ".*")) {
            return false;
        }
        
        // 排除纯点号文件名（如 "." 或 ".."）
        if (name.equals(".") || name.equals("..")) {
            return false;
        }
        
        // 排除以点开头的隐藏文件名（但允许正常的扩展名文件，如 ".txt" 这种情况很少见，通常是隐藏文件）
        // 对于文件夹，不允许以点开头；对于文件，如果以点开头且后面没有其他点，则认为是隐藏文件
        // 系统创建的特殊目录（如.knowledgebase）允许以点开头
        if (name.startsWith(".") && name.indexOf('.', 1) == -1) {
            // 如果是系统创建的，允许特定的系统目录名
            if (isSystemCreate && ".knowledgebase".equals(name)) {
                // 允许系统创建.knowledgebase目录
            } else {
                return false;
            }
        }
        
        // 排除Windows保留文件名
        String[] reservedNames = {"CON", "PRN", "AUX", "NUL", 
                                  "COM1", "COM2", "COM3", "COM4", "COM5", "COM6", "COM7", "COM8", "COM9",
                                  "LPT1", "LPT2", "LPT3", "LPT4", "LPT5", "LPT6", "LPT7", "LPT8", "LPT9"};
        String upperName = name.toUpperCase();
        for (String reserved : reservedNames) {
            if (upperName.equals(reserved) || upperName.startsWith(reserved + ".")) {
                return false;
            }
        }
        
        return true;
    }

    @Override
    public boolean checkKbFolderExists(String kbName) {
        // 1. 获取.knowledgebase根目录
        LambdaQueryWrapper<FileNode> rootWrapper = new LambdaQueryWrapper<>();
        rootWrapper.eq(FileNode::getName, ".knowledgebase")
                  .isNull(FileNode::getParentId)
                  .eq(FileNode::getNodeType, 1)
                  .eq(FileNode::getDeleted, 0);
        
        FileNode rootNode = getOne(rootWrapper);
        if (rootNode == null) {
            return false; // 根目录都不存在，肯定知识库文件夹也不存在
        }

        // 2. 检查知识库名称对应的文件夹是否存在
        LambdaQueryWrapper<FileNode> kbWrapper = new LambdaQueryWrapper<>();
        kbWrapper.eq(FileNode::getName, kbName)
                .eq(FileNode::getParentId, rootNode.getId())
                .eq(FileNode::getNodeType, 1)
                .eq(FileNode::getDeleted, 0);
        
        return count(kbWrapper) > 0;
    }

    @Override
    public List<KbFileRel> getKbFileRelations(Long kbId) {
        LambdaQueryWrapper<KbFileRel> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(KbFileRel::getKbId, kbId)
               .eq(KbFileRel::getDeleted, 0);
        return kbFileRelMapper.selectList(wrapper);
    }

    @Override
    @Transactional
    public void deleteKbFolder(Long kbId, Long userId) {
        // 1. 获取知识库信息
        KnowledgeBase kb = knowledgeBaseMapper.selectById(kbId);
        if (kb == null) {
            throw new ApiException("知识库不存在");
        }

        // 2. 获取.knowledgebase根目录
        LambdaQueryWrapper<FileNode> rootWrapper = new LambdaQueryWrapper<>();
        rootWrapper.eq(FileNode::getName, ".knowledgebase")
                  .isNull(FileNode::getParentId)
                  .eq(FileNode::getNodeType, 1)
                  .eq(FileNode::getDeleted, 0);
        
        FileNode rootNode = getOne(rootWrapper);
        if (rootNode == null) {
            log.warn("知识库根目录不存在，无需删除");
            return;
        }

        // 3. 获取知识库专属目录
        LambdaQueryWrapper<FileNode> kbWrapper = new LambdaQueryWrapper<>();
        kbWrapper.eq(FileNode::getName, kb.getName())
                .eq(FileNode::getParentId, rootNode.getId())
                .eq(FileNode::getNodeType, 1)
                .eq(FileNode::getDeleted, 0);
        
        FileNode kbNode = getOne(kbWrapper);
        if (kbNode == null) {
            log.warn("知识库目录不存在，无需删除");
            return;
        }

        // 4. 删除知识库目录（deleteFolder会递归删除子目录）
        deleteFolder(kbNode.getId(), userId);
    }

    /**
     * 检查文件重命名的前置条件
     * 根据文件位置和关联关系数量采用不同的检查策略：
     * 1. 非知识库目录 + 无关联：只检查目录重名（已在上层checkDuplicateName中处理）
     * 2. 非知识库目录 + 有关联：报错要求解绑
     * 3. 知识库目录 + 单一关联：检查ragflow端状态和名称冲突
     * 4. 知识库目录 + 多个关联：报错要求解绑
     *
     * @param fileId 文件ID
     * @param newName 新名称
     * @throws ApiException 如果检查不通过
     */
    private void checkFileRenameConditions(Long fileId, String newName) {
        // 1. 获取文件信息
        FileNode fileNode = getById(fileId);
        if (fileNode == null) {
            throw new ApiException("文件不存在");
        }

        // 2. 查询文件的所有知识库关联关系
        List<KbFileRel> fileRels = getFileKbRelations(fileId);
        
        // 3. 判断文件是否在知识库目录下
        boolean isInKbDirectory = isFileInKnowledgeBaseDirectory(fileNode.getFullPath());
        
        // 4. 根据文件位置和关联关系采用不同策略
        if (!isInKbDirectory) {
            // 非知识库目录下的文件
            handleNonKbDirectoryFile(fileRels);
        } else {
            // 知识库目录下的文件
            handleKbDirectoryFile(fileRels, newName);
        }
    }

    /**
     * 获取文件的所有知识库关联关系
     *
     * @param fileId 文件ID
     * @return 关联关系列表
     */
    private List<KbFileRel> getFileKbRelations(Long fileId) {
        LambdaQueryWrapper<KbFileRel> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(KbFileRel::getFileNodeId, fileId)
               .eq(KbFileRel::getDeleted, 0);
        return kbFileRelMapper.selectList(wrapper);
    }

    /**
     * 判断文件是否在知识库目录下
     *
     * @param fullPath 文件完整路径
     * @return true如果在知识库目录下，false如果不在
     */
    private boolean isFileInKnowledgeBaseDirectory(String fullPath) {
        if (fullPath == null) {
            return false;
        }
        // 知识库目录的路径格式：/.knowledgebase/{知识库名称}/...
        return fullPath.startsWith("/.knowledgebase/") && 
               fullPath.length() > "/.knowledgebase/".length();
    }

    /**
     * 处理非知识库目录下的文件重命名
     *
     * @param fileRels 文件关联关系列表
     * @throws ApiException 如果文件有知识库关联
     */
    private void handleNonKbDirectoryFile(List<KbFileRel> fileRels) {
        if (!fileRels.isEmpty()) {
            // 非知识库目录下的文件如果有关联关系，需要报错
            List<String> kbNames = new ArrayList<>();
            for (KbFileRel rel : fileRels) {
                KnowledgeBase kb = knowledgeBaseMapper.selectById(rel.getKbId());
                if (kb != null) {
                    kbNames.add(kb.getName());
                }
            }
            throw new ApiException("文件存在知识库关联，请解绑后再修改。关联的知识库：" + String.join(", ", kbNames));
        }
        // 如果没有关联关系，则只需要检查目录重名（已在上层checkDuplicateName中处理）
    }

    /**
     * 处理知识库目录下的文件重命名
     *
     * @param fileRels 文件关联关系列表
     * @param newName 新名称
     * @throws ApiException 如果检查不通过
     */
    private void handleKbDirectoryFile(List<KbFileRel> fileRels, String newName) {
        if (fileRels.isEmpty()) {
            // 知识库目录下的文件应该有关联关系，如果没有则异常
            throw new ApiException("知识库目录下的文件缺少关联关系，请联系管理员");
        }
        
        if (fileRels.size() > 1) {
            // 知识库目录下的文件如果有多个关联关系，需要报错
            List<String> kbNames = new ArrayList<>();
            for (KbFileRel rel : fileRels) {
                KnowledgeBase kb = knowledgeBaseMapper.selectById(rel.getKbId());
                if (kb != null) {
                    kbNames.add(kb.getName());
                }
            }
            throw new ApiException("文件存在多个知识库关联，请解绑后再修改。关联的知识库：" + String.join(", ", kbNames));
        }
        
        // 只有一个关联关系，检查ragflow端状态和名称冲突
        checkFileProcessingStatus(fileRels);
        checkRagflowNameConflict(fileRels, newName);
    }

    /**
     * 检查文件处理状态
     * 注意：此方法现在只用于知识库目录下单一关联关系的文件重命名检查
     *
     * @param fileRels 文件关联关系列表（应该只有一个元素）
     * @throws ApiException 如果文件正在解析中
     */
    private void checkFileProcessingStatus(List<KbFileRel> fileRels) {
        if (fileRels.size() != 1) {
            throw new ApiException("内部错误：文件处理状态检查只支持单一关联关系的文件");
        }
        
        KbFileRel rel = fileRels.get(0);
        
        if (Objects.equals(Constants.DocumentStatus.PROCESSING, rel.getDocumentStatus())) {
            // 获取知识库名称用于错误提示
            KnowledgeBase kb = knowledgeBaseMapper.selectById(rel.getKbId());
            String kbName = kb != null ? kb.getName() : "未知知识库";
            throw new ApiException("文件正在解析中，无法重命名。涉及知识库：" + kbName);
        }
    }

    /**
     * 检查新名称在ragflow端是否已存在
     * 注意：此方法现在只用于知识库目录下单一关联关系的文件重命名检查
     *
     * @param fileRels 文件关联关系列表（应该只有一个元素）
     * @param newName 新名称
     * @throws ApiException 如果名称在ragflow端已存在
     */
    private void checkRagflowNameConflict(List<KbFileRel> fileRels, String newName) {
        if (fileRels.size() != 1) {
            throw new ApiException("内部错误：ragflow名称冲突检查只支持单一关联关系的文件");
        }
        
        KbFileRel rel = fileRels.get(0);
        
        // 获取知识库信息
        KnowledgeBase kb = knowledgeBaseMapper.selectById(rel.getKbId());
        if (kb == null) {
            throw new ApiException("关联的知识库不存在");
        }
        
        // 检查该知识库中是否有重名文档
        if (checkNameExistsInRagflow(kb, newName, fileRels)) {
            throw new ApiException("新名称 '" + newName + "' 在知识库 '" + kb.getName() + "' 的ragflow端已存在，请选择其他名称");
        }
    }

    /**
     * 检查名称在ragflow知识库中是否已存在
     *
     * @param kb 知识库信息
     * @param newName 新名称
     * @param currentFileRels 当前文件的关联关系（用于排除自身）
     * @return true如果存在同名文档，false如果不存在
     */
    private boolean checkNameExistsInRagflow(KnowledgeBase kb, String newName, List<KbFileRel> currentFileRels) {
        try {
            // 获取当前文件在该知识库中的documentId列表，用于排除自身
            List<String> excludeDocIds = currentFileRels.stream()
                    .map(KbFileRel::getDocumentId)
                    .filter(Objects::nonNull)
                    .toList();
            
            // 查询ragflow中该知识库的所有文档
            Map<String, String> params = new HashMap<>();
            params.put("name", newName); // ragflow支持按名称搜索
            
            String result = ragClient.listDocuments(kb.getDatasetId(), params);
            RagflowUtils.handleResponse(result, "查询ragflow文档失败");
            
            ObjectMapper mapper = new ObjectMapper();
            RagflowDocsListVO response = mapper.readValue(result, RagflowDocsListVO.class);
            
            if (response.getCode() == 0 && response.getData() != null) {
                List<RagflowDocsListVO.UploadFileVO> docs = response.getData().getDocs();
                
                // 检查是否有同名文档（排除当前文件自身）
                return docs.stream()
                        .anyMatch(doc -> newName.equals(doc.getName()) && 
                                !excludeDocIds.contains(doc.getId()));
            }
        } catch (Exception e) {
            log.warn("检查ragflow名称冲突时发生异常，知识库: {}, 名称: {}", kb.getName(), newName, e);
            // 发生异常时为了安全起见，假设不存在冲突，允许重命名
        }
        
        return false;
    }
    
    @Override
    public List<DocumentInfoVO> getAllDocumentsByKbId(Long kbId, Long userId) {
        if (kbId == null) {
            return new ArrayList<>();
        }
        
        // 使用原生MyBatis mapper查询文档信息
        List<Map<String, Object>> results = baseMapper.selectDocumentsByKbId(kbId);
        
        // 转换为DocumentInfoVO列表
        return results.stream()
                .map(map -> new DocumentInfoVO(
                    (String) map.get("document_id"),
                    (String) map.get("name"),
                    (String) map.get("full_path")
                ))
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void batchDeleteKbFiles(Long kbId, List<Long> fileIds, Long userId) {
        if (kbId == null) {
            throw new ApiException("知识库ID不能为空");
        }
        if (fileIds == null || fileIds.isEmpty()) {
            throw new ApiException("文件ID列表不能为空");
        }

        // 获取知识库信息
        KnowledgeBase kb = knowledgeBaseMapper.selectById(kbId);
        if (kb == null) {
            throw new ApiException("知识库不存在");
        }

        // 1. 批量查询所有关联关系
        LambdaQueryWrapper<KbFileRel> relWrapper = new LambdaQueryWrapper<>();
        relWrapper.eq(KbFileRel::getKbId, kbId)
                  .in(KbFileRel::getFileNodeId, fileIds);
        List<KbFileRel> allRels = kbFileRelMapper.selectList(relWrapper);

        // 2. 验证所有文件都有关联关系
        Set<Long> foundFileIds = allRels.stream()
                .map(KbFileRel::getFileNodeId)
                .collect(Collectors.toSet());
        List<Long> missingFileIds = fileIds.stream()
                .filter(id -> !foundFileIds.contains(id))
                .toList();
        if (!missingFileIds.isEmpty()) {
            throw new ApiException("部分文件未关联到该知识库: " + missingFileIds);
        }

        // 3. 获取所有文件信息
        List<FileNode> files = listByIds(fileIds);
        Map<Long, FileNode> fileMap = files.stream()
                .collect(Collectors.toMap(FileNode::getId, file -> file));

        // 4. 分类处理：直接上传的文件和手动关联的文件
        List<KbFileRel> directUploadRels = new ArrayList<>();
        List<KbFileRel> manualBindRels = new ArrayList<>();
        List<String> ragflowDocumentIds = new ArrayList<>();

        for (KbFileRel rel : allRels) {
            if (rel.getRelType() == 1) { // 直接上传
                directUploadRels.add(rel);
            } else { // 手动关联
                manualBindRels.add(rel);
            }
            
            // 收集需要从Ragflow删除的文档ID
            if (rel.getDocumentId() != null) {
                ragflowDocumentIds.add(rel.getDocumentId());
            }
        }

        List<String> deletedFileNames = new ArrayList<>();
        List<String> failedFiles = new ArrayList<>();

        try {
            // 5. 删除直接上传的文件（需要删除MinIO文件和文件节点）
            for (KbFileRel rel : directUploadRels) {
                FileNode file = fileMap.get(rel.getFileNodeId());
                if (file != null) {
                    try {
                        if (!StringUtils.hasText(file.getStoragePath())){
                            log.warn("文件存储路径为空，跳过MinIO删除，文件ID: {}", file.getId());
                            continue;
                        }
                        // 从MinIO删除文件
                        minioClient.removeObject(
                            RemoveObjectArgs.builder()
                                .bucket(bucketName)
                                .object(file.getStoragePath())
                                .build()
                        );
                        
                        // 删除文件节点
                        removeById(file.getId());
                        deletedFileNames.add(file.getName());
                    } catch (Exception e) {
                        failedFiles.add(file.getName() + " (删除文件失败: " + e.getMessage() + ")");
                        log.error("删除文件失败，文件ID: {}", file.getId(), e);
                    }
                }
            }

            // 6. 记录手动关联文件的名称（只删除关联关系，不删除文件本身）
            for (KbFileRel rel : manualBindRels) {
                FileNode file = fileMap.get(rel.getFileNodeId());
                if (file != null) {
                    deletedFileNames.add(file.getName() + "(仅解绑)");
                }
            }

            // 7. 删除所有关联关系
            if (!allRels.isEmpty()) {
                List<Long> relIds = allRels.stream()
                        .map(KbFileRel::getId)
                        .toList();
                kbFileRelMapper.deleteBatchIds(relIds);
            }

            // 8. 批量删除Ragflow文档
            processDelFileToRagflow(kbId, ragflowDocumentIds, kb, failedFiles);

        } catch (Exception e) {
            log.error("批量删除文件过程中发生异常，知识库ID: {}, 文件ID列表: {}", kbId, fileIds, e);
            throw new ApiException("批量删除失败: " + e.getMessage());
        }

        // 9. 处理结果
        if (!failedFiles.isEmpty()) {
            String successInfo = deletedFileNames.isEmpty() ? "" : 
                "成功删除 " + deletedFileNames.size() + " 个文件。";
            String failureInfo = "失败的操作：" + String.join(", ", failedFiles);
            throw new ApiException(successInfo + failureInfo);
        }
        
        log.info("批量删除知识库文件成功，知识库ID: {}, 删除文件数: {}, 文件名: {}, Ragflow文档数: {}", 
                kbId, deletedFileNames.size(), String.join(", ", deletedFileNames), ragflowDocumentIds.size());
    }

    private void processDelFileToRagflow(Long kbId, List<String> ragflowDocumentIds, KnowledgeBase kb, List<String> failedFiles) {
        if (!ragflowDocumentIds.isEmpty()) {
            try {
                // 先检查哪些文档在ragflow中存在
                List<String> existingDocumentIds = new ArrayList<>();
                List<String> nonExistingDocumentIds = new ArrayList<>();

                for (String docId : ragflowDocumentIds) {
                    if (checkDocumentExistsInRagflow(kb.getDatasetId(), docId)) {
                        existingDocumentIds.add(docId);
                    } else {
                        nonExistingDocumentIds.add(docId);
                    }
                }

                // 记录不存在的文档
                if (!nonExistingDocumentIds.isEmpty()) {
                    log.warn("以下文档在Ragflow中不存在，跳过删除: {}", nonExistingDocumentIds);
                }

                // 只删除存在的文档
                if (!existingDocumentIds.isEmpty()) {
                    ragClient.deleteDocuments(kb.getDatasetId(), existingDocumentIds);
                    log.info("成功从Ragflow删除 {} 个文档，跳过 {} 个不存在的文档",
                            existingDocumentIds.size(), nonExistingDocumentIds.size());
                } else {
                    log.info("所有文档在Ragflow中都不存在，跳过删除操作");
                }
            } catch (Exception e) {
                log.error("从Ragflow批量删除文档失败，知识库ID: {}, 文档ID列表: {}", kbId, ragflowDocumentIds, e);
                // 注意：这里不抛出异常，因为本地数据已经删除成功，Ragflow删除失败不应该回滚整个操作
                failedFiles.add("Ragflow删除失败: " + e.getMessage());
            }
        }
    }

    @Override
    public void getDocumentImage(String imageId, HttpServletResponse response) throws IOException {
        try {
            String ragflowUrl = ragflowStaticUrl + "/document/image/" + imageId;
            
            // 设置请求头
            org.springframework.http.HttpHeaders headers = new org.springframework.http.HttpHeaders();
            headers.set("Accept", "image/*");
            headers.set("User-Agent", "Mozilla/5.0");
            
            org.springframework.http.HttpEntity<String> requestEntity = new org.springframework.http.HttpEntity<>(headers);
            
            // 使用exchange方法发送GET请求
            ResponseEntity<byte[]> ragflowResponse = restTemplate.exchange(
                ragflowUrl,
                org.springframework.http.HttpMethod.GET,
                requestEntity,
                byte[].class
            );
            
            if (ragflowResponse.getStatusCode().is2xxSuccessful() && ragflowResponse.getBody() != null) {
                // 复制所有响应头
                ragflowResponse.getHeaders().forEach((key, value) -> {
                    if (value != null && !value.isEmpty()) {
                        response.setHeader(key, value.get(0));
                    }
                });
                
                // 写入响应体
                try (OutputStream out = response.getOutputStream()) {
                    out.write(ragflowResponse.getBody());
                    out.flush();
                }
            } else {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
            }
        } catch (Exception e) {
            log.error("获取文档图片失败: {}", e.getMessage(), e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 验证解析文件的前置条件并准备数据
     */
    @Override
    public ParseFilePreparationResult prepareParseFiles(Long kbId, List<Long> fileIds, Long userId) {
        try {
            // 1. 参数校验
            if (kbId == null) {
                throw new ApiException("知识库ID不能为空");
            }
            if (fileIds == null || fileIds.isEmpty()) {
                throw new ApiException("文件ID列表不能为空");
            }

            // 2. 获取知识库信息
            KnowledgeBase kb = knowledgeBaseMapper.selectById(kbId);
            if (kb == null) {
                throw new ApiException("知识库不存在");
            }

            // 3. 获取文件信息
            List<FileNode> files = listByIds(fileIds);
            if (files.isEmpty()) {
                throw new ApiException("未找到指定的文件");
            }

            // 4. 验证文件与知识库的关联关系
            List<KbFileRel> rels = new ArrayList<>();
            for (FileNode file : files) {
                LambdaQueryWrapper<KbFileRel> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(KbFileRel::getFileNodeId, file.getId())
                        .eq(KbFileRel::getKbId, kbId);
                KbFileRel rel = kbFileRelMapper.selectOne(wrapper);

                if (rel == null) {
                    throw new ApiException("文件未关联到指定知识库：" + file.getName());
                }

                rels.add(rel);
            }

            // 5. 收集需要解析的文档ID
            List<String> documentIds = rels.stream()
                    .map(KbFileRel::getDocumentId)
                    .filter(id -> id != null)
                    .toList();

            if (documentIds.isEmpty()) {
                throw new ApiException("没有可解析的文件");
            }

            return new ParseFilePreparationResult(kb.getDatasetId(), documentIds, kb.getName());

        } catch (Exception e) {
            log.error("准备解析文件失败，知识库ID: {}, 文件ID列表: {}", kbId, fileIds, e);
            throw new ApiException("准备解析文件失败: " + e.getMessage());
        }
    }

    /**
     * 完成解析文件的后续处理
     */
    @Override
    public void completeParseFiles(Long kbId, List<Long> fileIds, Long userId, boolean success) {
        try {
            if (success) {
                // 更新文件状态为处理中
                List<KbFileRel> rels = new ArrayList<>();
                for (Long fileId : fileIds) {
                    LambdaQueryWrapper<KbFileRel> wrapper = new LambdaQueryWrapper<>();
                    wrapper.eq(KbFileRel::getFileNodeId, fileId)
                            .eq(KbFileRel::getKbId, kbId);
                    KbFileRel rel = kbFileRelMapper.selectOne(wrapper);
                    if (rel != null && rel.getDocumentId() != null) {
                        rel.setDocumentStatus(Constants.DocumentStatus.PROCESSING);
                        rels.add(rel);
                    }
                }

                // 批量更新状态
                for (KbFileRel rel : rels) {
                    kbFileRelMapper.updateById(rel);
                }

                log.info("解析文件任务完成，已更新 {} 个文件状态", rels.size());
            } else {
                log.warn("解析文件任务失败，知识库ID: {}, 文件ID列表: {}", kbId, fileIds);
            }
        } catch (Exception e) {
            log.error("完成解析文件处理失败", e);
        }
    }

    /**
     * 验证停止解析文件的前置条件并准备数据
     */
    @Override
    public StopParsingPreparationResult prepareStopParsingFiles(Long kbId, List<Long> fileIds, Long userId) {
        try {
            // 1. 参数校验
            if (kbId == null) {
                throw new ApiException("知识库ID不能为空");
            }
            if (fileIds == null || fileIds.isEmpty()) {
                throw new ApiException("文件ID列表不能为空");
            }

            // 2. 获取知识库信息
            KnowledgeBase kb = knowledgeBaseMapper.selectById(kbId);
            if (kb == null) {
                throw new ApiException("知识库不存在");
            }

            // 3. 获取文件信息
            List<FileNode> files = listByIds(fileIds);
            if (files.isEmpty()) {
                throw new ApiException("未找到指定的文件");
            }

            // 4. 验证文件与知识库的关联关系
            List<KbFileRel> rels = new ArrayList<>();
            for (FileNode file : files) {
                LambdaQueryWrapper<KbFileRel> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(KbFileRel::getFileNodeId, file.getId())
                        .eq(KbFileRel::getKbId, kbId);
                KbFileRel rel = kbFileRelMapper.selectOne(wrapper);

                if (rel == null) {
                    throw new ApiException("文件未关联到指定知识库：" + file.getName());
                }

                rels.add(rel);
            }

            // 5. 收集需要停止解析的文档ID
            List<String> documentIds = rels.stream()
                    .map(KbFileRel::getDocumentId)
                    .filter(id -> id != null)
                    .toList();

            if (documentIds.isEmpty()) {
                throw new ApiException("没有可终止解析的文件");
            }

            return new StopParsingPreparationResult(kb.getDatasetId(), documentIds, kb.getName());

        } catch (Exception e) {
            log.error("准备停止解析文件失败，知识库ID: {}, 文件ID列表: {}", kbId, fileIds, e);
            throw new ApiException("准备停止解析文件失败: " + e.getMessage());
        }
    }

    /**
     * 完成停止解析文件的后续处理
     */
    @Override
    public void completeStopParsingFiles(Long kbId, List<Long> fileIds, Long userId, boolean success) {
        try {
            if (success) {
                // 更新文件状态为已取消
                List<KbFileRel> rels = new ArrayList<>();
                for (Long fileId : fileIds) {
                    LambdaQueryWrapper<KbFileRel> wrapper = new LambdaQueryWrapper<>();
                    wrapper.eq(KbFileRel::getFileNodeId, fileId)
                            .eq(KbFileRel::getKbId, kbId);
                    KbFileRel rel = kbFileRelMapper.selectOne(wrapper);
                    if (rel != null && rel.getDocumentId() != null) {
                        rel.setDocumentStatus(Constants.DocumentStatus.CANCELED);
                        rels.add(rel);
                    }
                }

                // 批量更新状态
                for (KbFileRel rel : rels) {
                    kbFileRelMapper.updateById(rel);
                }

                log.info("停止解析文件任务完成，已更新 {} 个文件状态", rels.size());
            } else {
                log.warn("停止解析文件任务失败，知识库ID: {}, 文件ID列表: {}", kbId, fileIds);
            }
        } catch (Exception e) {
            log.error("完成停止解析文件处理失败", e);
        }
    }

    /**
     * 验证更新文档的前置条件并准备数据
     */
    @Override
    public UpdateDocumentPreparationResult prepareUpdateDocument(Long kbId, Long fileId, RagflowUploadVO.UploadFileVO updateVO, Long userId) {
        try {
            // 1. 参数校验
            if (kbId == null || fileId == null || updateVO == null || userId == null) {
                throw new ApiException("参数不能为空");
            }

            // 2. 查询文件和知识库信息
            FileNode fileNode = getById(fileId);
            if (fileNode == null) {
                throw new ApiException("文件不存在");
            }

            KnowledgeBase kb = knowledgeBaseMapper.selectById(kbId);
            if (kb == null) {
                throw new ApiException("知识库不存在");
            }

            // 3. 验证文件是否属于该知识库并获取关联关系
            LambdaQueryWrapper<KbFileRel> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(KbFileRel::getKbId, kbId)
                    .eq(KbFileRel::getFileNodeId, fileId);
            KbFileRel rel = kbFileRelMapper.selectOne(wrapper);
            if (rel == null) {
                throw new ApiException("文件不属于该知识库");
            }

            // 4. 验证文件是否已上传到Ragflow
            if (rel.getDocumentId() == null) {
                throw new ApiException("文件尚未上传到Ragflow");
            }

            // 5. 验证是否有需要更新的字段
            if (updateVO.getName() == null &&
                    updateVO.getMeta_fields() == null &&
                    updateVO.getChunk_method() == null) {
                throw new ApiException("没有需要更新的字段");
            }

            // 6. 构建更新字段
            Map<String, Object> updateFields = new HashMap<>();
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(updateVO.getName())) {
                updateFields.put("name", updateVO.getName());
            }
            if (updateVO.getMeta_fields() != null && !updateVO.getMeta_fields().isEmpty()) {
                updateFields.put("meta_fields", updateVO.getMeta_fields());
            }
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(updateVO.getChunk_method())) {
                updateFields.put("chunk_method", updateVO.getChunk_method());
                updateFields.put("parser_config", updateVO.getParser_config());
            }

            return new UpdateDocumentPreparationResult(kb.getDatasetId(), rel.getDocumentId(), fileNode.getName(), updateFields);

        } catch (Exception e) {
            log.error("准备更新文档失败，知识库ID: {}, 文件ID: {}", kbId, fileId, e);
            throw new ApiException("准备更新文档失败: " + e.getMessage());
        }
    }

    /**
     * 完成更新文档的后续处理
     */
    @Override
    public void completeUpdateDocument(Long kbId, Long fileId, RagflowUploadVO.UploadFileVO updateVO, Long userId, boolean success) {
        try {
            if (success) {
                // 更新本地文件节点信息
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(updateVO.getName())) {
                    FileNode fileNode = getById(fileId);
                    if (fileNode != null && !updateVO.getName().equals(fileNode.getName())) {
                        // 检查新名称在目标目录是否已存在
                        checkDuplicateName(fileNode.getParentId(), updateVO.getName(), fileNode.getId());

                        fileNode.setName(updateVO.getName());
                        fileNode.setFullPath(buildFullPath(fileNode.getParentId(), updateVO.getName()));
                        updateById(fileNode);

                        log.info("更新文档任务完成，已更新本地文件名，fileId: {}", fileId);
                    }
                }
            } else {
                log.warn("更新文档任务失败，知识库ID: {}, 文件ID: {}", kbId, fileId);
            }
        } catch (Exception e) {
            log.error("完成更新文档处理失败", e);
        }
    }

    /**
     * 验证批量更新文档状态的前置条件并准备数据
     */
    @Override
    public BatchUpdateStatusPreparationResult prepareBatchUpdateDocumentStatus(Long kbId, List<Long> fileIds, String status, Long userId) {
        try {
            // 1. 参数校验
            if (kbId == null || fileIds == null || fileIds.isEmpty() || status == null || userId == null) {
                throw new ApiException("参数不能为空");
            }

            // 2. 查询知识库信息
            KnowledgeBase kb = knowledgeBaseMapper.selectById(kbId);
            if (kb == null) {
                throw new ApiException("知识库不存在");
            }

            // 3. 批量查询文件关联关系
            LambdaQueryWrapper<KbFileRel> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(KbFileRel::getKbId, kbId)
                    .in(KbFileRel::getFileNodeId, fileIds);
            List<KbFileRel> rels = kbFileRelMapper.selectList(wrapper);

            if (rels.isEmpty()) {
                throw new ApiException("没有找到属于该知识库的文件");
            }

            // 4. 过滤出已上传到Ragflow的文件
            List<String> documentIds = rels.stream()
                    .filter(rel -> rel.getDocumentId() != null)
                    .map(KbFileRel::getDocumentId)
                    .collect(Collectors.toList());

            if (documentIds.isEmpty()) {
                throw new ApiException("没有找到已上传到Ragflow的文件");
            }

            return new BatchUpdateStatusPreparationResult(kb.getDatasetId(), documentIds, status, kb.getName());

        } catch (Exception e) {
            log.error("准备批量更新文档状态失败，知识库ID: {}, 文件ID列表: {}", kbId, fileIds, e);
            throw new ApiException("准备批量更新文档状态失败: " + e.getMessage());
        }
    }

    /**
     * 完成批量更新文档状态的后续处理
     */
    @Override
    public void completeBatchUpdateDocumentStatus(Long kbId, List<Long> fileIds, String status, Long userId, boolean success) {
        try {
            if (success) {
                // 同步更新本地document_info中的状态
                LambdaQueryWrapper<KbFileRel> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(KbFileRel::getKbId, kbId)
                        .in(KbFileRel::getFileNodeId, fileIds);
                List<KbFileRel> rels = kbFileRelMapper.selectList(wrapper);

                List<KbFileRel> validRels = rels.stream()
                        .filter(rel -> rel.getDocumentId() != null)
                        .collect(Collectors.toList());

                for (KbFileRel rel : validRels) {
                    try {
                        updateDocumentStatusInfo(rel, status);
                    } catch (Exception e) {
                        log.error("更新本地文档状态信息失败，documentId: {}, error: {}", rel.getDocumentId(), e.getMessage());
                    }
                }

                log.info("批量更新文档状态任务完成，已更新 {} 个文档的本地状态", validRels.size());
            } else {
                log.warn("批量更新文档状态任务失败，知识库ID: {}, 文件ID列表: {}", kbId, fileIds);
            }
        } catch (Exception e) {
            log.error("完成批量更新文档状态处理失败", e);
        }
    }

}