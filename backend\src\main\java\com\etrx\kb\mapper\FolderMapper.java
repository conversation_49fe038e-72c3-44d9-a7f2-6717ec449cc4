package com.etrx.kb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.etrx.kb.domain.Folder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 文件夹Mapper接口
 */
@Mapper
public interface FolderMapper extends BaseMapper<Folder> {

    /**
     * 获取知识库的根文件夹列表
     *
     * @param kbId 知识库ID
     * @return 根文件夹列表
     */
    @Select("SELECT * FROM tb_folder WHERE kb_id = #{kbId} AND parent_id IS NULL AND deleted = 0")
    List<Folder> findRootFoldersByKbId(@Param("kbId") Long kbId);

    /**
     * 获取父文件夹下的子文件夹列表
     *
     * @param parentId 父文件夹ID
     * @return 子文件夹列表
     */
    @Select("SELECT * FROM tb_folder WHERE parent_id = #{parentId} AND deleted = 0")
    List<Folder> findByParentId(@Param("parentId") Long parentId);

    /**
     * 获取知识库的所有文件夹
     *
     * @param kbId 知识库ID
     * @return 文件夹列表
     */
    @Select("SELECT * FROM tb_folder WHERE kb_id = #{kbId} AND deleted = 0")
    List<Folder> findByKbId(@Param("kbId") Long kbId);
}