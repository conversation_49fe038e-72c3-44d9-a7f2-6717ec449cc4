-- 文件节点表（统一管理文件和文件夹）
CREATE TABLE IF NOT EXISTS `tb_file_node` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(255) NOT NULL COMMENT '节点名称',
  `node_type` tinyint NOT NULL COMMENT '节点类型(1:文件夹,2:文件)',
  `parent_id` bigint DEFAULT NULL COMMENT '父节点ID',
  `full_path` varchar(1000) NOT NULL COMMENT '完整路径',
  `file_type` varchar(50) DEFAULT NULL COMMENT '文件类型(node_type为2时有效)',
  `file_size` bigint DEFAULT NULL COMMENT '文件大小(字节,node_type为2时有效)',
  `storage_path` varchar(500) DEFAULT NULL COMMENT '存储路径(node_type为2时有效)',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(0:处理中,1:正常,2:异常)',
  `embed_status` tinyint DEFAULT NULL COMMENT '嵌入状态(0:未嵌入,1:嵌入中,2:已嵌入,3:失败)',
  `creator_id` bigint NOT NULL COMMENT '创建者ID',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除(0:未删除,1:已删除)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_creator_id` (`creator_id`),
  KEY `idx_full_path` (`full_path`(768))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='文件节点表';

-- 知识库文件关联表
CREATE TABLE IF NOT EXISTS `tb_kb_file_rel` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `kb_id` bigint NOT NULL COMMENT '知识库ID',
  `file_node_id` bigint NOT NULL COMMENT '文件节点ID',
  `rel_type` tinyint NOT NULL DEFAULT '1' COMMENT '关联类型(1:直接上传,2:手动关联)',
  `document_id` varchar(255) DEFAULT NULL COMMENT '文档ID',
  `document_status` tinyint DEFAULT NULL COMMENT '文档状态',
  `document_info` text DEFAULT NULL COMMENT '文档信息',
  `creator_id` bigint NOT NULL COMMENT '创建者ID',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除(0:未删除,1:已删除)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_kb_file` (`kb_id`,`file_node_id`),
  KEY `idx_file_node_id` (`file_node_id`),
  KEY `idx_document_id` (`document_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='知识库文件关联表';

-- 文件标签表
CREATE TABLE IF NOT EXISTS `tb_file_tag` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(50) NOT NULL COMMENT '标签名称',
  `creator_id` bigint NOT NULL COMMENT '创建者ID',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除(0:未删除,1:已删除)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='文件标签表';

-- 文件标签关联表
CREATE TABLE IF NOT EXISTS `tb_file_tag_rel` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `file_node_id` bigint NOT NULL COMMENT '文件节点ID',
  `tag_id` bigint NOT NULL COMMENT '标签ID',
  `creator_id` bigint NOT NULL COMMENT '创建者ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_file_tag` (`file_node_id`,`tag_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='文件标签关联表';

-- 外键约束
ALTER TABLE `tb_file_node` ADD CONSTRAINT `fk_file_node_parent` FOREIGN KEY (`parent_id`) REFERENCES `tb_file_node` (`id`);
ALTER TABLE `tb_file_node` ADD CONSTRAINT `fk_file_node_creator` FOREIGN KEY (`creator_id`) REFERENCES `tb_user` (`id`);

ALTER TABLE `tb_kb_file_rel` ADD CONSTRAINT `fk_kb_file_rel_kb` FOREIGN KEY (`kb_id`) REFERENCES `tb_knowledge_base` (`id`);
ALTER TABLE `tb_kb_file_rel` ADD CONSTRAINT `fk_kb_file_rel_file` FOREIGN KEY (`file_node_id`) REFERENCES `tb_file_node` (`id`);
ALTER TABLE `tb_kb_file_rel` ADD CONSTRAINT `fk_kb_file_rel_creator` FOREIGN KEY (`creator_id`) REFERENCES `tb_user` (`id`);

ALTER TABLE `tb_file_tag` ADD CONSTRAINT `fk_file_tag_creator` FOREIGN KEY (`creator_id`) REFERENCES `tb_user` (`id`);

ALTER TABLE `tb_file_tag_rel` ADD CONSTRAINT `fk_file_tag_rel_file` FOREIGN KEY (`file_node_id`) REFERENCES `tb_file_node` (`id`);
ALTER TABLE `tb_file_tag_rel` ADD CONSTRAINT `fk_file_tag_rel_tag` FOREIGN KEY (`tag_id`) REFERENCES `tb_file_tag` (`id`);
ALTER TABLE `tb_file_tag_rel` ADD CONSTRAINT `fk_file_tag_rel_creator` FOREIGN KEY (`creator_id`) REFERENCES `tb_user` (`id`); 