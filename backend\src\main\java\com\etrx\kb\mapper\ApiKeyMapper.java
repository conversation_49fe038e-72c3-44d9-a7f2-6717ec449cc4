package com.etrx.kb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.etrx.kb.domain.ApiKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * API密钥Mapper接口
 */
@Mapper
public interface ApiKeyMapper extends BaseMapper<ApiKey> {

    /**
     * 根据API密钥查询
     *
     * @param apiKey API密钥
     * @return API密钥实体
     */
    @Select("SELECT * FROM tb_api_key WHERE api_key = #{apiKey} AND status = 1 AND deleted = 0 LIMIT 1")
    ApiKey findByApiKey(@Param("apiKey") String apiKey);
}