package com.etrx.kb.service;

import com.etrx.kb.common.PageResult;
import com.etrx.kb.dto.ChatAssistantMappingDTO;
import com.etrx.kb.dto.ChatRequestDTO;
import com.etrx.kb.dto.ChatResponseDTO;
import com.etrx.kb.dto.ChatHistoryResponseDTO;
import com.etrx.kb.dto.ChatSessionDTO;
import com.etrx.kb.dto.CreateChatAssistantRequestDTO;
import com.etrx.kb.dto.ConversationCompletionRequestDTO;

import java.util.List;
import java.util.function.Consumer;

/**
 * 聊天服务接口
 */
public interface ChatService {

    /**
     * 发送聊天消息
     *
     * @param request 聊天请求
     * @param userId  用户ID
     * @return 聊天响应
     */
    ChatResponseDTO chat(ChatRequestDTO request, Long userId);

    /**
     * 流式聊天
     *
     * @param request 聊天请求
     * @param userId  用户ID
     * @return 流式响应
     */
    List<ChatResponseDTO> streamChat(ChatRequestDTO request, Long userId);

    /**
     * 流式聊天（回调方式）
     *
     * @param request 聊天请求
     * @param userId  用户ID
     * @param responseCallback 响应回调处理器
     */
    void streamChatWithCallback(ChatRequestDTO request, Long userId, Consumer<ChatResponseDTO> responseCallback);

    /**
     * 新增/修改聊天助手（统一格式）
     * 支持RAGflow前端格式和原生格式
     * 根据dialogId是否存在来判断是新增还是修改操作
     *
     * @param request 新增/修改助手请求
     * @param userId  用户ID
     * @return 聊天助手ID
     */
    String createOrUpdateChatAssistant(CreateChatAssistantRequestDTO request, Long userId);

    /**
     * 创建聊天会话
     *
     * @param chatAssistantId 聊天助手ID
     * @param sessionName     会话名称
     * @param userId          用户ID
     * @return 会话ID
     */
    String createChatSession(String chatAssistantId, String sessionName, Long userId);

    /**
     * 更新聊天会话
     *
     * @param sessionId   会话ID
     * @param sessionName 新的会话名称
     * @param userId      用户ID
     * @return 更新结果
     */
    String updateChatSession(String sessionId, String sessionName, Long userId);

    /**
     * 获取聊天历史记录
     *
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @return 聊天历史记录
     */
    List<ChatResponseDTO> getChatHistory(String sessionId, Long userId);

    /**
     * 获取标准格式的聊天历史记录
     *
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @return 标准格式的聊天历史记录
     */
    ChatHistoryResponseDTO getChatHistoryStandard(String sessionId, Long userId);

    /**
     * 根据聊天助手ID获取会话列表
     *
     * @param chatAssistantId 聊天助手ID
     * @param userId          用户ID
     * @return 会话列表
     */
    List<ChatSessionDTO> getChatSessionsByAssistantId(String chatAssistantId, Long userId);

    /**
     * 基于完整对话历史进行Conversation Completion（RAGflow原生格式）
     *
     * @param request 包含conversation_id和messages的请求
     * @param userId  用户ID
     * @return 流式响应列表
     */
    List<ChatResponseDTO> conversationCompletion(ConversationCompletionRequestDTO request, Long userId);

    /**
     * 删除聊天会话
     *
     * @param sessionId 会话ID
     * @param userId    用户ID
     */
    void deleteChatSession(String sessionId, Long userId);

    /**
     * 列出RAGflow数据集（用于调试权限问题）
     *
     * @return 数据集列表JSON字符串
     */
    String listRagflowDatasets();

    /**
     * 获取用户的聊天助手列表
     *
     * @param userId 用户ID
     * @return 聊天助手列表
     */
    List<ChatAssistantMappingDTO> getUserChatAssistants(Long userId);

    /**
     * 分页获取用户的聊天助手列表
     *
     * @param userId   用户ID
     * @param pageNum  页码
     * @param pageSize 页大小
     * @return 分页结果
     */
    PageResult<ChatAssistantMappingDTO> getUserChatAssistantsPage(Long userId, Integer pageNum, Integer pageSize);

    /**
     * 通过会话ID获取聊天助手ID
     *
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @return 聊天助手ID
     */
    String getChatAssistantIdBySessionId(String sessionId, Long userId);

    /**
     * 删除聊天助理
     *
     * @param assistantId 助理ID（RAGflow助理ID）
     * @param userId      用户ID
     */
    void deleteChatAssistant(String assistantId, Long userId);

    /**
     * 获取助手详情
     *
     * @param assistantId 助手ID
     * @param userId      用户ID
     * @return 助手详情JSON字符串
     */
    String getChatAssistantDetails(String assistantId, Long userId);

    /**
     * 获取当前用户的默认聊天助手
     * 如果没有默认助手，则创建一个
     *
     * @param userId 用户ID
     * @return 默认聊天助手的映射信息
     */
    ChatAssistantMappingDTO getOrCreateDefaultChatAssistant(Long userId);
} 