package com.etrx.kb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.etrx.kb.common.Constants;
import com.etrx.kb.common.PageResult;
import com.etrx.kb.domain.User;
import com.etrx.kb.dto.LoginResponseDTO;
import com.etrx.kb.dto.UserInfoDTO;
import com.etrx.kb.dto.UserLoginDTO;
import com.etrx.kb.dto.UserRegisterDTO;
import com.etrx.kb.dto.ThirdPartyLoginDTO;
import com.etrx.kb.dto.ApiKeyDTO;
import com.etrx.kb.exception.ApiException;
import com.etrx.kb.mapper.UserMapper;
import com.etrx.kb.mapper.UserAppRelationMapper;
import com.etrx.kb.domain.UserAppRelation;
import com.etrx.kb.service.UserService;
import com.etrx.kb.service.ApiKeyService;
import com.etrx.kb.util.JwtTokenUtil;
import com.etrx.kb.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final UserMapper userMapper;
    private final UserAppRelationMapper userAppRelationMapper;
    private final PasswordEncoder passwordEncoder;
    private final JwtTokenUtil jwtTokenUtil;
    private final AuthenticationManager authenticationManager;
    private final SecurityUtils securityUtils;
    private final ApiKeyService apiKeyService;

    @Value("${jwt.expiration}")
    private Long expiration;

    @Value("${jwt.token-prefix}")
    private String tokenPrefix;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserInfoDTO register(UserRegisterDTO registerDTO) {
        // 验证用户名是否已存在
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getUsername, registerDTO.getUsername());
        if (userMapper.selectCount(queryWrapper) > 0) {
            throw new ApiException("用户名已存在");
        }

        // 验证邮箱是否已存在
        queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getEmail, registerDTO.getEmail());
        if (userMapper.selectCount(queryWrapper) > 0) {
            throw new ApiException("邮箱已存在");
        }

        // 验证工号是否已存在
        queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getEmployeeNo, registerDTO.getEmployeeNo());
        if (userMapper.selectCount(queryWrapper) > 0) {
            throw new ApiException("工号已存在");
        }

        // 验证两次密码是否一致
        if (!registerDTO.getPassword().equals(registerDTO.getConfirmPassword())) {
            throw new ApiException("两次密码不一致");
        }

        // 创建用户
        User user = new User();
        user.setUsername(registerDTO.getUsername());
        user.setPassword(passwordEncoder.encode(registerDTO.getPassword()));
        user.setNickname(registerDTO.getNickname());
        user.setEmployeeNo(registerDTO.getEmployeeNo());
        user.setEmail(registerDTO.getEmail());
        user.setPhone(registerDTO.getPhone());
        user.setRole(Constants.UserRole.USER);
        user.setStatus(Constants.UserStatus.ENABLED);
        // 设置注册来源为系统注册
        user.setRegisterSource(Constants.UserRegisterSource.SYSTEM);
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());

        userMapper.insert(user);

        // 返回用户信息
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        BeanUtils.copyProperties(user, userInfoDTO);
        return userInfoDTO;
    }

    @Override
    public LoginResponseDTO login(UserLoginDTO loginDTO) {
        // 认证
        Authentication authentication = null;
        try {
            authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(loginDTO.getUsername(), loginDTO.getPassword())
            );
        } catch (Exception e) {
            throw new ApiException(e.getMessage());
        }
        SecurityContextHolder.getContext().setAuthentication(authentication);

        // 生成JWT令牌
        UserDetails userDetails = (UserDetails) authentication.getPrincipal();
        String token = jwtTokenUtil.generateToken(userDetails);

        // 获取用户信息
        User user = getUserByUsername(userDetails.getUsername());
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        BeanUtils.copyProperties(user, userInfoDTO);

        // 构建登录响应
        LoginResponseDTO loginResponseDTO = new LoginResponseDTO();
        loginResponseDTO.setUserInfo(userInfoDTO);
        loginResponseDTO.setToken(token);
        loginResponseDTO.setTokenType(tokenPrefix);
        loginResponseDTO.setExpiresIn(expiration);

        return loginResponseDTO;
    }

    @Override
    public UserInfoDTO getCurrentUserInfo() {
        // 使用SecurityUtils获取当前用户（包含appName信息）
        User user = securityUtils.getCurrentUser();
        
        // 转换为DTO
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        BeanUtils.copyProperties(user, userInfoDTO);
        return userInfoDTO;
    }

    @Override
    public UserInfoDTO getUserInfo(Long id) {
        User user = userMapper.selectById(id);
        if (user == null) {
            throw new ApiException("用户不存在");
        }

        UserInfoDTO userInfoDTO = new UserInfoDTO();
        BeanUtils.copyProperties(user, userInfoDTO);
        return userInfoDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserInfoDTO updateUserInfo(Long id, UserInfoDTO userDTO) {
        User user = userMapper.selectById(id);
        if (user == null) {
            throw new ApiException("用户不存在");
        }

        return updateUserInfo(user, userDTO);
    }

    /**
     * 更新用户信息的核心逻辑（重载方法，避免重复查询）
     */
    private UserInfoDTO updateUserInfo(User user, UserInfoDTO userDTO) {
        // 更新用户信息
        if (StringUtils.hasText(userDTO.getNickname())) {
            user.setNickname(userDTO.getNickname());
        }
        if (StringUtils.hasText(userDTO.getEmail())) {
            // 验证邮箱是否已存在
            LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(User::getEmail, userDTO.getEmail())
                    .ne(User::getId, user.getId());
            if (userMapper.selectCount(queryWrapper) > 0) {
                throw new ApiException("邮箱已存在");
            }
            user.setEmail(userDTO.getEmail());
        }
        if (StringUtils.hasText(userDTO.getPhone())) {
            user.setPhone(userDTO.getPhone());
        }
        if (StringUtils.hasText(userDTO.getAvatar())) {
            user.setAvatar(userDTO.getAvatar());
        }
        user.setUpdateTime(LocalDateTime.now());

        userMapper.updateById(user);

        // 返回更新后的用户信息
        UserInfoDTO updatedUserDTO = new UserInfoDTO();
        BeanUtils.copyProperties(user, updatedUserDTO);
        return updatedUserDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changePassword(Long id, String oldPassword, String newPassword) {
        User user = userMapper.selectById(id);
        if (user == null) {
            throw new ApiException("用户不存在");
        }

        User currentUser = securityUtils.getCurrentUser();
        
        // 如果是修改自己的密码，需要验证旧密码
        if (currentUser.getId().equals(id)) {
            if (oldPassword == null || !passwordEncoder.matches(oldPassword, user.getPassword())) {
                throw new ApiException("旧密码错误");
            }
        }
        // 如果是管理员或超级管理员修改其他人的密码，不需要验证旧密码
        // 权限检查已经在Controller层的@PreAuthorize中完成

        // 更新密码
        user.setPassword(passwordEncoder.encode(newPassword));
        user.setUpdateTime(LocalDateTime.now());
        userMapper.updateById(user);
    }

    @Override
    public PageResult<UserInfoDTO> listUsers(Integer pageNum, Integer pageSize, String keyword) {
        // 构建查询条件
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        if (keyword != null && !keyword.isEmpty()) {
            queryWrapper.like(User::getUsername, keyword)
                    .or()
                    .like(User::getNickname, keyword)
                    .or()
                    .like(User::getEmail, keyword);
        }

        // 分页查询
        IPage<User> page = userMapper.selectPage(new Page<>(pageNum, pageSize), queryWrapper);

        // 转换为DTO
        List<UserInfoDTO> userDTOList = page.getRecords().stream()
                .map(user -> {
                    UserInfoDTO userDTO = new UserInfoDTO();
                    BeanUtils.copyProperties(user, userDTO);
                    return userDTO;
                })
                .collect(Collectors.toList());

        // 构建分页结果
        PageResult<UserInfoDTO> result = new PageResult<>();
        result.setPageNum(page.getCurrent());
        result.setPageSize(page.getSize());
        result.setTotal(page.getTotal());
        result.setTotalPage(page.getPages());
        result.setList(userDTOList);

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(Long id, Integer status) {
        User user = userMapper.selectById(id);
        if (user == null) {
            throw new ApiException("用户不存在");
        }

        // 更新状态
        user.setStatus(status);
        user.setUpdateTime(LocalDateTime.now());
        userMapper.updateById(user);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRole(Long id, Integer role) {
        User user = userMapper.selectById(id);
        if (user == null) {
            throw new ApiException("用户不存在");
        }

        // 更新角色
        user.setRole(role);
        user.setUpdateTime(LocalDateTime.now());
        userMapper.updateById(user);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteUser(Long id) {
        User user = userMapper.selectById(id);
        if (user == null) {
            throw new ApiException("用户不存在");
        }

        // 删除用户
        userMapper.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserInfoDTO updateCurrentUserInfo(UserInfoDTO userDTO) {
        // 从SecurityContext获取当前用户名
        String username = SecurityContextHolder.getContext().getAuthentication().getName();
        
        // 查询用户信息
        User user = getUserByUsername(username);
        if (user == null) {
            throw new ApiException("用户不存在");
        }

        // 复用更新逻辑，避免重复查询和重复代码
        return updateUserInfo(user, userDTO);
    }

    /**
     * 根据用户名获取用户
     *
     * @param username 用户名
     * @return 用户
     */
    private User getUserByUsername(String username) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getUsername, username);
        return userMapper.selectOne(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LoginResponseDTO thirdPartyLogin(String apiKey, ThirdPartyLoginDTO.UserInfo userInfo) {
        // 1. 验证API密钥
        if (!apiKeyService.validateApiKey(apiKey)) {
            throw new ApiException("API密钥无效或已过期");
        }

        // 2. 根据工号查询用户是否存在
        User user = getUserByEmployeeNo(userInfo.getEmployeeNo());
        
        // 3. 获取应用信息（一次查询获取ID和名称）
        ApiKeyDTO appInfo = apiKeyService.getAppInfoByApiKey(apiKey);
        if (appInfo == null) {
            throw new ApiException("API密钥对应的应用不存在");
        }
        
        // 4. 如果用户不存在，则注册新用户
        if (user == null) {
            user = registerThirdPartyUser(userInfo, apiKey, appInfo.getAppName());
            // 记录新用户注册日志
            log.info("第三方用户注册成功: 工号=" + userInfo.getEmployeeNo() + ", 应用=" + appInfo.getAppName());
        } else {
            // 更新用户信息（如果需要）
            updateThirdPartyUserInfo(user, userInfo);
        }
        
        // 5. 记录用户应用关系
        recordUserAppRelation(user.getId(), appInfo.getId());

        // 6. 生成第三方token
        String token = jwtTokenUtil.generateThirdPartyToken(
            userInfo.getEmployeeNo(), 
            apiKey,
            appInfo.getAppName()
        );

        // 7. 构建登录响应
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        BeanUtils.copyProperties(user, userInfoDTO);
        // 设置运行时的应用名称信息
        userInfoDTO.setAppName(appInfo.getAppName());

        LoginResponseDTO loginResponseDTO = new LoginResponseDTO();
        loginResponseDTO.setUserInfo(userInfoDTO);
        loginResponseDTO.setToken(token);
        loginResponseDTO.setTokenType(tokenPrefix);
        loginResponseDTO.setExpiresIn(expiration);

        return loginResponseDTO;
    }

    @Override
    public User getUserByEmployeeNo(String employeeNo) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getEmployeeNo, employeeNo);
        return userMapper.selectOne(queryWrapper);
    }

    /**
     * 注册第三方用户
     */
    private User registerThirdPartyUser(ThirdPartyLoginDTO.UserInfo userInfo, String apiKey, String appName) {
        User user = new User();
        user.setUsername(userInfo.getUsername());
        user.setEmployeeNo(userInfo.getEmployeeNo());
        user.setUserUuid(userInfo.getUserUuid());
        // nickname如果为空，使用username
        String nickname = StringUtils.hasText(userInfo.getNickname()) ? userInfo.getNickname() : userInfo.getUsername();
        user.setNickname(nickname);
        user.setEmail(userInfo.getEmail());
        user.setPhone(userInfo.getPhone());
        user.setAvatar(userInfo.getAvatar());
        // 第三方用户默认为普通用户
        user.setRole(Constants.UserRole.USER);
        user.setStatus(Constants.UserStatus.ENABLED);
        // 设置注册来源为第三方接入
        user.setRegisterSource(Constants.UserRegisterSource.THIRD_PARTY);
        // 第三方用户初始密码为工号
        user.setPassword(passwordEncoder.encode(userInfo.getEmployeeNo()));
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());

        userMapper.insert(user);
        return user;
    }

    /**
     * 更新第三方用户信息
     */
    private void updateThirdPartyUserInfo(User user, ThirdPartyLoginDTO.UserInfo userInfo) {
        boolean updated = false;
        
        // 处理nickname，如果第三方传入的nickname为空，使用username
        String thirdPartyNickname = StringUtils.hasText(userInfo.getNickname()) ? 
            userInfo.getNickname() : userInfo.getUsername();
        if (thirdPartyNickname != null && !thirdPartyNickname.equals(user.getNickname())) {
            user.setNickname(thirdPartyNickname);
            updated = true;
        }
        
        if (userInfo.getEmail() != null && !userInfo.getEmail().equals(user.getEmail())) {
            user.setEmail(userInfo.getEmail());
            updated = true;
        }
        if (userInfo.getPhone() != null && !userInfo.getPhone().equals(user.getPhone())) {
            user.setPhone(userInfo.getPhone());
            updated = true;
        }
        if (userInfo.getAvatar() != null && !userInfo.getAvatar().equals(user.getAvatar())) {
            user.setAvatar(userInfo.getAvatar());
            updated = true;
        }
        if (userInfo.getUserUuid() != null && !userInfo.getUserUuid().equals(user.getUserUuid())) {
            user.setUserUuid(userInfo.getUserUuid());
            updated = true;
        }

        if (updated) {
            user.setUpdateTime(LocalDateTime.now());
            userMapper.updateById(user);
        }
    }

    /**
     * 记录用户应用关系
     */
    private void recordUserAppRelation(Long userId, Long appId) {
        // 检查关系是否已存在
        LambdaQueryWrapper<UserAppRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserAppRelation::getUserId, userId)
                .eq(UserAppRelation::getAppId, appId);
        
        UserAppRelation existingRelation = userAppRelationMapper.selectOne(queryWrapper);
        if (existingRelation == null) {
            // 关系不存在，创建新关系
            UserAppRelation relation = new UserAppRelation();
            relation.setUserId(userId);
            relation.setAppId(appId);
            relation.setCreateTime(LocalDateTime.now());
            relation.setUpdateTime(LocalDateTime.now());
            userAppRelationMapper.insert(relation);
        } else {
            // 关系已存在，更新最后访问时间
            existingRelation.setUpdateTime(LocalDateTime.now());
            userAppRelationMapper.updateById(existingRelation);
        }
    }

    /**
     * 数据集成用户注册（预留方法）
     * 用于从外部系统批量导入用户数据时使用
     *
     * @param userInfo 用户信息
     * @return 注册的用户
     */
    @Transactional(rollbackFor = Exception.class)
    public User registerDataIntegrationUser(UserInfoDTO userInfo) {
        // 验证用户名是否已存在
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getUsername, userInfo.getUsername());
        if (userMapper.selectCount(queryWrapper) > 0) {
            throw new ApiException("用户名已存在");
        }

        // 创建用户
        User user = new User();
        BeanUtils.copyProperties(userInfo, user);
        // 设置默认值
        user.setRole(Constants.UserRole.USER);
        user.setStatus(Constants.UserStatus.ENABLED);
        // 设置注册来源为数据集成
        user.setRegisterSource(Constants.UserRegisterSource.DATA_INTEGRATION);
        // 数据集成用户设置默认密码（建议首次登录强制修改）
        user.setPassword(passwordEncoder.encode("123456"));
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());

        userMapper.insert(user);
        return user;
    }
}