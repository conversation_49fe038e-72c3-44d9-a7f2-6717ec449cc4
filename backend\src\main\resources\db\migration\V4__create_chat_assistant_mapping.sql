-- 创建聊天助手映射表
CREATE TABLE IF NOT EXISTS chat_assistant_mapping (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
    user_id BIGINT NOT NULL COMMENT '平台用户ID',
    ragflow_chat_assistant_id VARCHAR(255) NOT NULL COMMENT 'RAGflow聊天助手ID',
    assistant_name <PERSON><PERSON>HA<PERSON>(255) NOT NULL COMMENT '聊天助手名称',
    icon TEXT DEFAULT NULL COMMENT '助手图标（Base64编码）',
    ragflow_knowledge_base_ids TEXT COMMENT 'RAGflow知识库ID列表(JSON)',
    local_knowledge_base_ids TEXT COMMENT '本地知识库ID列表(JSON)',
    assistant_type TINYINT DEFAULT 1 COMMENT '助手类型: 1-RAGflow知识库, 2-本地知识库',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-禁用, 1-启用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_ragflow_chat_assistant_id (ragflow_chat_assistant_id),
    INDEX idx_status (status),
    UNIQUE KEY uk_user_assistant (user_id, ragflow_chat_assistant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='聊天助手映射表'; 