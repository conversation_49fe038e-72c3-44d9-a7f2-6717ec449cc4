package com.etrx.kb.service;

import com.etrx.kb.common.PageResult;
import com.etrx.kb.dto.ChatAssistantMappingDTO;

import java.util.List;

/**
 * 聊天助手映射服务接口
 */
public interface ChatAssistantMappingService {

    /**
     * 创建聊天助手映射记录
     *
     * @param userId                    用户ID
     * @param ragflowChatAssistantId   RAGflow聊天助手ID
     * @param assistantName            助手名称
     * @param icon                     助手图标（Base64编码）
     * @param ragflowKnowledgeBaseIds  RAGflow知识库ID列表
     * @param localKnowledgeBaseIds    本地知识库ID列表
     * @param assistantType            助手类型
     * @return 映射记录ID
     */
    Long createMapping(Long userId, String ragflowChatAssistantId, String assistantName, String icon,
                       List<String> ragflowKnowledgeBaseIds, List<Long> localKnowledgeBaseIds,
                       Integer assistantType);

    /**
     * 根据用户ID查询聊天助手列表
     *
     * @param userId 用户ID
     * @return 聊天助手列表
     */
    List<ChatAssistantMappingDTO> getUserChatAssistants(Long userId);

    /**
     * 分页查询用户的聊天助手列表
     *
     * @param userId   用户ID
     * @param pageNum  页码
     * @param pageSize 页大小
     * @return 分页结果
     */
    PageResult<ChatAssistantMappingDTO> getUserChatAssistantsPage(Long userId, Integer pageNum, Integer pageSize);

    /**
     * 根据用户ID和助手ID查询映射记录
     *
     * @param userId              用户ID
     * @param ragflowAssistantId  RAGflow助手ID
     * @return 映射记录
     */
    ChatAssistantMappingDTO getMappingByUserAndAssistant(Long userId, String ragflowAssistantId);

    /**
     * 禁用聊天助手
     *
     * @param id     映射记录ID
     * @param userId 用户ID
     */
    void disableChatAssistant(Long id, Long userId);

    /**
     * 启用聊天助手
     *
     * @param id     映射记录ID
     * @param userId 用户ID
     */
    void enableChatAssistant(Long id, Long userId);

    /**
     * 删除聊天助手映射
     *
     * @param id     映射记录ID
     * @param userId 用户ID
     */
    void deleteChatAssistant(Long id, Long userId);

    /**
     * 检查用户是否已有该助手
     *
     * @param userId              用户ID
     * @param ragflowAssistantId  RAGflow助手ID
     * @return 是否存在
     */
    boolean existsMapping(Long userId, String ragflowAssistantId);

    /**
     * 更新助手信息
     *
     * @param userId                    用户ID
     * @param ragflowChatAssistantId   RAGflow聊天助手ID
     * @param assistantName            助手名称
     * @param icon                     助手图标（Base64编码）
     * @param ragflowKnowledgeBaseIds  RAGflow知识库ID列表
     * @param localKnowledgeBaseIds    本地知识库ID列表
     */
    void updateAssistantInfo(Long userId, String ragflowChatAssistantId, String assistantName, String icon,
                           List<String> ragflowKnowledgeBaseIds, List<Long> localKnowledgeBaseIds);
} 