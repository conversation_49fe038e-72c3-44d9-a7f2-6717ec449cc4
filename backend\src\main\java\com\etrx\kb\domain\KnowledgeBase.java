package com.etrx.kb.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 知识库实体类
 */
@Data
@TableName("tb_knowledge_base")
public class KnowledgeBase implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 知识库名称
     */
    private String name;

    /**
     * 知识库描述
     */
    private String description;

    /**
     * 类型(Open,App)
     */
    private String type;

    /**
     * APP名称
     */
    private String appName;

    /**
     * API Key(App类型使用)
     */
    private String apiKey;

    /**
     * 数据集ID
     */
    private String datasetId;

    /**
     * 数据集配置
     */
    private String datasetConfig;

    /**
     * 所有者ID
     */
    private Long ownerId;

    /**
     * 状态(0:禁用,1:启用)
     */
    private Integer status;

    /**
     * 逻辑删除(0:未删除,1:已删除)
     */
    @TableLogic
    private Integer deleted;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}