W1:
1. 知识库团队成员管理 - 当前成员管理不可用，知识库owner可以对其成员进行新增或删除
    member：只读权限
    admin：读改权限
    owner：所有权限

    页面调试
    知识库成员查询接口
    知识库成员更新接口
    知识库成员删除接口

2. 权限控制，延用当前逻辑，根据角色值来进行权限校验？
    保持当前逻辑：用固定值1/2/3来判定或10/20/30判定

3. 创建知识库
    知识库详情页面设计
        知识库基本信息更新页面，参考：知识库>数据集/配置
        提供预览/解析/下载功能
    知识库配置数据表设计
    创建API接口优化
    更新API接口开发
    删除API接口开发
    生成API秘钥接口

    调用RAGFlow知识库创建接口
    调用RAGFlow知识库更新接口
    调用RAGFlow知识库删除接口

W2:
4. 知识库文档管理
    文档类型管理页面设计
    文档类型结构查询接口（系统文档>技术文档、产品文档）
    文档类型结构更新接口

    文档列表查询接口
    文档目录父子级查询接口

    文档数据可链接至知识库
    权限控制：文档管理中上传的文档，可以在文档管理中删除，但不能删除知识库上传的文档数据
            知识库中可删除所有，如果是文档中上传的，只解绑关系，如果是知识库上传的，连带文档一起删除

    调用RAGFlow文档创建接口
    调用RAGFlow文档更新接口
    调用RAGFlow文档删除接口
    调用RAGFlow文档解析接口
    调用RAGFlow文档停止解析接口

5. 文件管理
	文件列表展示
	文件上传，移动，删除
	目录树管理

W2进度：
	1.知识库更新布局 - 100%
	2.知识库文档文件管理-列表展示 - 100%
	3.知识库文档上传 - 100%
	4.同步上传RAGFLow文档 - 100%
	5.知识库文档/文件更新-基本数据更新和配置参数更新 - 70%
	6.知识库文档解析与终止 - 100%
	7.知识库文件夹目录查看，创建，删除，移动 - 100%

W3:
5.1 文件管理
    支持批量绑定和解绑知识库
    支持知识库文档列表展示兼容文件关联的目录及文件
    知识库文档结构调整，支持文档一对多知识库关系
    知识库存量接口整个，支持单个文档可以绑定多个知识库

5.2 知识库文档管理
    文档一对多结构下，优化文档解析逻辑
    文档一对多结构下，优化终止解析逻辑

6. 用户管理 - 管理员可修改用户的角色（管理员，普通用户）
    用户管理页面设计
        用户列表展示
        数据列表工具列展示（编辑/删除？）
    用户更新页面设计
        更新用户基本信息(头像，昵称)，角色
    用户查询接口
    用户更新接口

7. 密钥管理
	优化复制密钥逻辑
	密钥权限管理

9. 全局样式调整

计划进度：
2. 支持单文件绑定多个知识库（100%）
3. 支持文档一对多结构下，文档解析和终止解析（100%）
4. 支持知识库文档展示非知识库文档（100%）
5. 用户管理，支持查看，修改，删除等功能（100%）
6. 权限管理，调整系统管理和文档管理员的查看数据范围权限（100%）
7. 密钥管理，调整复制及查看密钥的权限范围（100%）
8. 模块联调，给Chat提供知识库列表接口（100%）
9. 全局样式调整（100%）
10.适配ragflow 0.19升级 - 参数调整

W4:
11.支持第三方接口接入
    第三方接口接入的鉴权及用户信息接入

12. 知识库文档管理
    ragflow升级适配-知识库参数升级配置联调
    ragflow升级适配-文档参数升级配置联调

13. 页面样式微调

nomic-embed-text:latest
嵌入模型需要改成数据字典配置,供页面选择

后续计划：
14.支持Q&A(需等外部接口联调)
    RAGFLOW支持多种解析方式，支持Q&A模式

15.支持知识库文档切片信息管理
        知识库文档切片信息查询接口
        知识库文档切片信息更新接口
        知识库文档切片信息删除接口


其他待定




