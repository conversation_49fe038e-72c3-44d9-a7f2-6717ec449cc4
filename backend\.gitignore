# 构建输出
target/
/bin/
/build/
/out/
*.jar
*.war
*.ear

# 依赖管理
/.settings/
/.classpath
/.project
/.factorypath

# 开发环境配置
.env
application-dev.properties
application-local.yml
/config/local/

# IDE文件
.idea/
*.iml
*.ipr
*.iws
.vscode/
.classpath
.project
.settings/

# 日志文件
*.log
/logs/
/spring.log

# 系统文件
.DS_Store
Thumbs.db

# 其他
*.swp
*.bak
*.patch
*.tmp

# Maven包装器（保留wrapper）
!.mvn/wrapper/maven-wrapper.jar

# 测试输出
**/reports/
**/test-output/
**/test-results/