import React, { useState, useCallback, useEffect, memo, useRef } from 'react';
import { Button, Drawer, Space, Typography, Popover,  List, Spin, Avatar } from 'antd';
import { X, Plus, History, Minimize2, Maximize2, Bot } from 'lucide-react';
import MessageInput from './message-input';
import MessageItem from './message-item';
import styles from './index.module.less';
import {
  useFetchNextConversation,
  useHandleDialog,
  useHandleMessageInputChange,
  useSendButtonDisabled,
  useFindPrologueFromDialogList,
  useSetConversation
} from './hook';
import trim from 'lodash/trim';
import { v4 as uuid } from 'uuid';
import { MessageType } from '@/constants/chat';
import { useSelectDerivedMessages, useSendMessageWithSse } from './hook';
import {  Message } from '@/interfaces/database/chat';
import api from '@/utils/api';
import { useSelectDerivedConversationList } from './hook';
import { IMessage } from '@/pages/chat/interface';
import { useRemoveNextConversation } from './hook';
import { DeleteOutlined } from '@ant-design/icons';
import { useTranslate } from '@/hooks/common-hooks';
import { useRegenerateMessage } from './hook';
import { ReactComponent as AiChatIcon } from '@/assets/svg/ai-chat-icon.svg';

const { Title } = Typography;

export interface ChatWidgetProps {
  fullscreenClick?: () => void;
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  drawerWidth?: number;
  iconPosition?: { left: number; top: number };
}

// interface Message {
//   id: string;
//   role: 'user' | 'assistant';
//   content: string;
//   doc_ids?: string[];
// }

interface ContextTag {
  id: string;
  name: string;
}

const ChatWidget: React.FC<ChatWidgetProps> = ({
  fullscreenClick,
  position = 'bottom-right',
  drawerWidth = 600,
  iconPosition
}) => {
  const [visible, setVisible] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const { dialogId, setDialogId, data: dialogDetail} = useHandleDialog();
  const [historyVisible, setHistoryVisible] = useState(false);
  const [contextTags, setContextTags] = useState<ContextTag[]>([]);
  const [controller, setController] = useState(new AbortController());
  const { handleInputChange, value, setValue } = useHandleMessageInputChange();
  const iconRef = useRef<HTMLDivElement>(null);

  const {
    conversationId,
    setConversationId,
    list: conversationList,
    addConversation,
    loading: conversationListLoading,
  } = useSelectDerivedConversationList();
  const handleAddNewConversation = useCallback(async () => {
    let newConversation = await addConversation();
    setConversationId(newConversation.data);
  }, [addConversation]);


  const { data: conversation, loading: conversationLoading } = useFetchNextConversation(conversationId);
  const {setConversation} = useSetConversation()
  const stopOutputMessage = useCallback(() => {
    controller.abort();
    setController(new AbortController());
  }, [controller]);
  const { send, answer, done } = useSendMessageWithSse(
    api.completeConversation,
  );



  const {
    ref,
    setDerivedMessages,
    derivedMessages,
    addNewestAnswer,
    addNewestQuestion,
    removeLatestMessage,
    removeMessagesAfterCurrentMessage
  } = useSelectDerivedMessages(visible);

  const sendMessage = useCallback(
    async ({
             message,
             currentConversationId,
             messages,
           }: {
      message: Message;
      currentConversationId?: string;
      messages?: Message[];
    }) => {
      const res = await send(
        {
          conversationId: currentConversationId,
          messages: [...(messages ?? derivedMessages ?? []), message],
          question: message.content,
          sessionId: currentConversationId,
        },
        controller,
      );

      if (res && (res?.response.status !== 200 || res?.data?.code !== 0)) {
        setValue(message.content);
        console.info('removeLatestMessage111');
        removeLatestMessage();
      }
    },
    [
      derivedMessages,
      removeLatestMessage,
      setValue,
      send,
      controller,
    ],
  );
  const { regenerateMessage } = useRegenerateMessage({
    removeMessagesAfterCurrentMessage,
    sendMessage,
    messages: derivedMessages,
    conversationId
  });

  const { t } = useTranslate('chat');
  const handlePressEnterForWidget = useCallback(
    async (question?: string, documentIds?: string[]) => {
      let innerValue = question ||value;
      if (trim(innerValue) === '') return;
      const id = uuid();
      if (done) {
        addNewestQuestion({
          content: innerValue,
          doc_ids: documentIds,
          id,
          role: MessageType.User,
        });
        setValue('');
        if (conversation.name !== t('newConversation')) {
          sendMessage({ message: {
              id,
              content: innerValue.trim(),
              role: MessageType.User,
              doc_ids: documentIds,
            }, currentConversationId: conversationId });
        } else {
          const data = await setConversation(
            innerValue.trim(),
            false,
            conversationId,
            dialogId
          );
          if (data.code === 0 || data.code === 200) {
            sendMessage({ message: {
                id,
                content: innerValue.trim(),
                role: MessageType.User,
                doc_ids: documentIds,
              }, currentConversationId: conversationId });
          }
        }
      }
    },
    [addNewestQuestion, sendMessage, done, setValue, value, conversationId, conversation],
  );
  const handleRemoveContextTag = (tagId: string) => {
    setContextTags((prev) => prev.filter((tag) => tag.id !== tagId));
  };
  const addContextTag = (tag: ContextTag) => {
    setContextTags((prev) => [
      ...prev,
      {
        ...tag
      }
    ])
  }

  useEffect(() => {
    if (answer.answer && conversationId) {
      addNewestAnswer(answer);
    }
  }, [answer, addNewestAnswer, conversationId]);



  // 监听dialogId变化
  // useEffect(() => {
  //   loadTopics();
  // }, [dialogId, loadTopics]);
  const sendDisabled = useSendButtonDisabled(value);

  useEffect(() => {
    (window as any).chatWidget = {
      ask:(question: string) => {
        handlePressEnterForWidget(question, []);
        setVisible(true);
      },
      show:() => {
        setVisible(true);
      },
      hide:() => {
        setVisible(false);
      }
    }
  });

  const handleIconClick = useCallback(() => {
    setVisible(true);
  }, []);

  const prologue = useFindPrologueFromDialogList();
  const addPrologue = useCallback(() => {
    if (dialogId !== '' && conversationId !== '') {
      const nextMessage = {
        role: MessageType.Assistant,
        content: prologue,
        id: uuid(),
      } as IMessage;

      setDerivedMessages([nextMessage]);
    }
  }, [dialogId, prologue, setDerivedMessages, conversationId]);
  useEffect(() => {
    if (conversationId) {
      if (conversation.message?.length > 0) {
        setDerivedMessages(conversation.message || []);
      } else {
        addPrologue();
      }
    }
  }, [conversation.message, conversationId, setDerivedMessages]);

  const { removeConversation } = useRemoveNextConversation();
  // console.log("conversationList", conversationList)
  const historyContent = (
    <div className={styles.historyContent}>
      <Spin spinning={conversationListLoading}>
        <List
          dataSource={conversationList}
          renderItem={(conversation) => (
            <List.Item
              key={conversation.id}
              onClick={(e) => {
                setConversationId(conversation.id);
                setHistoryVisible(false);
              }}
              className={styles.topicItem}
            >
              <div style={{ flex: 1, margin:"0px 8px 0px 0px" }}>
                <div className="tw-text-xs text-slate-800">{conversation.name}</div>
                <div className="tw-text-xs text-slate-500">
                  {conversation.createTime
                    ? new Date(conversation.createTime).toLocaleString()
                    : ''}
                </div>
              </div>
              <DeleteOutlined className="tw-text-red-500"  onClick={ (e) => {
                e.preventDefault();
                e.stopPropagation();
                removeConversation(conversation.id);
                setHistoryVisible(false);
              }}/>
            </List.Item>
          )}
        />
      </Spin>
    </div>
  );

  const handleToggleFullscreen = useCallback(() => {
    if(fullscreenClick) {
      fullscreenClick();
    } else {
      setIsFullscreen(prev => !prev);
    }
  }, []);
  const handleOpenChange = (newOpen: boolean) => {
    setHistoryVisible(newOpen);
  };

  const handleCloseDrawer = useCallback(() => {
    setVisible(false);
  }, []);

  return (
    <div className={styles.chatWidget} >
      {!visible && (
        <div
          ref={iconRef}
          data-position={position}
          className={styles.customChatIcon}
          style={iconPosition ? { 
            left: iconPosition.left, 
            top: iconPosition.top, 
            position: 'fixed', 
            zIndex: 1100 
          } : {}}
          title="点击打开聊天"
          onClick={handleIconClick}
        >
          <AiChatIcon style={{ width: '24px', height: '24px' }} />
        </div>
      )}
      <Drawer
        zIndex={10000}
        title={
          <div className="tw-flex tw-items-center tw-space-x-3">
            <div className="tw-relative">
              <Avatar className="tw-bg-gradient-to-br tw-from-blue-500 tw-to-purple-600 tw-text-white tw-h-10 tw-w-10 tw-ring-2 tw-ring-blue-100" icon={<Bot className="tw-h-5 tw-w-5" />}>
              </Avatar>
              <div className="tw-absolute tw--bottom-1 tw--right-1 tw-h-4 tw-w-4 tw-bg-green-500 tw-rounded-full tw-border-2 tw-border-white tw-shadow-sm"></div>
            </div>
            <div>
              <h4 className="tw-font-semibold tw-text-slate-800">易特克思AI助手</h4>
              <p className="tw-text-xs tw-text-slate-500">在线 · 随时为您服务</p>
            </div>
          </div>
        }
        placement="right"
        onClose={handleCloseDrawer}
        open={visible}
        width={isFullscreen ? '100%' : drawerWidth}
        closeIcon={false}
        mask={false}
        className={isFullscreen ? styles.fullscreenDrawer : ''}
        extra={
          <Space>
            <Button
              type="text"
              className="tw-h-8 tw-w-8 tw-text-slate-600 hover:tw-text-slate-800"
              icon={<Plus className="tw-h-4 tw-w-4"/>}
              onClick={handleAddNewConversation}
            />
            <Popover
              zIndex={10000}
              content={historyContent}
              open={historyVisible}
              onOpenChange={handleOpenChange}
              trigger="click"
            >
              <Button
                type="text"
                className="tw-h-8 tw-w-8 tw-text-slate-600 hover:tw-text-slate-800"
                icon={<History className="tw-h-4 tw-w-4"/>}
              />
            </Popover>
            <Button
              type="text"
              className="tw-h-8 tw-w-8 tw-text-slate-600 hover:tw-text-slate-800"
              icon={isFullscreen ? <Minimize2 className="tw-h-4 tw-w-4"/> : <Maximize2 className="tw-h-4 tw-w-4"/>}
              onClick={handleToggleFullscreen}
            />
            <Button
              type="text"
              className="tw-h-8 tw-w-8 tw-text-slate-600 hover:tw-text-slate-800"
              icon={<X className="tw-h-4 tw-w-4"/>}
              onClick={handleCloseDrawer}
            />
          </Space>
        }
      >
        <Spin spinning={conversationLoading} wrapperClassName="wgq" style={{height: '100%'}}>
        <div className={styles.chatContainer}>
          <div className={styles.messageList}>
            {derivedMessages.map((message, index) => (
              <MessageItem
                key={message.id}
                item={message}
                loading={!done}
                index={index}
                reference={message.reference}
                regenerateMessage={regenerateMessage}
              />
            ))}
            <div ref={ref} />
          </div>
          <div className={styles.inputContainer}>
            <MessageInput
              sendLoading={!done}
              conversationId={conversationId || 'default'}
              disabled={!conversationId}
              sendDisabled={sendDisabled}
              value={value}
              onInputChange={handleInputChange}
              onPressEnter={handlePressEnterForWidget}
              stopOutputMessage={stopOutputMessage}
              contextTags={contextTags}
              onRemoveContextTag={handleRemoveContextTag}
            />
          </div>
        </div>
        </Spin>
      </Drawer>
    </div>
  );
};

export default memo(ChatWidget);