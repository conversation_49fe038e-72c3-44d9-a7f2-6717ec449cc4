package com.etrx.kb.dto;

import lombok.Data;
import java.util.List;

/**
 * Chunk数据传输对象
 */
@Data
public class ChunkDTO {

    /**
     * Chunk ID
     */
    private String id;

    /**
     * 数据集ID
     */
    private String datasetId;

    /**
     * 文档ID
     */
    private String documentId;

    /**
     * Chunk内容
     */
    private String content;

    /**
     * 重要关键词列表
     */
    private List<String> importantKeywords;

    /**
     * 相关问题列表
     */
    private List<String> questions;

    /**
     * 是否可用
     */
    private Boolean available;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 创建时间戳
     */
    private Double createTimestamp;

    /**
     * 文档名称关键字
     */
    private String docnmKwd;

    /**
     * 图片ID
     */
    private String imageId;

    /**
     * 位置信息
     */
    private List<String> positions;
} 