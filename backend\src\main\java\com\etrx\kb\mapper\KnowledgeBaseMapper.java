package com.etrx.kb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.etrx.kb.domain.KnowledgeBase;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 知识库Mapper接口
 */
@Mapper
public interface KnowledgeBaseMapper extends BaseMapper<KnowledgeBase> {
    
    /**
     * 通过datasetId获取知识库ID
     */
    @Select("SELECT id FROM tb_knowledge_base WHERE dataset_id = #{datasetId} AND deleted = 0")
    Long getKbIdByDatasetId(@Param("datasetId") String datasetId);

    /**
     * 获取知识库的文档数量
     */
    @Select("SELECT COUNT(*) FROM tb_kb_file_rel WHERE kb_id = #{kbId} AND deleted = 0")
    Long countDocuments(@Param("kbId") Long kbId);

    /**
     * 检查用户是否是知识库管理员
     */
    @Select("SELECT IF(COUNT(*) > 0, 1, 0) FROM tb_kb_member WHERE kb_id = #{kbId} AND user_id = #{userId} AND role >= 20")
    boolean isKbAdmin(@Param("kbId") Long kbId, @Param("userId") Long userId);

    /**
     * 批量获取知识库的文档数量
     */
    @Select("""
            <script>
            SELECT kb_id, COUNT(*) as count 
            FROM tb_kb_file_rel 
            WHERE kb_id IN 
            <foreach collection='kbIds' item='item' open='(' separator=',' close=')'>
                #{item}
            </foreach>
            AND deleted = 0 
            GROUP BY kb_id
            </script>
            """)
    List<Map<String, Object>> batchCountDocumentsList(@Param("kbIds") List<Long> kbIds);

    /**
     * 批量获取知识库的文档数量（返回Map形式）
     */
    default Map<Long, Long> batchCountDocuments(List<Long> kbIds) {
        if (kbIds == null || kbIds.isEmpty()) {
            return new HashMap<>();
        }
        return batchCountDocumentsList(kbIds).stream()
                .collect(java.util.stream.Collectors.toMap(
                        map -> Long.valueOf(String.valueOf(map.get("kb_id"))),
                        map -> Long.valueOf(String.valueOf(map.get("count")))
                ));
    }

    /**
     * 统计用户有权限的知识库总数
     */
    @Select("""
            SELECT COUNT(DISTINCT kb.id) 
            FROM tb_knowledge_base kb 
            LEFT JOIN tb_kb_member m ON kb.id = m.kb_id AND m.deleted = 0
            WHERE kb.deleted = 0 
            AND (
                kb.type = 'Open' 
                OR (
                    kb.type = 'App' 
                    AND (
                        kb.owner_id = #{userId} 
                        OR m.user_id = #{userId}
                    )
                )
            )
            """)
    Long countUserKnowledgeBases(@Param("userId") Long userId);

    /**
     * 统计用户有权限的知识库下的所有文档数量
     */
    @Select("""
            SELECT COUNT(DISTINCT r.id)
            FROM tb_knowledge_base kb
            LEFT JOIN tb_kb_member m ON kb.id = m.kb_id AND m.deleted = 0
            LEFT JOIN tb_kb_file_rel r ON kb.id = r.kb_id AND r.deleted = 0
            WHERE kb.deleted = 0
            AND (
                kb.type = 'Open'
                OR (
                    kb.type = 'App'
                    AND (
                        kb.owner_id = #{userId}
                        OR m.user_id = #{userId}
                    )
                )
            )
            """)
    Long countUserDocuments(@Param("userId") Long userId);

    /**
     * 统计用户有权限的知识库下的所有成员数量
     */
    @Select("""
            SELECT COUNT(DISTINCT m2.id)
            FROM tb_knowledge_base kb
            LEFT JOIN tb_kb_member m ON kb.id = m.kb_id AND m.deleted = 0
            LEFT JOIN tb_kb_member m2 ON kb.id = m2.kb_id AND m2.deleted = 0
            WHERE kb.deleted = 0
            AND (
                kb.type = 'Open'
                OR (
                    kb.type = 'App'
                    AND (
                        kb.owner_id = #{userId}
                        OR m.user_id = #{userId}
                    )
                )
            )
            """)
    Long countUserKnowledgeBaseMembers(@Param("userId") Long userId);

    // ========== 超级管理员统计方法 ==========
    
    /**
     * 统计所有知识库总数（超级管理员）
     */
    @Select("SELECT COUNT(*) FROM tb_knowledge_base WHERE deleted = 0")
    Long countAllKnowledgeBases();

    /**
     * 统计所有文档总数（超级管理员）
     */
    @Select("SELECT COUNT(*) FROM tb_kb_file_rel WHERE deleted = 0")
    Long countAllDocuments();

    /**
     * 统计所有成员总数（超级管理员）
     */
    @Select("SELECT COUNT(*) FROM tb_kb_member WHERE deleted = 0")
    Long countAllKnowledgeBaseMembers();

    // ========== 本周新增统计方法 ==========
    
    /**
     * 统计用户有权限的知识库本周新增数量
     */
    @Select("""
            SELECT COUNT(DISTINCT kb.id) 
            FROM tb_knowledge_base kb 
            LEFT JOIN tb_kb_member m ON kb.id = m.kb_id AND m.deleted = 0
            WHERE kb.deleted = 0 
            AND kb.create_time >= #{weekStart}
            AND kb.create_time < #{weekEnd}
            AND (
                kb.type = 'Open' 
                OR (
                    kb.type = 'App' 
                    AND (
                        kb.owner_id = #{userId} 
                        OR m.user_id = #{userId}
                    )
                )
            )
            """)
    Long countUserWeeklyNewKnowledgeBases(@Param("userId") Long userId, @Param("weekStart") LocalDateTime weekStart, @Param("weekEnd") LocalDateTime weekEnd);

    /**
     * 统计用户有权限的知识库本周删除数量
     */
    @Select("""
            SELECT COUNT(DISTINCT kb.id) 
            FROM tb_knowledge_base kb 
            LEFT JOIN tb_kb_member m ON kb.id = m.kb_id AND m.deleted = 0
            WHERE kb.deleted = 1 
            AND kb.update_time >= #{weekStart}
            AND kb.update_time < #{weekEnd}
            AND (
                kb.type = 'Open' 
                OR (
                    kb.type = 'App' 
                    AND (
                        kb.owner_id = #{userId} 
                        OR m.user_id = #{userId}
                    )
                )
            )
            """)
    Long countUserWeeklyDeletedKnowledgeBases(@Param("userId") Long userId, @Param("weekStart") LocalDateTime weekStart, @Param("weekEnd") LocalDateTime weekEnd);

    /**
     * 统计用户有权限的知识库下本周新增文档数量
     */
    @Select("""
            SELECT COUNT(DISTINCT r.id)
            FROM tb_knowledge_base kb
            LEFT JOIN tb_kb_member m ON kb.id = m.kb_id AND m.deleted = 0
            LEFT JOIN tb_kb_file_rel r ON kb.id = r.kb_id AND r.deleted = 0
            WHERE kb.deleted = 0
            AND r.create_time >= #{weekStart}
            AND r.create_time < #{weekEnd}
            AND (
                kb.type = 'Open'
                OR (
                    kb.type = 'App'
                    AND (
                        kb.owner_id = #{userId}
                        OR m.user_id = #{userId}
                    )
                )
            )
            """)
    Long countUserWeeklyNewDocuments(@Param("userId") Long userId, @Param("weekStart") LocalDateTime weekStart, @Param("weekEnd") LocalDateTime weekEnd);

    /**
     * 统计用户有权限的知识库下本周删除文档数量
     */
    @Select("""
            SELECT COUNT(DISTINCT r.id)
            FROM tb_knowledge_base kb
            LEFT JOIN tb_kb_member m ON kb.id = m.kb_id AND m.deleted = 0
            LEFT JOIN tb_kb_file_rel r ON kb.id = r.kb_id AND r.deleted = 1
            WHERE kb.deleted = 0
            AND r.update_time >= #{weekStart}
            AND r.update_time < #{weekEnd}
            AND (
                kb.type = 'Open'
                OR (
                    kb.type = 'App'
                    AND (
                        kb.owner_id = #{userId}
                        OR m.user_id = #{userId}
                    )
                )
            )
            """)
    Long countUserWeeklyDeletedDocuments(@Param("userId") Long userId, @Param("weekStart") LocalDateTime weekStart, @Param("weekEnd") LocalDateTime weekEnd);

    /**
     * 统计用户有权限的知识库下本周新增成员数量
     */
    @Select("""
            SELECT COUNT(DISTINCT m2.id)
            FROM tb_knowledge_base kb
            LEFT JOIN tb_kb_member m ON kb.id = m.kb_id AND m.deleted = 0
            LEFT JOIN tb_kb_member m2 ON kb.id = m2.kb_id AND m2.deleted = 0
            WHERE kb.deleted = 0
            AND m2.create_time >= #{weekStart}
            AND m2.create_time < #{weekEnd}
            AND (
                kb.type = 'Open'
                OR (
                    kb.type = 'App'
                    AND (
                        kb.owner_id = #{userId}
                        OR m.user_id = #{userId}
                    )
                )
            )
            """)
    Long countUserWeeklyNewMembers(@Param("userId") Long userId, @Param("weekStart") LocalDateTime weekStart, @Param("weekEnd") LocalDateTime weekEnd);

    /**
     * 统计用户有权限的知识库下本周删除成员数量
     */
    @Select("""
            SELECT COUNT(DISTINCT m2.id)
            FROM tb_knowledge_base kb
            LEFT JOIN tb_kb_member m ON kb.id = m.kb_id AND m.deleted = 0
            LEFT JOIN tb_kb_member m2 ON kb.id = m2.kb_id AND m2.deleted = 1
            WHERE kb.deleted = 0
            AND m2.update_time >= #{weekStart}
            AND m2.update_time < #{weekEnd}
            AND (
                kb.type = 'Open'
                OR (
                    kb.type = 'App'
                    AND (
                        kb.owner_id = #{userId}
                        OR m.user_id = #{userId}
                    )
                )
            )
            """)
    Long countUserWeeklyDeletedMembers(@Param("userId") Long userId, @Param("weekStart") LocalDateTime weekStart, @Param("weekEnd") LocalDateTime weekEnd);

    // ========== 超级管理员本周统计方法 ==========
    
    /**
     * 统计所有知识库本周新增数量（超级管理员）
     */
    @Select("""
            SELECT COUNT(*) 
            FROM tb_knowledge_base 
            WHERE deleted = 0 
            AND create_time >= #{weekStart}
            AND create_time < #{weekEnd}
            """)
    Long countAllWeeklyNewKnowledgeBases(@Param("weekStart") LocalDateTime weekStart, @Param("weekEnd") LocalDateTime weekEnd);

    /**
     * 统计所有知识库本周删除数量（超级管理员）
     */
    @Select("""
            SELECT COUNT(*) 
            FROM tb_knowledge_base 
            WHERE deleted = 1 
            AND update_time >= #{weekStart}
            AND update_time < #{weekEnd}
            """)
    Long countAllWeeklyDeletedKnowledgeBases(@Param("weekStart") LocalDateTime weekStart, @Param("weekEnd") LocalDateTime weekEnd);

    /**
     * 统计所有文档本周新增数量（超级管理员）
     */
    @Select("""
            SELECT COUNT(*) 
            FROM tb_kb_file_rel 
            WHERE deleted = 0 
            AND create_time >= #{weekStart}
            AND create_time < #{weekEnd}
            """)
    Long countAllWeeklyNewDocuments(@Param("weekStart") LocalDateTime weekStart, @Param("weekEnd") LocalDateTime weekEnd);

    /**
     * 统计所有文档本周删除数量（超级管理员）
     */
    @Select("""
            SELECT COUNT(*) 
            FROM tb_kb_file_rel 
            WHERE deleted = 1 
            AND update_time >= #{weekStart}
            AND update_time < #{weekEnd}
            """)
    Long countAllWeeklyDeletedDocuments(@Param("weekStart") LocalDateTime weekStart, @Param("weekEnd") LocalDateTime weekEnd);

    /**
     * 统计所有成员本周新增数量（超级管理员）
     */
    @Select("""
            SELECT COUNT(*) 
            FROM tb_kb_member 
            WHERE deleted = 0 
            AND create_time >= #{weekStart}
            AND create_time < #{weekEnd}
            """)
    Long countAllWeeklyNewMembers(@Param("weekStart") LocalDateTime weekStart, @Param("weekEnd") LocalDateTime weekEnd);

    /**
     * 统计所有成员本周删除数量（超级管理员）
     */
    @Select("""
            SELECT COUNT(*) 
            FROM tb_kb_member 
            WHERE deleted = 1 
            AND update_time >= #{weekStart}
            AND update_time < #{weekEnd}
            """)
    Long countAllWeeklyDeletedMembers(@Param("weekStart") LocalDateTime weekStart, @Param("weekEnd") LocalDateTime weekEnd);

    /**
     * 通过datasetId列表获取知识库ID列表
     */
    @Select("""
            <script>
            SELECT id FROM tb_knowledge_base 
            WHERE dataset_id IN 
            <foreach collection='datasetIds' item='datasetId' open='(' separator=',' close=')'>
                #{datasetId}
            </foreach>
            AND deleted = 0
            </script>
            """)
    List<Long> getKbIdsByDatasetIds(@Param("datasetIds") List<String> datasetIds);
}