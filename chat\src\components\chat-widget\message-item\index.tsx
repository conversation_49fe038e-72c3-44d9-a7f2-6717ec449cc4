import { ReactComponent as AssistantI<PERSON> } from '@/assets/svg/assistant.svg';
import { ReactComponent as UserIcon } from '@/assets/svg/logo.svg';
import { MessageType } from '@/constants/chat';
import { IReference,  } from '@/interfaces/database/chat';
import {  Message } from '@/interfaces/database/chat';
import classNames from 'classnames';
import React, { memo, useCallback} from 'react';
import {  IRemoveMessageById } from '@/hooks/logic-hooks';
import { IMessage } from '@/pages/chat/interface';
import MarkdownContent from '../markdown-content';
import { Avatar, Flex, Space } from 'antd';
import { AssistantGroupButton, UserGroupButton } from './group-button';
import styles from './index.module.less';
import { Bot, User } from 'lucide-react';

export interface IRegenerateMessage {
  regenerateMessage?: (message: Message) => void;
}

interface IProps extends Partial<IRemoveMessageById>, IRegenerateMessage {
  item: IMessage;
  reference?: IReference;
  loading?: boolean;
  sendLoading?: boolean;
  visibleAvatar?: boolean;
  nickname?: string;
  avatar?: string;
  avatarDialog?: string | null;
  index: number;
  showLikeButton?: boolean;
  showLoudspeaker?: boolean;
}

const MessageItem = ({
  item,
  reference,
  loading = false,
  avatar,
  avatarDialog,
  sendLoading = false,
  index,
  removeMessageById,
  regenerateMessage,
  showLikeButton = true,
  showLoudspeaker = true,
  visibleAvatar = true,
}: IProps) => {
  const isAssistant = item.role === MessageType.Assistant;
  const handleRegenerateMessage = useCallback(() => {
    regenerateMessage?.(item);
  }, [regenerateMessage, item]);

  return (
    <div
      className={classNames(styles.messageItem, {
        [styles.messageItemLeft]: item.role === MessageType.Assistant,
        [styles.messageItemRight]: item.role === MessageType.User,
      })}
    >
      <section
        className={classNames(styles.messageItemSection, {
          [styles.messageItemSectionLeft]: item.role === MessageType.Assistant,
          [styles.messageItemSectionRight]: item.role === MessageType.User,
        })}
      >
        <div
          className={classNames(styles.messageItemContent, {
            [styles.messageItemContentReverse]: item.role === MessageType.User,
          })}
        >
          {visibleAvatar &&
            (item.role === MessageType.User ?
                <Avatar className="tw-bg-gradient-to-br tw-from-green-500 tw-to-teal-600 tw-text-white" size="large" icon={<User className="tw-h-5 tw-w-5" />} />
                :
                <Avatar className="tw-bg-gradient-to-br tw-from-blue-500 tw-to-purple-600 tw-text-white" size="large" icon={<Bot className="tw-h-5 tw-w-5" />} />
            )}

          <Flex vertical gap={8} flex={1}>
            <Space>
              {isAssistant ? (
                index !== 0 && (
                  <AssistantGroupButton
                    messageId={item.id}
                    content={item.content}
                    prompt={item.prompt}
                    showLikeButton={showLikeButton}
                    audioBinary={item.audio_binary}
                    showLoudspeaker={showLoudspeaker}
                  ></AssistantGroupButton>
                )
              ) : (
                <UserGroupButton
                  content={item.content}
                  messageId={item.id}
                  removeMessageById={removeMessageById}
                  regenerateMessage={
                    regenerateMessage && handleRegenerateMessage
                  }
                  sendLoading={sendLoading}
                ></UserGroupButton>
              )}
               {/*<b>{isAssistant ? '' : nickname}</b>*/}
            </Space>
            <div
              className={
                isAssistant
                  ? styles.messageText
                  : styles.messageUserText
              }
            >
              <MarkdownContent
                loading={loading}
                content={item.content}
              ></MarkdownContent>
            </div>
          </Flex>
        </div>
      </section>
    </div>
  );
};

export default memo(MessageItem);
