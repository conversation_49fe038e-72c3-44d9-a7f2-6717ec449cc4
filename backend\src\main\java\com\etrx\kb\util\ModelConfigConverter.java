package com.etrx.kb.util;

import com.etrx.kb.config.ModelConfig;
import com.etrx.kb.domain.ModelConfigEntity;
import com.etrx.kb.dto.ModelDTO;

/**
 * 模型配置转换工具类
 */
public class ModelConfigConverter {

    /**
     * 将数据库实体转换为配置项
     */
    public static ModelConfig.ModelConfigItem entityToConfigItem(ModelConfigEntity entity) {
        if (entity == null) {
            return null;
        }
        
        ModelConfig.ModelConfigItem item = new ModelConfig.ModelConfigItem();
        item.setLlmName(entity.getLlmName());
        item.setModelType(entity.getModelType());
        item.setAvailable(entity.getAvailable());
        item.setMaxTokens(entity.getMaxTokens());
        item.setIsTools(entity.getIsTools());
        item.setStatus(entity.getStatus());
        item.setTags(entity.getTags());
        return item;
    }

    /**
     * 将配置项转换为数据库实体
     */
    public static ModelConfigEntity configItemToEntity(String provider, ModelConfig.ModelConfigItem item) {
        if (item == null) {
            return null;
        }
        
        ModelConfigEntity entity = new ModelConfigEntity();
        entity.setProvider(provider);
        entity.setLlmName(item.getLlmName());
        entity.setModelType(item.getModelType());
        entity.setAvailable(item.getAvailable() != null ? item.getAvailable() : true);
        entity.setMaxTokens(item.getMaxTokens());
        entity.setIsTools(item.getIsTools() != null ? item.getIsTools() : false);
        entity.setStatus(item.getStatus() != null ? item.getStatus() : "1");
        entity.setTags(item.getTags());
        return entity;
    }

    /**
     * 将数据库实体转换为DTO
     */
    public static ModelDTO entityToDTO(ModelConfigEntity entity) {
        if (entity == null) {
            return null;
        }
        
        return entityToConfigItem(entity).toModelDTO(entity.getProvider());
    }
} 