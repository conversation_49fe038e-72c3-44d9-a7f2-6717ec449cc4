package com.etrx.kb.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 文件夹DTO
 */
@Data
public class FolderDTO {

    /**
     * 文件夹ID
     */
    private Long id;

    /**
     * 知识库ID
     */
    @NotNull(message = "知识库ID不能为空")
    private Long kbId;

    /**
     * 文件夹名称
     */
    @NotBlank(message = "文件夹名称不能为空")
    @Size(max = 100, message = "文件夹名称长度不能超过100个字符")
    private String name;

    /**
     * 父文件夹ID
     */
    private Long parentId;

    /**
     * 子文件夹列表（用于树形结构）
     */
    private List<FolderDTO> children;

    /**
     * 文档列表（用于树形结构）
     */
    private List<DocumentDTO> documents;
}