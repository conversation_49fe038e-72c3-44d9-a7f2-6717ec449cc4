package com.etrx.kb.task;

import com.etrx.kb.annotation.TaskDefinition;
import com.etrx.kb.service.TaskDefinitionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 任务定义扫描器
 * 在应用启动时扫描所有带有 @TaskDefinition 注解的方法
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TaskDefinitionScanner implements CommandLineRunner {

    private final ApplicationContext applicationContext;
    private final TaskDefinitionService taskDefinitionService;

    @Override
    public void run(String... args) throws Exception {
        log.info("开始扫描任务定义...");
        scanTaskDefinitions();
        log.info("任务定义扫描完成");
    }

    /**
     * 扫描任务定义
     */
    private void scanTaskDefinitions() {
        // 获取所有Bean
        Map<String, Object> beans = applicationContext.getBeansOfType(Object.class);
        
        for (Map.Entry<String, Object> entry : beans.entrySet()) {
            Object bean = entry.getValue();
            Class<?> clazz = bean.getClass();
            
            // 跳过Spring内部Bean和代理类
            if (clazz.getName().startsWith("org.springframework") || 
                clazz.getName().contains("$$")) {
                continue;
            }
            
            // 获取实际的类（处理CGLIB代理）
            if (clazz.getName().contains("$$")) {
                clazz = clazz.getSuperclass();
            }
            
            // 扫描所有方法
            Method[] methods = clazz.getDeclaredMethods();
            for (Method method : methods) {
                if (method.isAnnotationPresent(TaskDefinition.class)) {
                    processTaskDefinition(clazz, method);
                }
            }
        }
    }

    /**
     * 处理任务定义
     */
    private void processTaskDefinition(Class<?> clazz, Method method) {
        try {
            TaskDefinition annotation = method.getAnnotation(TaskDefinition.class);
            
            // 构建任务名称
            String taskName = annotation.taskName();
            if (taskName.isEmpty()) {
                taskName = clazz.getSimpleName() + "." + method.getName();
            }
            
            // 构建任务定义实体
            com.etrx.kb.domain.TaskDefinition entity = new com.etrx.kb.domain.TaskDefinition();
            entity.setTaskName(taskName);
            entity.setTaskType(annotation.taskType());
            entity.setClassPath(clazz.getName());
            entity.setMethodName(method.getName());
            entity.setThreadCount(annotation.threadCount());
            entity.setMaxExecutionCount(annotation.maxExecutionCount());
            entity.setMaxRetryCount(annotation.maxRetryCount());
            entity.setDescription(annotation.description());
            entity.setStatus(com.etrx.kb.domain.TaskDefinition.Status.ENABLED);
            entity.setCreateTime(LocalDateTime.now());
            entity.setUpdateTime(LocalDateTime.now());
            
            // 保存或更新任务定义
            taskDefinitionService.saveOrUpdateTaskDefinition(entity);
            
            log.info("注册任务定义: {}", taskName);
            
        } catch (Exception e) {
            log.error("处理任务定义失败: {}.{}", clazz.getName(), method.getName(), e);
        }
    }
} 